# ASR服务400005错误修复方案

## 问题描述
ASR服务返回错误码400005，错误信息为"音频时长超出范围"，导致语音识别功能无法正常工作。

## 问题分析
根据日志分析，问题可能的原因包括：
1. 音频数据过长，超过ASR服务的时长限制
2. 音频数据过短，不足以进行有效识别
3. 音频数据为空或格式不正确
4. 音频数据大小超过服务限制

## 修复方案

### 1. 添加音频数据验证功能
在 `app/service/asr.py` 中添加了以下功能：

#### 音频时长计算函数
```python
def calculate_audio_duration(audio_data, sample_rate, channels=1, sample_width=2):
    """
    计算音频数据的时长（秒）
    :param audio_data: 音频数据（字节格式）
    :param sample_rate: 采样率
    :param channels: 声道数，默认1
    :param sample_width: 采样宽度（字节数），默认2（16位）
    :return: 音频时长（秒）
    """
```

#### 音频数据验证函数
```python
def validate_audio_data(audio_data, audio_param):
    """
    验证音频数据是否符合要求
    :param audio_data: 音频数据
    :param audio_param: 音频参数
    :return: (is_valid, error_message)
    """
```

### 2. 配置参数
添加了可配置的限制参数：
- `ASR_MIN_DURATION = 0.1`  # 最小时长100毫秒
- `ASR_MAX_DURATION = 60.0`  # 最大时长60秒
- `ASR_MAX_SIZE = 10 * 1024 * 1024`  # 最大音频数据大小10MB

### 3. 改进的错误处理
- 在调用ASR API之前进行音频数据验证
- 添加了详细的错误日志记录
- 改进了异常处理，包括超时和网络错误
- 对ASR服务返回的400005错误进行特殊处理

### 4. 主要修改内容

#### call_asr_api函数改进：
1. **预验证**：在发送请求前验证音频数据
2. **详细日志**：记录音频大小和时长信息
3. **超时处理**：添加30秒请求超时
4. **错误分类**：区分不同类型的错误并返回相应信息
5. **响应解析**：改进JSON响应解析的错误处理

#### 验证逻辑：
- 检查音频数据是否为空
- 计算并验证音频时长是否在合理范围内
- 检查音频数据大小是否超过限制
- 提供详细的错误信息帮助调试

## 修复效果

### 修复前的问题：
- ASR服务直接返回400005错误
- 缺乏音频数据的预验证
- 错误信息不够详细
- 无法区分不同类型的音频问题

### 修复后的改进：
- 在发送请求前进行音频数据验证
- 提供详细的错误信息和建议
- 记录音频参数便于调试
- 改进的异常处理和超时控制
- 更好的日志记录

## 使用说明

### 音频数据要求：
- 最小时长：0.1秒（100毫秒）
- 最大时长：60秒
- 最大数据大小：10MB
- 支持的格式：PCM等

### 错误信息示例：
- "音频数据为空"
- "音频时长过短: 0.03秒，最小需要0.1秒"
- "音频时长过长: 65.2秒，最大允许60.0秒"
- "音频数据过大: 12582912字节，最大允许10485760字节"

## 测试验证

可以使用提供的测试脚本 `test_asr_fix.py` 来验证修复效果：

```bash
python3 test_asr_fix.py
```

测试包括：
1. 空音频数据测试
2. 过短音频数据测试
3. 正常音频数据测试
4. 过长音频数据测试
5. 过大音频数据测试
6. 不同采样率的时长计算测试

## 注意事项

1. **配置调整**：可以根据实际需求调整 `ASR_MIN_DURATION`、`ASR_MAX_DURATION` 和 `ASR_MAX_SIZE` 参数
2. **性能影响**：音频验证会增加少量计算开销，但可以避免无效的API调用
3. **兼容性**：修改保持了原有API接口的兼容性
4. **监控**：建议监控修复后的错误率和响应时间

## 后续优化建议

1. 将配置参数移到配置文件中，支持动态调整
2. 添加音频格式检测和转换功能
3. 实现音频数据的自动分片处理（对于过长音频）
4. 添加更多的音频质量检测（如静音检测）
5. 考虑实现音频数据的压缩优化
