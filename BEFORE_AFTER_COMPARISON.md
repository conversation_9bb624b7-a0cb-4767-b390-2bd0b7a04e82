# ASR修复前后对比

## 🔴 修复前的问题

### 原始代码逻辑：
```python
def call_asr_api(session_id, audio_param, audio_data):
    # 直接发送请求，没有验证
    response = requests.post(url, headers=headers, data=audio_data)
    
    # 简单的错误处理
    if response.status_code == 200:
        return response_json
    else:
        return {'errcode': 500, 'errmsg': 'failed'}
```

### 问题现象：
- ❌ ASR服务返回400005错误："音频时长超出范围"
- ❌ 没有音频数据预验证
- ❌ 错误信息不够详细
- ❌ 无法区分问题原因
- ❌ 浪费API调用资源

## 🟢 修复后的改进

### 新增功能：

#### 1. 音频时长计算
```python
def calculate_audio_duration(audio_data, sample_rate, channels=1, sample_width=2):
    """计算音频数据的时长（秒）"""
    if not audio_data:
        return 0
    
    bytes_per_sample = channels * sample_width
    total_samples = len(audio_data) // bytes_per_sample
    duration = total_samples / sample_rate
    return duration
```

#### 2. 音频数据验证
```python
def validate_audio_data(audio_data, audio_param):
    """验证音频数据是否符合要求"""
    if not audio_data:
        return False, "音频数据为空"
    
    duration = calculate_audio_duration(audio_data, sample_rate)
    
    if duration < ASR_MIN_DURATION:
        return False, f"音频时长过短: {duration:.2f}秒，最小需要{ASR_MIN_DURATION}秒"
    
    if duration > ASR_MAX_DURATION:
        return False, f"音频时长过长: {duration:.2f}秒，最大允许{ASR_MAX_DURATION}秒"
    
    if len(audio_data) > ASR_MAX_SIZE:
        return False, f"音频数据过大: {len(audio_data)}字节，最大允许{ASR_MAX_SIZE}字节"
    
    return True, None
```

#### 3. 改进的主函数
```python
def call_asr_api(session_id, audio_param, audio_data):
    # 🆕 预验证音频数据
    is_valid, error_msg = validate_audio_data(audio_data, audio_param)
    if not is_valid:
        logger.warning(f"call_asr_api validation failed session_id={session_id} error={error_msg}")
        return {
            'errcode': 400005,
            'errmsg': error_msg,
            'data': None
        }
    
    # 🆕 记录音频信息
    duration = calculate_audio_duration(audio_data, sample_rate)
    logger.info(f"call_asr_api session_id={session_id} audio_size={len(audio_data)} duration={duration:.2f}s")
    
    # 🆕 添加超时处理
    try:
        response = requests.post(url, headers=headers, data=audio_data, timeout=30)
    except requests.exceptions.Timeout:
        return {'errcode': 500, 'errmsg': 'ASR服务请求超时'}
    except requests.exceptions.RequestException as e:
        return {'errcode': 500, 'errmsg': f'ASR服务请求失败: {str(e)}'}
    
    # 🆕 改进的响应处理
    if response.status_code == 200:
        try:
            response_json = json.loads(response.content.decode('utf-8'))
            
            # 🆕 特殊处理400005错误
            if response_json.get('errcode') == 400005:
                logger.warning(f"call_asr_api ASR service returned 400005 session_id={session_id}")
                return {
                    'errcode': 400005,
                    'errmsg': '音频时长超出ASR服务范围，请检查音频数据',
                    'data': None
                }
            
            return response_json
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            return {'errcode': 500, 'errmsg': f'ASR服务响应解析失败: {str(e)}'}
```

## 📊 测试用例验证

### 测试场景1：空音频数据
- **输入**: `audio_data = b''`
- **修复前**: 发送到ASR服务 → 400005错误
- **修复后**: 预验证拦截 → 返回"音频数据为空"

### 测试场景2：过短音频（50字节 ≈ 0.0016秒）
- **输入**: `audio_data = b'\x00' * 50`
- **修复前**: 发送到ASR服务 → 400005错误
- **修复后**: 预验证拦截 → 返回"音频时长过短: 0.00秒，最小需要0.1秒"

### 测试场景3：正常音频（32000字节 = 1秒）
- **输入**: `audio_data = b'\x00' * 32000`
- **修复前**: 可能正常或400005错误
- **修复后**: 通过验证 → 正常发送到ASR服务

### 测试场景4：过长音频（2000000字节 ≈ 62.5秒）
- **输入**: `audio_data = b'\x00' * 2000000`
- **修复前**: 发送到ASR服务 → 400005错误
- **修复后**: 预验证拦截 → 返回"音频时长过长: 62.5秒，最大允许60.0秒"

### 测试场景5：过大文件（11MB）
- **输入**: `audio_data = b'\x00' * (11 * 1024 * 1024)`
- **修复前**: 发送到ASR服务 → 可能超时或错误
- **修复后**: 预验证拦截 → 返回"音频数据过大: 11534336字节，最大允许10485760字节"

## 🎯 修复效果总结

### ✅ 解决的问题：
1. **减少400005错误**: 通过预验证避免发送无效音频数据
2. **提供详细错误信息**: 明确告知问题原因和建议
3. **节省API调用**: 避免无效请求，提升性能
4. **改善调试体验**: 详细的日志记录便于问题排查
5. **增强稳定性**: 添加超时和异常处理

### 📈 预期改进：
- 400005错误率显著下降
- API调用成功率提升
- 用户体验改善
- 系统稳定性增强

### 🔧 配置参数：
```python
ASR_MIN_DURATION = 0.1   # 最小时长100毫秒（可调整）
ASR_MAX_DURATION = 60.0  # 最大时长60秒（可调整）
ASR_MAX_SIZE = 10 * 1024 * 1024  # 最大10MB（可调整）
```

## 🚀 部署建议

1. **备份原文件**: `cp app/service/asr.py app/service/asr.py.backup`
2. **部署新代码**: 已完成修改
3. **重启服务**: 根据部署方式重启
4. **监控效果**: 观察400005错误率变化
5. **调整参数**: 根据实际需求调整限制参数

修复已完成，可以直接部署使用！
