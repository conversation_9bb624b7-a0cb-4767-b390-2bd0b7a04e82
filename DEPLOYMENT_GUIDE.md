# ASR修复部署指南

## 修复内容概述
本次修复主要解决ASR服务返回400005"音频时长超出范围"错误的问题。

## 修改的文件
- `app/service/asr.py` - 主要修复文件
- `test_asr_fix.py` - 测试脚本（可选）
- `ASR_FIX_README.md` - 修复说明文档

## 部署步骤

### 1. 备份原文件
```bash
# 备份原始的asr.py文件
cp app/service/asr.py app/service/asr.py.backup.$(date +%Y%m%d_%H%M%S)
```

### 2. 验证修复
```bash
# 检查语法错误
python3 -m py_compile app/service/asr.py

# 运行测试（可选）
python3 test_asr_fix.py
```

### 3. 重启服务
```bash
# 根据你的部署方式重启应用
# 例如：
# systemctl restart xiaomei-weiwei
# 或者
# supervisorctl restart xiaomei-weiwei
# 或者
# docker restart xiaomei-weiwei
```

### 4. 验证部署
- 检查应用日志确保启动正常
- 测试ASR接口功能
- 监控错误率是否下降

## 关键改进点

### 1. 音频数据预验证
- 在调用ASR API前验证音频数据
- 检查音频时长、大小等参数
- 提供详细的错误信息

### 2. 改进的错误处理
- 区分不同类型的错误
- 添加超时处理
- 更好的异常捕获和日志记录

### 3. 配置参数
```python
ASR_MIN_DURATION = 0.1   # 最小时长100毫秒
ASR_MAX_DURATION = 60.0  # 最大时长60秒  
ASR_MAX_SIZE = 10 * 1024 * 1024  # 最大10MB
```

## 监控建议

### 1. 关键指标
- ASR API调用成功率
- 400005错误的发生频率
- 音频验证失败的原因分布
- API响应时间

### 2. 日志关键字
- `call_asr_api validation failed` - 音频验证失败
- `call_asr_api timeout` - 请求超时
- `ASR service returned 400005` - ASR服务返回400005错误
- `audio_size=` 和 `duration=` - 音频参数信息

### 3. 告警设置
- 400005错误率超过阈值时告警
- ASR API成功率低于阈值时告警
- 音频验证失败率异常时告警

## 回滚方案
如果出现问题，可以快速回滚：

```bash
# 恢复备份文件
cp app/service/asr.py.backup.YYYYMMDD_HHMMSS app/service/asr.py

# 重启服务
# systemctl restart xiaomei-weiwei
```

## 配置调优
根据实际使用情况，可能需要调整以下参数：

```python
# 在 app/service/asr.py 中调整
ASR_MIN_DURATION = 0.05  # 如果需要支持更短的音频
ASR_MAX_DURATION = 120.0 # 如果需要支持更长的音频
ASR_MAX_SIZE = 20 * 1024 * 1024  # 如果需要支持更大的音频文件
```

## 常见问题

### Q: 修复后仍然出现400005错误怎么办？
A: 检查日志中的详细错误信息，可能是：
- 音频数据本身有问题
- ASR服务端的其他限制
- 网络或认证问题

### Q: 音频验证过于严格怎么办？
A: 可以调整配置参数，放宽时长或大小限制

### Q: 性能是否会受到影响？
A: 音频验证的计算开销很小，但可以避免无效的API调用，总体上应该会提升性能

## 联系方式
如有问题，请联系开发团队或查看相关文档。
