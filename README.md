# 微微AI助手架构设计文档

## 目录
- [1. 整体架构](#1-整体架构)
- [2. 核心业务流程](#2-核心业务流程)
- [3. 关键组件详解](#3-关键组件详解)
- [4. 数据存储](#4-数据存储)
- [5. 错误处理与日志](#5-错误处理与日志)
- [6. 部署与监控](#6-部署与监控)
- [7. 待优化项目](#7-待优化项目)
- [8. 环境要求](#8-环境要求)
- [9. 目录结构索引](#9-目录结构索引)
- [10. 测试相关信息](#10-测试相关信息)
- [11. 工作流程图](#11-工作流程图)
- [12. 商家状态检查](#12-商家状态检查)

## 1. 整体架构

系统被拆分为以下核心模块：

### 1.1 Web服务 (app.py)
- Flask Web框架
- API路由管理
- 请求处理
- 流式响应
- 错误处理

**主要路由接口**:
- `/weiwei/chat` - 主聊天接口
- `/chat` - AI测试接口
- `/weiwei/stream/asr` - 语音识别接口
- `/weiwei/stream/get-tts-response` - 获取TTS响应接口
- `/weiwei/tts/stream-synthesis` - 文本转语音接口
- `/weiwei/intent` - 用户意图接口
- `/weiwei/vex/*` - 向量数据库相关接口
- `/weiwei/es-*` - ES数据库相关接口
- `/weiwei/order/*` - 订单相关接口
- `/weiwei/share/*` - 广场内容分享接口
- `/monitor/alive` - 服务健康检查接口

### 1.2 配置管理 (configs/)
- 基础配置 (`app/configs/config.py`)
  - 应用基础配置
  - 应用密钥
  - 常量定义
- Lion配置 (`app/configs/lion_config.py`)
  - 模型配置
  - 远程配置获取
  - 配置更新机制
- 本地开发配置 (`app/configs/local_config.py`)
  - 本地环境变量
  - 开发测试参数

### 1.3 AI客户端模块 (service/ai_client.py)
- AI通信核心功能 (`app/service/ai_client.py`)
- 模型端点:
  - AIGC_URL: 'https://aigc.sankuai.com/v1/openai/native/chat/completions'
  - TIKTOKEN_URL: "https://aigc.sankuai.com/v1/openai/tiktoken"
  - CLAUDE_URL: "https://aigc.sankuai.com/v1/claude/aws/v1/messages"
  - CLAUDE_STREAM_URL: "https://aigc.sankuai.com/v1/claude/stream/v1/messages"

主要类和方法：
```python
@timeit
def send_to_ai(data: Dict[str, Any] = None) -> Optional[requests.Response]:
    """
    发送请求到AI模型
    
    Args:
        data: 请求数据，格式如下：
        {
            "model": model,
            "messages": messages,
            "stream": generator is not None,
            "temperature": 0.0,
            "max_tokens": 4000,
        }
        
    Returns:
        requests.Response: 响应对象，失败返回None
    """
    
@timeit
def get_token_num(messages: List[Dict[str, Any]], function_call: Optional[List[Dict[str, Any]]] = None) -> int:
    """
    获取消息的token数量
    
    Args:
        messages: 消息列表
        function_call: 函数调用信息
        
    Returns:
        int: token数量，失败返回-1
    """
```

### 1.4 意图处理模块 (service/rag.py)
- 用户输入意图识别 (`app/service/rag.py`)
- RAG检索增强
- 知识库集成
- 上下文处理
- 处理流程:
  1. 接收用户消息
  2. 向量化查询
  3. 相似度匹配
  4. 整合向量检索结果
  5. 生成增强回复

主要类和方法：
```python
def get_user_intent(messages: list[dict['role':str, 'content':str]], user_id: int, memory_content: str, mis_id: str = "") -> list|None:
    """获取用户意图
    
    Args:
        messages: 消息列表
        user_id: 用户ID
        memory_content: 记忆内容
        mis_id: 美团内部ID

    Returns:
        list: 意图列表
    """

def extract_intent_from_str(intent:str)->list:
    """从字符串中提取意图列表
    
    Args:
        intent: 意图字符串（通常是JSON格式）
        
    Returns:
        list: 意图列表
    """
```

### 1.5 函数调用模块 (service/COT/)
- Chain-of-Thought 实现 (`app/service/COT/core.py`)
- 函数调用框架 (`app/service/COT/function_box.py`)
- 代理注册与管理 (`app/service/COT/registry_pool.py`)
- 方法调度系统

主要类和方法：
```python
def function_calling(function_name:str, method_name:str="call", **kwargs):
    """
    调用注册的函数
    
    Args:
        function_name: 函数名称
        method_name: 方法名称，默认为 "call"
        **kwargs: 传递给函数的参数
        
    Returns:
        函数调用结果，如果调用失败则返回 None
    """

class AgentMeta(ABCMeta):
    """元类，用于检查子类是否定义了所需的属性并自动注册"""
    
class Registry:
    """函数注册中心，用于管理所有已注册的代理类"""
    
    @classmethod
    def register(cls, function_name:str):
        """注册函数装饰器"""
        
    @classmethod
    def get_class(cls, function_name:str) -> Optional[Type]:
        """获取注册的类"""
        
    @classmethod
    def call_method(cls, function_name:str, method_name:str, **kwargs):
        """调用注册类的指定方法"""
```

### 1.6 语音处理模块
- 文本转语音 (`app/service/tts.py`)
  - 流式合成
  - PCM数据处理
  - 音频格式转换
- 语音识别 (`app/service/asr.py`)
  - 音频参数解析
  - ASR API调用
  - 结果处理
- 音频流处理
  - 缓存管理
  - 流式传输

主要类和方法:
```python
class TtsClient:
    """TTS客户端单例类"""
    
    @staticmethod
    def get_instance():
        """获取TTS客户端实例"""
        
    def stream_synthesize_with_raw_pcm(self, text, voiceName, speed, volume, sampleRate, audioFormat, enableExtraVolume, extendParams):
        """生成流式PCM数据"""

def call_asr_api(session_id, audio_param, audio_data):
    """调用ASR API进行语音识别"""
```

### 1.7 数据库接口
- MySQL客户端 (`app/mysql/sql_client.py`)
  - 连接池管理
  - SQL执行
  - 事务控制
- ES客户端 (`app/es/es_client.py` 和 `app/service/ESmemory/es_memory_client.py`)
  - 索引管理
  - 文档CRUD
  - 搜索功能
  - 记忆和画像存储
- 向量数据库 (`app/vex/vex_thrift.py`)
  - 向量添加/删除
  - 相似度搜索
  - 特征提取
- Redis客户端 (`app/utils/squirrel.py`)
  - 缓存操作
  - 键值管理
  - 过期控制

### 1.8 工具模块 (utils/)
- 线程生成器 (`app/utils/threaded_generator.py`)
  - 线程安全队列
  - 流数据生成
- 日志工具 (`app/utils/logger.py`)
  - 跟踪ID管理
  - 日志格式化
  - 日志级别控制
- 工具函数 (`app/utils/utils.py`)
  - 时间函数装饰器
  - 通用工具函数
- 数据提取 (`app/utils/extract.py`)
  - 结构化数据提取
  - 模式匹配
  - 文本解析
- 文件操作 (`app/utils/file_operator.py`)
  - 文件读写
  - 路径管理
  - 数据加载

### 1.9 业务服务模块 (service/)
- 订单服务 (`app/service/order.py`)
  - 订单查询
  - 订单分析
  - 订单管理
- 食材分析 (`app/service/ingredient.py`)
  - 食材匹配
  - 数据更新
  - ES索引管理
- 推荐服务 (`app/service/recommend.py`)
  - 话题推荐
  - 偏好分析
- 记忆系统 (`app/service/ESmemory/`)
  - 用户记忆存储
  - 记忆事实管理
  - 重要性排序
  - 记忆内容生成
- 聊天处理 (`app/service/chat.py`, `app/service/chat_agent.py`)
  - 对话管理
  - 上下文控制
- 上下文管理 (`app/service/context.py`)
  - 历史记录管理
  - 上下文增强
- 用户画像 (`app/service/ESmemory/es_portrait_service.py`)
  - 用户数据分析
  - 特征提取
  - 偏好建模
- 内容分享 (`app/service/share_to_plaza.py`)
  - 内容发布到广场
  - 分享管理
  - 内容格式化
- 时间范围解析 (`app/service/time_range_str.py`)
  - 自然语言时间解析
  - 时间区间格式化
- 外卖搜索 (`app/service/waimai_search_api/`)
  - 美食搜索
  - 餐厅推荐
  - 菜品查询
- 节假日管理 (`app/service/holiday/`)
  - 节假日识别
  - 特殊日期处理

### 1.10 商家服务模块 (service/merchant_service.py)
- 商家状态检查 (`app/service/merchant_service.py`)
  - 单个商家检查
  - 批量商家状态查询
  - 商家状态过滤
- 商家API接口 (`app/api/merchant_api.py`)
  - 状态查询接口
  - 商家可用性检查
  - 批量状态查询

主要类和方法:
```python
class MerchantService:
    """商家信息服务，用于获取商家开店状态"""
    
    def check_merchant_status(self, merchant_id):
        """检查商家是否开店"""
        
    def is_merchant_available(self, merchant_id):
        """快速检查商家是否可用（已上线且正在营业）"""
        
    def batch_check_merchant_status(self, merchant_ids):
        """批量检查多个商家状态"""
        
    def filter_available_merchants(self, merchants, id_field="poi_id"):
        """过滤出可用的商家（已上线且正在营业）"""
```

## 2. 核心业务流程

### 2.1 对话流程
1. 用户发送消息到 `/weiwei/chat` 端点 (`app/app.py`)
2. 系统解析用户请求 (`xiaomei_callback_chat` 函数)
   - 获取用户ID和认证信息 (`token`, `misId`)
   - 提取聊天历史 (`messages`)
   - 解析用户特征和记忆内容 (`user_feature`, `memory_content`, `personal_reply`)
3. 创建线程生成器处理流式响应 (`ThreadedGenerator`)
4. 启动函数调用处理 (`Thread(target=function_calling)`)
   - 使用 `ChatAgent` 和 `OrderAdivisorAgent` 工具
   - 根据模型类型处理请求 (`model_type`)
5. 返回流式响应给客户端 (`Response(stream_with_context(generator))`)

详细请求流程:
```
用户请求 → Flask路由 → 参数解析 → 用户ID映射 → 创建线程生成器 → 异步函数调用 → 流式响应
```

### 2.2 意图识别流程
1. 接收用户消息 (`get_user_intent` 函数)
2. 提取用户画像信息 (`get_user_portrait`)
3. 使用AI模型分析用户意图 (`get_intent_from_ai`)
4. 解析返回的JSON结构 (`extract_intent_from_str`)
5. 返回意图列表

模型调用流程:
```
用户输入 → 构建提示词 → 调用AI模型 → 解析JSON响应 → 提取意图列表 → 返回意图
```

### 2.3 订单分析流程
1. 获取用户订单数据 (`query_user_orders_by_user_id`)
2. 分析订单历史和偏好 (`analyze_orders`)
3. 根据分析结果提供建议 (`generate_recommendations`)
4. 格式化回复内容

### 2.4 TTS流程 (`app/service/tts.py`)
1. 接收文本内容 (`stream_synthesis` 函数)
2. 预处理文本 (`pre_handle_text` 函数)
   - 去除圆括号中的链接
   - 中文冒号转换为换行符
   - 保留中文字符、数字和指定标点符号
3. 调用TTS API生成音频 (`TtsClient.stream_synthesize_with_raw_pcm`)
4. 流式返回音频数据给客户端 (`generate` 生成器函数)

### 2.5 ASR流程 (`app/service/asr.py`)
1. 接收音频数据 (`asr_service` 函数)
2. 提取音频参数 (`audio_param`)
3. 调用ASR API转换为文本 (`call_asr_api`)
4. 返回识别结果

### 2.6 内容分享流程 (`app/service/share_to_plaza.py`)
1. 用户触发分享操作
2. 系统提取对话内容和上下文 (`extract_share_content`)
3. 格式化分享内容 (`format_content_for_sharing`)
4. 调用广场API发布内容 (`publish_to_plaza`)
5. 返回分享结果和链接

### 2.7 记忆处理流程 (`app/service/ESmemory/`)
1. 用户消息处理过程中自动提取重要事实
2. 通过记忆服务更新用户记忆 (`begin_memory_service`)
   - 使用模型生成记忆内容
   - 将记忆内容存储到ES数据库
   - 区分短期记忆和长期记忆
3. 定期删除过期的短期记忆 (`delete_outdated_memories`)
4. 根据一定时间间隔汇总短期记忆为长期记忆 (`update_long_memory`)
5. 在对话生成时检索用户记忆以增强AI回复
6. 并行执行用户画像更新 (`begin_portrait_service`)

详细记忆流程:
```
对话处理 → 记忆生成 → ES存储 → 短期记忆管理 → 长期记忆更新 → 记忆检索 → AI提示增强
```

## 3. 关键组件详解

### 3.1 函数调用系统 (`app/service/COT/function_box.py` & `app/service/COT/registry_pool.py`)
```python
class BaseAgent(metaclass=AgentMeta):
    """所有代理的基类"""
    
    @staticmethod
    @abstractmethod
    def call(**kwargs):
        """必须实现的调用方法"""
        pass

class ChatAgent(BaseAgent):###注意它使用的是weiwei.system_prompt
    """聊天代理"""
    function_name = "Chat"
    function_description = "用于处理用户的聊天请求"
    parameters = [
        {"name": "query", "type": "string", "description": "用户查询", "required": True},
        {"name": "history", "type": "array", "description": "聊天历史", "required": False},
        {"name": "context", "type": "object", "description": "上下文信息", "required": False},
    ]
    
    @staticmethod
    def call(**kwargs):
        # 处理聊天逻辑
        pass

class OrderAdivisorAgent(BaseAgent):###注意它使用的是weiwei.model_name.weiwei.system_prompt
    """订单顾问代理"""
    function_name = "OrderAdvisor"
    function_description = "用于提供订单和餐饮推荐"
    parameters = [
        {"name": "user_id", "type": "string", "description": "用户ID", "required": True},
        {"name": "query_type", "type": "string", "description": "查询类型", "required": True},
        {"name": "location", "type": "object", "description": "位置信息", "required": False},
    ]
    
    @staticmethod
    def call(**kwargs):
        # 订单推荐逻辑
        pass
```

### 3.2 流式响应处理 (`app/utils/threaded_generator.py`)
```python
class ThreadedGenerator:
    """线程安全的生成器，用于流式响应"""
    
    def __init__(self):
        self.queue = Queue()
        
    def send(self, data):
        """发送数据到队列"""
        self.queue.put(data)
        
    def close(self):
        """关闭生成器"""
        self.queue.put(StopIteration)
        
    def __iter__(self):
        """迭代器接口"""
        return self
        
    def __next__(self):
        """获取下一个数据项"""
        item = self.queue.get()
        if item is StopIteration:
            raise StopIteration
        return item
```

使用示例:
```python
# 创建生成器
generator = ThreadedGenerator()

# 启动后台处理线程
Thread(target=process_function, args=(generator,)).start()

# 返回流式响应
return Response(stream_with_context(generator), mimetype='text/event-stream')

# 在处理函数中
def process_function(generator):
    # 处理数据并发送
    generator.send("数据块1")
    generator.send("数据块2")
    # 完成后关闭
    generator.close()
```

### 3.3 RAG实现 (`app/service/rag.py` & `app/vex/vex_thrift.py`)
RAG (Retrieval-Augmented Generation) 系统通过向量数据库检索相关内容，增强AI响应：

1. 文本向量化
   - 使用模型将文本转换为向量
   - 存储在向量数据库中 (`vex.add`)
   
2. 相似度搜索
   - 将用户查询向量化
   - 在向量库中搜索相似内容 (`vex.search`)
   - 按相似度排序
   
3. 上下文增强
   - 将检索到的内容融入提示中
   - 增强AI的背景知识
   
4. 生成高质量回复
   - 模型根据增强后的上下文生成回复
   - 输出结构化的、知识丰富的回答

### 3.4 用户画像系统 (`app/service/ESmemory/es_portrait_service.py`)
```python
def get_portrait_by_id(user_id:str) -> str:
    """
    给定user id（也就是mis id）获取并格式化对应用户的画像
    
    Args:
        user_id: 用户ID
        
    Returns:
        str: 格式化后的用户画像数据
    """
    
def upsert_portrait(index_name:str, user_id:str, portrait_content:dict, doc_id:str) -> dict:
    """
    上传画像
    
    Args:
        index_name: ES索引名称
        user_id: 用户ID
        portrait_content: 画像内容
        doc_id: 文档ID
        
    Returns:
        dict: 上传结果
    """
    
def integrate_portrait(portrait_content:dict, portrait_content_from_db:dict) -> dict:
    """
    将新的画像内容按格式整合到旧画像内容中
    
    Args:
        portrait_content: 新的画像内容
        portrait_content_from_db: 旧画像内容
        
    Returns:
        dict: 整合后的画像内容
    """
    
def begin_portrait_service(mis_id: str, history: list[dict], function_name: str, **kwargs) -> str:
    """
    启动画像服务的主入口
    
    Args:
        mis_id: 用户ID
        history: 对话历史
        function_name: 函数名称
        
    Returns:
        str: 处理结果
    """
```

### 3.5 时间范围解析 (`app/service/time_range_str.py`)
```python
def parse_time_range(text: str) -> Tuple[datetime, datetime]:
    """
    从自然语言文本中解析时间范围
    
    Args:
        text: 包含时间表达的文本，如"最近一周"、"上个月"等
        
    Returns:
        Tuple[datetime, datetime]: 开始时间和结束时间
    """
    
def format_date_range(start_time: datetime, end_time: datetime, format_str: str = "%Y-%m-%d") -> Tuple[str, str]:
    """
    格式化日期范围为字符串
    
    Args:
        start_time: 开始时间
        end_time: 结束时间
        format_str: 日期格式化模板
        
    Returns:
        Tuple[str, str]: 格式化后的开始日期和结束日期字符串
    """
```

### 3.6 记忆系统 (`app/service/ESmemory/`)
记忆系统用于存储和管理用户对话中的重要事实，为AI提供长期记忆能力。系统使用ES数据库存储记忆，并提供记忆的生成、检索、整合和更新功能。

#### 3.6.1 记忆服务核心 (`app/service/ESmemory/es_memory_service.py`)
```python
def get_last_updated_time(index_name:str, user_id:str):
    """
    获取用户记忆的最后更新时间
    
    Args:
        index_name: ES索引名称
        user_id: 用户ID
        
    Returns:
        str: 最后更新时间
    """
    
def begin_memory_service(mis_id: str, history: list[dict['content':str]], user_orders: list[dict], token: str, model_type: str, success: bool, function_name: str, **kwargs) -> list[str]:
    """
    记忆服务逻辑，首先生成记忆并上传，然后删除过时的短期记忆，然后执行记忆总结更新逻辑：短期记忆汇总为长期记忆。
    
    Args:
        mis_id: 用户ID
        history: 对话历史
        user_orders: 用户订单
        token: 认证令牌
        model_type: 模型类型
        success: 是否成功
        function_name: 函数名称
        
    Returns:
        list[str]: 生成的记忆内容
    """
```

#### 3.6.2 记忆更新 (`app/service/ESmemory/es_memory_update.py`)
```python
def delete_outdated_memories(index_name:str, user_id:str):
    """
    删除过期的短期记忆
    
    Args:
        index_name: ES索引名称
        user_id: 用户ID
    """
    
def update_long_memory(index_name:str, user_id:str):
    """
    更新用户的长期记忆
    
    Args:
        index_name: ES索引名称
        user_id: 用户ID
    """
    
def search_update_memory(index_name:str, topic:str, size:int, k:int, user_id:str, memory_type:str="short"):
    """
    搜索可用于更新的记忆
    
    Args:
        index_name: ES索引名称
        topic: 记忆主题
        size: 返回结果数量
        k: 候选数量
        user_id: 用户ID
        memory_type: 记忆类型
        
    Returns:
        list: 记忆列表
    """
    
def integrate_memories(new_memory:str, memories:list, user_id:str):
    """
    整合新记忆和现有记忆
    
    Args:
        new_memory: 新记忆
        memories: 现有记忆列表
        user_id: 用户ID
        
    Returns:
        list: 整合结果
    """
```

#### 3.6.3 记忆上传 (`app/service/ESmemory/es_memory_upsert.py`)
```python
def upsert_memory(index_name:str, user_id:str, memory_content:str, memory_type:str="short", topic:str=None):
    """
    上传或更新记忆
    
    Args:
        index_name: ES索引名称
        user_id: 用户ID
        memory_content: 记忆内容
        memory_type: 记忆类型
        topic: 记忆主题
        
    Returns:
        dict: 上传结果
    """
```

#### 3.6.4 ES客户端 (`app/service/ESmemory/es_memory_client.py`)
```python
class ESClient:
    """ES客户端，提供对ES的基本操作"""
    
    def __init__(self, es_server_url: str):
        """初始化ES客户端"""
        pass
        
    def search(self, index: str, body: dict) -> dict:
        """搜索ES文档"""
        pass
    
    def create(self, index: str, id: str, body: dict) -> dict:
        """创建ES文档"""
        pass
    
    def update(self, index: str, id: str, body: dict) -> dict:
        """更新ES文档"""
        pass
    
    def delete(self, index: str, id: str) -> dict:
        """删除ES文档"""
        pass
```

#### 3.6.5 记忆初始化 (`app/service/ESmemory/es_memory_initialization.py`)
```python
def initialize_memory(index_name:str, mis_id:str, user_orders:list[dict]):
    """
    由用户历史订单生成初始化的长期记忆
    
    Args:
        index_name: ES索引名称
        mis_id: 用户ID
        user_orders: 用户历史订单
    """
```

该模块负责用户首次使用系统时的记忆初始化：
- 检查用户是否已有长期记忆
- 基于用户历史订单生成初始化的长期记忆
- 使用AI模型分析用户订单习惯，生成用户偏好相关的长期记忆
- 通过upsert_memory将初始化的长期记忆存储到ES

#### 3.6.6 记忆召回处理 (`app/service/ESmemory/es_memory_callback.py`)
```python
def callback_memory(index_name:str, user_input:str, user_id:str, long_count:int, short_count:int) -> list:
    """
    召回记忆，根据用户输入分别返回对应要求数量的长短期记忆
    
    Args:
        index_name: ES索引名称
        user_input: 用户输入
        user_id: 用户ID
        long_count: 长期记忆数量
        short_count: 短期记忆数量
        
    Returns:
        list: 排序后的记忆列表
    """
```

该模块负责在对话过程中检索用户记忆：
- 基于用户输入进行向量相似度搜索
- 分别检索长期记忆和短期记忆
- 应用时间衰减因子，使较新的记忆具有更高权重
- 应用记忆类型权重，一般长期记忆权重更高
- 根据相似度、时间和类型权重对记忆进行排序
- 返回最相关的记忆列表用于增强AI回复

## 4. 数据存储

### 4.1 MySQL数据库 (`app/mysql/`)

系统使用MySQL数据库存储持久化数据，主要包括以下内容：

#### 4.1.1 MySQL架构设计
- 基于SQLAlchemy ORM框架构建，提供对象关系映射功能
- 使用zebraproxyclient连接MySQL数据库，提供连接池管理
- 实现了数据库连接的自动回收机制，确保长时间运行时的连接有效性
- 支持SQL查询、事务处理和批量操作

#### 4.1.2 数据库连接管理
- 通过`make_engine()`函数创建SQLAlchemy连接池
- 设置连接参数(timeout、charset等)和连接池属性
- 使用`pool_recycle=3600`确保连接定期回收，避免长时间运行时连接失效

#### 4.1.3 主要表结构

##### 向量记录表 (`vector_record`)
- 存储向量ID与文本映射关系，用于向量检索功能
- 主要字段：id、global_id、origin_content、content_md5、status等
- 提供global_id和content_md5索引，支持高效查询

##### 食材分析表 (`ingredient_analysis`)
- 存储食材信息和分析数据，用于食材查询和分析功能
- 主要字段：id、ingredient、analysis、status等
- 提供ingredient索引，支持按食材名称快速查询

#### 4.1.4 数据库操作接口

系统提供了一组基础操作接口，封装了数据库交互的常见操作：

- `insert_return_id(sql)`: 执行插入操作并返回新插入记录的ID
- `select_one(sql)`: 执行查询并返回单条记录
- `select_many(sql)`: 执行查询并返回多条记录
- `update(sql)`: 执行更新操作并返回结果
- `execute(sql)`: 直接执行原生SQL语句并返回结果

所有接口都实现了自动的连接管理，保证连接在使用后被正确归还到连接池。

#### 4.1.5 实体操作接口

基于基础操作接口，系统实现了一系列针对特定业务实体的高级操作：

##### 向量记录操作
- `insert_vector`: 插入向量记录，实现重复检查避免冗余
- `select_vector_by_id`: 根据全局ID查询向量记录
- `select_vector_by_content`: 通过内容查询向量记录(使用MD5索引优化查询)
- `delete_vector`: 逻辑删除向量记录(将状态标记为无效)

##### 食材分析操作
- `upsert_data`: 更新或插入食材分析数据
- `select_ingredient`: 按食材名称查询
- `search_ingredient`: 批量搜索食材信息，支持JSON格式意图输入

#### 4.1.6 Web API SQL执行接口

系统提供了通过HTTP接口直接执行SQL查询的功能，方便调试和数据管理：

```python
@app.route('/weiwei/sql/execute')
def execute_sql():
    """
    提供直接执行SQL查询的API接口
    
    通过HTTP请求执行SQL语句并返回结果。客户端可以发送包含SQL查询的JSON请求，
    系统会执行该查询并返回结果。
    """
    set_trace_id(request)
    sql = request.json.get('sql')
    return response.success(execute(sql))
```

**安全注意事项**:
- 该接口应在有适当访问控制的环境中使用，以防止SQL注入攻击
- 生产环境中应限制该接口的访问权限
- 应避免执行修改数据的SQL语句(INSERT/UPDATE/DELETE等)

#### 4.1.7 事务处理
系统支持数据库事务处理，确保多条SQL语句的原子性：
- 自动管理事务，操作完成后自动提交
- 提供事务回滚机制，确保数据一致性
- 支持在单个事务中执行多条SQL语句

#### 4.1.8 应用场景

##### 向量记录存储
- 存储向量ID与原始文本的映射关系
- 支持基于内容的向量检索
- 用于实现RAG（检索增强生成）功能

##### 食材分析数据管理
- 存储食材的详细分析信息
- 支持按食材名称快速查询
- 提供批量食材查询功能，用于食谱分析

##### 调试与管理
- 通过SQL执行接口直接查询数据
- 分析系统数据状态
- 执行数据修复操作

#### 4.1.9 连接池优化
- 定期回收长时间未使用的连接
- 设置连接超时参数，防止长时间阻塞
- 自动关闭和归还连接，确保资源有效利用


https://km.sankuai.com/collabpage/2634732402  mysql操作手册


### 4.2 Elasticsearch (`app/es/es_client.py`)
- 食材分析数据 (`ingredient_analysis` 索引)
- 用户画像数据 (`user_portrait` 索引)
- 意图关键词索引 (`intent_keyword` 索引)
- 分享内容索引 (`shared_content` 索引)

ES主要操作:
```python
def search(index_name: str, query: dict) -> dict:
    """在指定索引中搜索"""

def upsert_data(index_name: str, doc_id: str, doc: dict) -> dict:
    """更新或插入文档"""

def delete_doc(index_name: str, doc_id: str) -> dict:
    """删除文档"""
```

### 4.3 向量数据库 (`app/vex/vex_thrift.py`)
- 向量检索
  - 文本相似度搜索
  - Top-K检索
- 相似度计算
- 知识库存储

向量操作接口:
```python
def add(text: str) -> dict:
    """添加文本到向量库"""

def search(text: str, topk: int = 5) -> list:
    """搜索相似文本"""

def delete(global_id: int) -> object:
    """删除向量"""

def get_feature(global_ids: list) -> list:
    """获取向量特征"""
```

### 4.4 Redis缓存 (`app/utils/squirrel.py`)
- 会话数据缓存
- 用户ID映射缓存 (`mis2user` 键空间)
- TTS音频数据缓存 (`tts_pcm_data` 键空间)
- 分享链接缓存 (`share_links` 键空间)

Redis接口:
```python
class RedisClient:
    """Redis客户端单例类"""
    
    @staticmethod
    def get_instance():
        """获取Redis客户端实例"""
        
    def get_client(self):
        """获取Redis连接"""
```

## 5. 错误处理与日志

### 5.1 全局错误处理 (`app/app.py`)
- 异常捕获与记录
```python
try:
    # 业务逻辑
except Exception as e:
    import traceback
    error_trace = traceback.format_exc()
    logger.error(f"Error processing request: {str(e)}\nStack trace:\n{error_trace}")
    return response.fail(500, "Internal server error", str(e))
```

- 错误回复生成 (`app/web/response.py`)
```python
def fail(code=400, message="", data=None):
    """生成错误响应"""
```

- 降级策略 (当主模型失败时切换到备用模型)

### 5.2 日志系统 (`app/utils/logger.py`)
```python
logger.info(f"received request data: {request_data}")
logger.error(f"Error processing request: {str(e)}\nStack trace:\n{error_trace}")
```

- 结构化日志
  - 定义日志格式 (`LOG_FORMAT`)
  - 添加上下文信息
- 追踪ID关联
  - 设置追踪ID (`set_trace_id`)
  - 获取追踪ID (`get_trace_id`)
  - 追踪变量 (`trace_id_var`)
- 日志级别控制
  - 动态调整日志级别
  - 处理器配置 (`show_handlers`)

## 6. 部署与监控

### 6.1 部署配置 (`app/app.py:main()`)
```python
def main():
    parser = argparse.ArgumentParser(description='服务启动程序')
    parser.add_argument('--level', type=str, default='DEBUG',
                       help='日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)')
    parser.add_argument('--logfile', type=str, default="None",
                        help='日志文件路径')
    parser.add_argument('--local', action='store_true',
                        help='是否本地测试')
    args = parser.parse_args()

    # 配置日志
    if args.logfile != "None":
        logger.add(args.logfile, rotation="100MB", level="INFO",
           format=LOG_FORMAT)
    show_handlers(logger, args)
    
    # 启动服务
    app.run(host="0.0.0.0", port=8080, debug=False)
    app.config['JSON_AS_ASCII'] = False
```

### 6.2 性能监控
- 请求响应时间 (使用 `@timeit` 装饰器)
- 错误率统计
- 资源使用监控
- 用户交互追踪
- 函数调用性能分析

### 6.3 健康检查 (`app/app.py`)
```python
@app.route('/monitor/alive')
def health():
    return "alive"
```

## 7. 待优化项目

### 7.1 功能优化
- 意图识别精度提升
  - 扩展意图类型
  - 提高识别准确率
- 多轮对话优化
  - 上下文管理改进
  - 连贯性提升
- 推荐算法改进
  - 个性化推荐增强
  - 实时更新机制
- 内容分享体验优化
  - 优化分享流程
  - 增强分享内容格式化
- 节假日特色体验
  - 节日特色对话
  - 节日专属推荐

### 7.2 性能优化
- 缓存策略优化
  - 多级缓存
  - 智能过期策略
- 异步处理优化
  - 非阻塞IO
  - 协程支持
- 资源使用优化
  - 内存管理
  - 连接池优化
- 分布式部署支持
  - 负载均衡
  - 水平扩展能力

### 7.3 架构优化
- 服务拆分
  - 微服务架构
  - 服务边界清晰化
- 中间件抽象
  - 统一接口设计
  - 通用组件复用
- 配置中心设计
  - 动态配置更新
  - 多环境支持
- 监控系统增强
  - 全链路追踪
  - 业务指标监控

## 8. 环境要求

### 8.1 依赖
- Flask - Web框架
- Requests - HTTP客户端
- Redis - 缓存服务
- MySQL - 关系型数据库
- Elasticsearch - 搜索引擎
- Cat客户端 - 监控系统

详细依赖见 `requirements.txt`:
```
flask>=2.0.0
requests>=2.25.0
redis>=4.0.0
mysql-connector-python>=8.0.0
elasticsearch>=7.0.0
pycat>=1.0.0
loguru>=0.5.0
thrift>=0.16.0
```

### 8.2 系统要求
- Python 3.7+
- 运行内存 8GB+
- 存储空间 20GB+
- 网络连接到内部服务
  - AI模型服务
  - TTS/ASR服务
  - 配置中心
  - 广场分享服务

## 9. 目录结构索引

```
app/
├── app.py                      # 主应用入口和API路由
├── __init__.py                 # 包初始化
├── analyze/                    # 分析模块
├── configs/                    # 配置文件
│   ├── __init__.py
│   ├── appenv                  # 环境配置
│   ├── config.py               # 基础配置
│   ├── lion_config.py          # Lion配置客户端
│   └── local_config.py         # 本地开发配置
├── es/                         # Elasticsearch相关
├── img/                        # 图像资源
├── mysql/                      # MySQL数据库
│   ├── __init__.py
│   ├── entity/                 # 数据实体定义
│   └── sql_client.py           # SQL客户端
├── rerank/                     # 重排序模块
├── redis/                      # Redis相关
├── service/                    # 核心服务模块
│   ├── __init__.py
│   ├── COT/                    # Chain-of-Thought模块
│   │   ├── __init__.py
│   │   ├── core.py             # COT核心实现
│   │   ├── function_box.py     # 函数调用框架
│   │   └── registry_pool.py    # 代理注册中心
│   ├── holiday/                # 节假日处理模块
│   ├── waimai_search_api/      # 外卖搜索API
│   ├── ai_client.py            # AI客户端
│   ├── asr.py                  # 语音识别
│   ├── chat.py                 # 聊天处理
│   ├── chat_agent.py           # 聊天代理
│   ├── context.py              # 上下文管理
│   ├── ingredient.py           # 食材分析
│   ├── insight.py              # 洞察分析
│   ├── order.py                # 订单服务
│   ├── rag.py                  # RAG实现
│   ├── recommend.py            # 推荐服务
│   ├── request_aigc.py         # AIGC请求
│   ├── share_to_plaza.py       # 广场分享
│   ├── time_range_str.py       # 时间范围处理
│   ├── tts.py                  # 文本转语音
│   └── user_portrait.py        # 用户画像
├── takeout/                    # 外卖相关模块
├── utils/                      # 工具类
│   ├── __init__.py
│   ├── extract.py              # 数据提取
│   ├── file_operator.py        # 文件操作
│   ├── lion_connection.py      # Lion连接
│   ├── logger.py               # 日志工具
│   ├── meituan_link.py         # 美团链接处理
│   ├── squirrel.py             # Redis客户端
│   ├── threaded_generator.py   # 线程生成器
│   ├── user_info/              # 用户信息
│   └── utils.py                # 通用工具
├── vex/                        # 向量数据库
│   └── vex_thrift.py           # Thrift接口
└── web/                        # Web相关
    ├── __init__.py
    └── response.py             # 响应格式化
```

## 10. 测试相关信息
本地测试连接：
```bash
conda create -n envname
conda activate envname
cd xiaomei-weiwei
pip install -r requirements.txt
cd app
python app.py

```

如果遇到内部库不能安装的情况，应当使用美团内部库下载依赖：

```bash
pip install -r requirements.txt -i http://pypi.sankuai.com/simple --trusted-host pypi.sankuai.com --break-system-packages
```

如果发现下载完依赖包之后仍然缺少某些dylib文件的情况，尝试运行以下命令：
```bash
brew tap inf/inf  ssh://*******************/~zhangzheng51/homebrew-inf-lib.git

brew install inf/inf/cat
```


在软件上（如apifox）建立post链接，地址为 http://127.0.0.1:8080/weiwei/chat
Body里选json，填入格式如下的输入并建立连接：
```json
{
   "systemParams":{  
       "messages":[
           {
               "role":"user",
               "content":"我想吃点好的"
           }
       ],
       "fileList":[
           {
               "fileUrl":"s3"
           }
       ]
   },
   "model":"deepseek-r1-friday",
   "memoryContent":"这时一条测试的记忆信息，用户貌似说喜欢吃辣",
   "activateCot":"False",
   "bizParams":{  
     "key1":"value1",
     "key2":{"key3":"value3"}
   },
   "traceId":"",  
   "userInfo":{  
       "userId":"your mis id here",
       "conversationId":"",
       "agentId":""
   },
   "accessToken":"your access token here, 在https://xiaomeiai.meituan.com/xiaomeivv/chat中聊天，打开开发者工具，在network类下的chat信息里可以查询到"
   
}
```
即可在终端或软件中看到输出的信息。

线上测试链接：http://10.101.222.64:8080/weiwei/chat

灰度测试链接：http://10.252.91.72:8080/weiwei/chat

线上webapp使用地址：https://xiaomeiai.meituan.com/xiaomeivv/chat
**进行一段聊天后**，进入开发者工具，找到network类下的chat信息，找到其中的M-traceid属性并复制
访问http://logcenter.data.sankuai.com/
找到xiaomei-weiwei对应的日志记录，在顶端检索栏输入你的M-traceid即可检索日志（如果id是负数，注意用英文双引号括住id），就能看到所有的活动日志。

test环境机器显示日志使用命令：
```bash
tail -f /opt/logs/com.sankuai.friday.xiaomei.weiwei/app.log
```



## 11. 工作流程图

以下是系统的主要工作流程图，展示从用户输入到系统响应的完整过程：

```
                      +-------------------+
                      |   用户输入(文本)   |
                      +-------------------+
                              |
                              v
                      +-------------------+
                      |  请求处理(app.py)  |
                      +-------------------+
                              |
                              v
                      +-------------------+
                      |    文本输入处理    |
                      +-------------------+
                              |
                              v
                      +-----------------------------+
                      | 用户意图识别(service/rag.py) |
                      | 通过AI分析提取用户意图        |
                      +-----------------------------+
                              |
                              v
                      +-----------------------------+
                      |    Chain-of-Thought处理流程 |
                      |  (service/COT/function_box.py) |
                      +-----------------------------+
                              |
                              v
                      +-----------------------------+
                      |  根据意图选择合适的Agent处理  |
                      |  (ChatAgent/OrderAgent)     |
                      +-----------------------------+
                             /         \
                            /           \
                           v             v
                  +----------+     +------------+
                  | 普通对话  |     | 订单查询/推荐 |
                  | ChatAgent |     | OrderAgent  |
                  +----------+     +------------+
                          \           /
                           \         /
                            v       v
                      +-----------------------------+
                      |  AI模型调用(service/ai_client.py) |
                      |  生成回复内容               |
                      +-----------------------------+
                              |
                              v
                      +-----------------------------+
                      |      返回文本响应           |
                      +-----------------------------+
```

### 处理流程详细说明

1. **输入处理**:
   - 系统接收用户文本输入
   - 通过app.py中的路由进行处理

2. **意图识别**:
   - 使用RAG(检索增强生成)提取用户意图
   - 通过AI分析，将用户意图转化为结构化数据

3. **Chain-of-Thought处理**:
   - MainAgent接收输入，决定处理流程
   - 根据意图类型分配到对应的专门Agent

4. **分支处理**:
   - **ChatAgent**: 处理普通对话和问答
   - **OrderAgent**: 处理订单查询、推荐相关意图

5. **AI响应生成**:
   - 调用大语言模型生成回复内容
   - 根据用户信息和上下文个性化响应

6. **响应返回**:
   - 返回文本处理结果给用户

7. **响应返回**:
   - 返回文本处理结果给用户
```

## 12. 商家状态检查

系统集成了商家状态检查功能，确保推荐给用户的商家都是正常营业的。该功能可以完全过滤掉已下线或休息中的商家，只向用户推荐可用的商家。

### 12.1 功能说明

- 在推荐商家给用户前，系统会自动检查商家的营业状态和上线状态
- 仅推荐"已上线"且"营业中"的商家，完全过滤掉休息中或已下线的商家
- 支持单个商家检查和批量商家过滤，具有内存缓存机制避免重复请求
- 默认不显示不可用商家的信息，保持界面简洁

### 12.2 实现流程

1. **商家识别**：系统从用户查询或AI回复中提取商家名称
2. **商家信息查询**：通过ES数据库查询获取商家ID
3. **状态检查**：调用商家服务API检查商家状态（支持处理嵌套数据结构）
4. **结果过滤**：仅展示状态正常的商家，不可用商家完全不展示
5. **缓存机制**：缓存商家状态查询结果，减少重复请求

### 12.3 涉及的文件

- `app/service/merchant_service.py` - 商家状态检查核心功能
- `app/utils/meituan_link.py` - 即时推荐商品时的商家过滤
- `app/analyze/main.py` - 分析订单数据时的商家过滤
- `app/api/merchant_api.py` - 商家状态查询API接口
- `app/service/context.py` - 商家信息查询功能

### 12.4 使用方式

#### 单个商家检查
```python
from service.merchant_service import MerchantService

# 创建服务实例
merchant_service = MerchantService()

# 检查单个商家
status = merchant_service.check_merchant_status(merchant_id)

# 判断商家是否可用
is_available = status.get("is_open", False) and status.get("is_online", False)
```

#### 批量商家过滤
```python
# 批量过滤可用商家
available_merchants = merchant_service.filter_available_merchants(merchants_list)

# 获取不可用商家信息（调试用，正常流程不使用）
unavailable_merchants = merchant_service.get_unavailable_merchants_info(merchants_list)
```

### 12.5 状态码说明

商家状态码含义：
- **营业状态(status)**:
  - 1: 营业中
  - 3: 休息中
  - 其他: 未知状态

- **上线状态(valid)**:
  - 0: 已下线
  - 1: 已上线
  - 2: 上单中
  - 3: 审核通过可上线

### 12.6 特殊场景处理

- **无可用商家**：当所有商家都不可用时，分析结果会提示"附近暂无可推荐商家"
- **商家数据嵌套**：能够正确处理API返回的嵌套在"data"字段中的商家信息
- **缓存机制**：避免短时间内多次查询同一商家状态，优化性能