import os
import json
import random
from typing import Optional, Tuple, List, Dict, Any, Union
from datetime import datetime
from utils.file_operator import load_orders_data
from utils.logger import logger
from configs.config import HOT_MERCHANTS_ORDERS_FILE_PATH, OTHER_MERCHANTS_ORDERS_FILE_PATH
from concurrent.futures import ThreadPoolExecutor
from service.merchant_service import MerchantService

# 创建商家服务实例
merchant_service = MerchantService()

# 1.14新逻辑中用到，用于分析用户历史订单，生成价格区间
def analyze_user_price_ranges(token, user_id: int, keywords: Optional[List[str]] = None, user_orders=None) -> Tuple[Optional[Tuple[float, float]], Optional[Tuple[float, float]]]:
    """从用户历史订单分析合理的价格区间
    
    Args:
        user_id (int): 用户ID
        keywords (list): 搜索关键词，用于过滤相关商品
        
    Returns:
        tuple: (order_price_range, item_price_range)，每个都是(min_price, max_price)格式
    """
    try:
        orders = user_orders
        # logger.info(f"analyze_user_price_ranges::orders = {orders}")
        if not orders:
            return None, None
            
        order_prices = []
        item_prices = []
        
        for poi_orders in orders.values():
            for order in poi_orders['details']:
                # 收集订单总价
                if 'total_price' in order and order['total_price']:
                    order_prices.append(float(order['total_price']))
                
                # 收集商品价格
                items = order.get('items', [])
                if isinstance(items, list):
                    for item in items:
                        if isinstance(item, dict):
                            # 如果有关键词，只收集相关商品的价格
                            item_name = item.get('food_name', '')
                            if keywords and not any(kw in item_name for kw in keywords):
                                continue
                            
                            if 'price' in item and item['price']:
                                item_prices.append(float(item['price']))
        
        if not order_prices and not item_prices:
            return None, None
            
        # 计算价格区间，考虑合理性
        order_price_range = None
        if order_prices:
            min_order = min(order_prices)
            max_order = max(order_prices)
            order_price_range = (
                max(20, min_order * 0.8),  # 最小不低于20元
                min(2000, max_order * 1.2)  # 最大不超过2000元
            )
            
        item_price_range = None
        if item_prices:
            min_item = min(item_prices)
            max_item = max(item_prices)
            item_price_range = (
                max(5, min_item * 0.8),  # 最小不低于5元
                min(1000, max_item * 1.2)  # 最大不超过1000元
            )
            
        return order_price_range, item_price_range
        
    except Exception as e:
        logger.info(f"分析用户价格区间时出错: {str(e)}")
        return None, None

# 1.14新逻辑中用到，用于分析订单数据，生成分析报告
def analyze_orders(orders: List[Dict[str, Any]]) -> Optional[str]:
    """分析订单数据，生成分析报告"""
    try:
        total_orders = len(orders)
        if total_orders == 0:
            return None
            
        # 统计商家信息
        merchant_stats = {}
        merchant_poi_ids = {}  # 存储商家ID用于后续查询状态
        
        for order in orders:
            merchant_name = order.get('merchant_name', '未知商家')
            
            # 提取商家ID
            poi_id = order.get('poi_id')
            if poi_id:
                merchant_poi_ids[merchant_name] = poi_id
                
            if merchant_name not in merchant_stats:
                merchant_stats[merchant_name] = {
                    'order_count': 0,
                    'total_amount': 0,
                    'avg_rating': 0,
                    'ratings': [],
                    'items': set(),  # 用于存储商品名称
                    'poi_id': poi_id  # 存储商家ID
                }
            
            stats = merchant_stats[merchant_name]
            stats['order_count'] += order.get('order_count', 0)
            stats['total_amount'] += float(order.get('average_total_amount', 0))
            rating = float(order.get('rating', 0))
            if rating > 0:
                stats['ratings'].append(rating)
                
            # 收集商品信息
            items = order.get('items', [])
            for item in items:
                if isinstance(item, dict):
                    item_name = item.get('name', '')
                    item_price = item.get('price', 0)
                    if item_name and item_price:
                        stats['items'].add(f"{item_name}(¥{item_price})")
        
        # 计算平均评分
        for stats in merchant_stats.values():
            if stats['ratings']:
                stats['avg_rating'] = sum(stats['ratings']) / len(stats['ratings'])
                stats['items'] = list(stats['items'])  # 转换为列表
        
        # 按订单数量和评分排序商家
        sorted_merchants = sorted(
            merchant_stats.items(),
            key=lambda x: (x[1]['order_count'], x[1]['avg_rating']),
            reverse=True
        )
        
        # 批量检查商家状态并过滤
        logger.info(f"开始检查推荐商家状态，共{len(sorted_merchants)}个商家")
        available_merchants = []
        
        for merchant_name, stats in sorted_merchants:
            poi_id = stats.get('poi_id')
            if not poi_id:
                # 如果没有商家ID，默认不可用
                continue
                
            # 检查商家状态
            status = merchant_service.check_merchant_status(poi_id)
            
            # 将状态信息添加到商家统计中
            stats['status_info'] = status
            
            if status.get('is_open', False) and status.get('is_online', False):
                available_merchants.append((merchant_name, stats))
                logger.info(f"商家[{merchant_name}]({poi_id})状态正常，可以推荐")
            else:
                logger.info(f"商家[{merchant_name}]({poi_id})状态异常，已过滤。状态: {status}")
        
        # 生成分析报告
        report = f"分析了{total_orders}个相关订单"
        
        # 只添加可用商家
        if available_merchants:
            report += "，为您推荐以下商家：\n\n"
            for merchant_name, stats in available_merchants[:5]:  # 只显示前5个商家
                try:
                    avg_price = stats['total_amount'] / (stats['order_count'])
                except Exception as e:
                    logger.info(f"计算平均价格时出错: {str(e)}")
                    avg_price = 1
                rating_text = f"评分{stats['avg_rating']:.1f}" if stats['ratings'] else "暂无评分"
                
                report += f"**{merchant_name}**\n"
                report += f"- 订单数：{stats['order_count']}，平均消费：¥{avg_price:.0f}，{rating_text}\n"
                if stats['items']:
                    report += f"- 热门商品：{' | '.join(list(stats['items'])[:3])}\n"
                report += "\n"
        else:
            report += "，但附近暂无可推荐商家。您可以稍后再试，或者尝试其他类型的商家。\n\n"
        
        logger.info(f"分析报告: {report}")
        return report
        
    except Exception as e:
        logger.info(f"生成分析报告时出错: {str(e)}")
        return None

# 1.14新逻辑中用到，用于获取订单分析结果，分析调用的顶层函数
def get_order_analysis(token, keywords: Union[str, List[str]], 
                      must_include: Optional[Union[str, List[str]]] = None, 
                      exclude_keywords: Optional[Union[str, List[str]]] = None, 
                      order_price_range: Optional[Tuple[float, float]] = None, 
                      item_price_range: Optional[Tuple[float, float]] = None, 
                      user_id: Optional[int] = None, 
                      feedback: Optional[str] = None,
                      user_orders=None) -> Optional[str]:
    """获取订单分析结果"""
    try:
        logger.info("\n正在分析订单数据...")
        
        # 确保关键词是列表类型
        if isinstance(keywords, str):
            keywords = [keywords]
        if isinstance(must_include, str):
            must_include = [must_include]
        if isinstance(exclude_keywords, str):
            exclude_keywords = [exclude_keywords]
            
        # 如果没有指定价格区间且有用户ID，从历史订单分析价格区间
        if user_id and (order_price_range is None or item_price_range is None):
            analyzed_order_range, analyzed_item_range = analyze_user_price_ranges(token, user_id, keywords, user_orders=user_orders)
            logger.info(f"分析用户价格区间：{analyzed_order_range}, {analyzed_item_range}")
            if order_price_range is None:
                order_price_range = analyzed_order_range
            if item_price_range is None:
                item_price_range = analyzed_item_range
        
        # 读取热门商家订单数据
        hot_merchants_file = HOT_MERCHANTS_ORDERS_FILE_PATH # 'app/weiwei/data/hot_merchants_orders_v6.json'
        hot_merchants_data = []
        if os.path.exists(hot_merchants_file):
            logger.info("正在读取热门商家数据...")
            hot_merchants_data = load_orders_data(hot_merchants_file)
            logger.info(f"已读取热门商家数据：{len(hot_merchants_data)}条")

        # 读取其他商家订单数据
        other_merchants_file = OTHER_MERCHANTS_ORDERS_FILE_PATH # 'app/weiwei/data/other_merchants_orders_v6.json'
        other_merchants_data = []
        if os.path.exists(other_merchants_file):
            logger.info("正在读取其他商家数据...")
            other_merchants_data = load_orders_data(other_merchants_file)
            logger.info(f"已读取其他商家数据：{len(other_merchants_data)}条")

        # 合并所有订单数据
        all_orders = hot_merchants_data + other_merchants_data
        if not all_orders:
            logger.info("未找到任何订单数据")
            return None
            
        logger.info(f"开始筛选{len(all_orders)}条订单...")
        
        # 根据关键词过滤订单
        filtered_orders = []
        for order in all_orders:
            order_text = json.dumps(order, ensure_ascii=False).lower()

            # 检查关键词 - 只要有任意一个关键词匹配即可
            if not any(keyword.lower() in order_text for keyword in keywords):
                continue
                
            # 检查必须包含的词
            if must_include and not all(word.lower() in order_text for word in must_include):
                continue
                
            # 检查排除词
            if exclude_keywords and any(word.lower() in order_text for word in exclude_keywords):
                continue
                
            # 检查订单价格范围
            if order_price_range:
                order_price = float(order.get('total_amount', 0))
                if not (order_price_range[0] <= order_price <= order_price_range[1]):
                    continue
            
            # 检查商品价格范围
            if item_price_range:
                items = order.get('items', [])
                item_prices = [float(item.get('f_price', 0)) for item in items]
                if not any(item_price_range[0] <= price <= item_price_range[1] for price in item_prices):
                    continue
            
            filtered_orders.append(order)
        
        logger.info(f"筛选完成，找到{len(filtered_orders)}条相关订单")
        
        if not filtered_orders:
            return None
            
        # 限制分析的订单数量
        if len(filtered_orders) > 1000:
            logger.info(f"订单数量较多，随机选取1000条进行分析")
            filtered_orders = random.sample(filtered_orders, 1000)
        # 分析订单数据
        return analyze_orders(filtered_orders)
        
    except Exception as e:
        logger.info(f"分析订单数据时出错: {str(e)}")
        return None

# 1.15添加RAG逻辑，用于获取订单分析结果
def get_order_analysis_rag(token, keywords: Union[str, List[str]], 
                      must_include: Optional[Union[str, List[str]]] = None, 
                      exclude_keywords: Optional[Union[str, List[str]]] = None, 
                      order_price_range: Optional[Tuple[float, float]] = None, 
                      item_price_range: Optional[Tuple[float, float]] = None, 
                      user_id: Optional[int] = None, 
                      feedback: Optional[str] = None,
                      user_orders=None) -> Optional[str]:
    """获取订单分析结果"""
    try:
        logger.info("\n正在分析订单数据...")
        
        # 确保关键词是列表类型
        if isinstance(keywords, str):
            keywords = [keywords]
        if isinstance(must_include, str):
            must_include = [must_include]

        # 目前似乎exclude在这种情况下没法使用
        if isinstance(exclude_keywords, str):
            exclude_keywords = [exclude_keywords]
            
        # 如果没有指定价格区间且有用户ID，从历史订单分析价格区间
        if user_id and (order_price_range is None or item_price_range is None):
            analyzed_order_range, analyzed_item_range = analyze_user_price_ranges(token, user_id, keywords, user_orders=user_orders)
            logger.info(f"分析用户价格区间：{analyzed_order_range}, {analyzed_item_range}")
            if order_price_range is None:
                order_price_range = analyzed_order_range
            if item_price_range is None:
                item_price_range = analyzed_item_range
        logger.info(f"keywords: {keywords}, must_include: {must_include}")
        # food_knowledge_base = knowledge_base[0]
        # insight_base = knowledge_base[1]

        # 使用线程池并行执行
        # with ThreadPoolExecutor(max_workers=2) as executor:
            # future_food = executor.submit(food_knowledge_base.get_food_knowledge, ','.join(keywords + must_include), top_k=int(500), max_chars=int(1e5))
            # future_insight = executor.submit(insight_base.get_food_knowledge, ','.join(keywords + must_include), top_k=int(500), max_chars=int(1e5))

        filtered_orders = ["1月1日点了牛肉", "1月2日点了牛肉"]
        insight_orders = ["喜欢吃牛肉"]

        logger.info(f"筛选完成，找到{len(filtered_orders)}条相关订单, 以及{len(insight_orders)}条洞察订单")

        if not filtered_orders:
            return None
            
        # 限制分析的订单数量
        if len(filtered_orders) > 1000:
            logger.info(f"订单数量较多，随机选取1000条进行分析")
            filtered_orders = random.sample(filtered_orders, 1000)

        if len(insight_orders) > 1000:
            logger.info(f"洞察订单数量较多，随机选取1000条进行分析")
            insight_orders = random.sample(insight_orders, 1000)
        # 分析订单数据
        return analyze_orders(filtered_orders) + '\n' + analyze_orders(insight_orders)
        
    except Exception as e:
        logger.info(f"分析订单数据时出错: {str(e)}")
        return None


