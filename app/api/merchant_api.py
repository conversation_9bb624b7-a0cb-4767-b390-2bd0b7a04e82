#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
商家API接口
提供商家状态检测的RESTful API
"""

from flask import Blueprint, request, jsonify
from service.merchant_service import MerchantService
from utils.logger import logger

# 创建蓝图
merchant_api = Blueprint('weiwei/merchant_api', __name__)
merchant_service = MerchantService()

@merchant_api.route('/status', methods=['GET'])
def get_merchant_status():
    """获取商家状态"""
    merchant_id = request.args.get('merchant_id')
    if not merchant_id:
        return jsonify({
            'code': 400,
            'message': '缺少商家ID参数',
            'data': None
        }), 400
    
    try:
        status = merchant_service.check_merchant_status(merchant_id)
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': status
        })
    except Exception as e:
        logger.error(f"获取商家状态时出错: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取商家状态失败: {str(e)}',
            'data': None
        }), 500

@merchant_api.route('/available', methods=['GET'])
def is_merchant_available():
    """检查商家是否可用（已上线且正在营业）"""
    merchant_id = request.args.get('merchant_id')
    if not merchant_id:
        return jsonify({
            'code': 400,
            'message': '缺少商家ID参数',
            'data': None
        }), 400
    
    try:
        available = merchant_service.is_merchant_available(merchant_id)
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': {
                'merchant_id': merchant_id,
                'is_available': available
            }
        })
    except Exception as e:
        logger.error(f"检查商家可用性时出错: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'检查商家可用性失败: {str(e)}',
            'data': None
        }), 500

@merchant_api.route('/batch_status', methods=['POST'])
def get_batch_merchant_status():
    """批量获取商家状态"""
    data = request.get_json()
    if not data or not isinstance(data.get('merchant_ids', []), list):
        return jsonify({
            'code': 400,
            'message': '请求格式错误，需要提供商家ID列表',
            'data': None
        }), 400
    
    merchant_ids = data.get('merchant_ids', [])
    if not merchant_ids:
        return jsonify({
            'code': 400,
            'message': '商家ID列表为空',
            'data': None
        }), 400
    
    try:
        results = {}
        for merchant_id in merchant_ids:
            status = merchant_service.check_merchant_status(merchant_id)
            results[merchant_id] = {
                'is_open': status.get('is_open', False),
                'is_online': status.get('is_online', False),
                'status_text': status.get('status_text', '未知'),
                'valid_text': status.get('valid_text', '未知'),
                'name': status.get('name', '未知商家')
            }
        
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': results
        })
    except Exception as e:
        logger.error(f"批量获取商家状态时出错: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'批量获取商家状态失败: {str(e)}',
            'data': None
        }), 500 