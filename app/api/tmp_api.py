"""
商家API接口
提供商家状态检测的RESTful API
"""
from typing import List, Dict, Any

from flask import Blueprint, request, jsonify
from service.merchant_service import MerchantService
from utils.logger import logger

from es import es_client

# 创建蓝图
tmp_api = Blueprint('weiwei/tmp', __name__)


@tmp_api.route('/get_chat_history', methods=['POST'])
def get_chat_history():
    """获取聊天记录"""
    data = request.get_json()
    mis_id = data.get('mis_id', None)
    user_id = data.get('user_id', None)
    conversation_id = data.get('conversation_id', None)

    if conversation_id:
        query = {
            "query": {
                "term": {
                    "conversationId": conversation_id
                }
            }
        }
        result = es_client.search("combination_history", query)['hits']['hits']
        if result:
            return jsonify({"status": 0, "message": "成功", "data": result})
        else:
            return jsonify({"status": 1, "message": "失败", "data": []})
    else:
        return jsonify({"status": 1, "message": "失败", "data": []})


# @tmp_api.route('/insert_chat_history', methods=['POST'])
# def insert_chat_history():
#     import random
#     import json
#     """插入聊天记录"""
#     data = request.get_json()
#     mis_id = data.get('mis_id', None)
#     user_id = data.get('user_id', None)
#     conversation_id = data.get('conversation_id', None)
#     content = data.get('content', None)
#     if content is None:
#         return jsonify({"status": 1, "message": "失败", "data": []})

#     doc = {
        
#     }
#     if conversation_id:
#         doc["conversationId"] = conversation_id
#         query = {
#             "query": {
#                 "term": {
#                     "conversationId": conversation_id
#                 }
#             }
#         }
#         result = es_client.search("combination_history", query)['hits']['hits']
#         if result:
#             history:List[Dict[str, Any]] = json.loads(result[0]['_source']['content'])
#         else:
#             history = None
        
#         if history:

#     if mis_id:
#         doc["misId"] = mis_id

#     if user_id:
#         doc["userId"] = user_id

#     if conversation_id:
#         update_doc = {
#             "doc_as_upsert": True,
#             "doc": {
#                 "mis_id": mis_id,
#                 "user_id": user_id,
#                 "conversation_id": conversation_id,
#                 "content": data.get('content', None),
#                 "pic_list": data.get('pic_list', None)
#             }
#         }
#         result = es_client.update_doc("combination_history", str(random.randint(1000000000000000000, 9999999999999999999)), update_doc)
#         if result:
#             return jsonify({"status": 0, "message": "成功", "data": result})
#         else:
#             return jsonify({"status": 1, "message": "失败", "data": []})
#     else:
#         return jsonify({"status": 1, "message": "失败", "data": []})

