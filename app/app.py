import re
import time
from flask import Flask, request, jsonify, Response, stream_with_context
from threading import Thread
from pycat import Cat
from datetime import datetime
import requests
import asyncio
import random
from typing import Dict, Any

import vex.vex_thrift as vex
from my_mysql.entity import vector_record as sql
from my_mysql.sql_client import execute
from utils.threaded_generator import ThreadedGenerator
from utils.logger import logger, set_trace_id, get_trace_id, LOG_FORMAT, trace_id_var
from service import recommend, chat, ingredient, asr, order, tts
from utils.user_info.user_client import UserClient
from utils.squirrel import RedisClient, build_category_key
from utils.location_utils import (
    calculate_distance, 
    calculate_user_merchant_distance, 
    batch_calculate_distances,
    get_merchants_by_distance,
    test_distance_calculation
)
from my_mysql.entity import ai_batch_response, batch_job_stop_status, prompt_templates
from web import response
from configs import lion_config, config
from configs.config import APP_KEY, MERCHANT_DATA_URL
from es import es_client
from service.rag import get_user_intent
from configs.lion_config import M<PERSON><PERSON>_CONFIG
from service.ai_client import send_to_ai, stream
import json
import base64
import argparse
import sys
from uuid import uuid4
from service.COT.function_box import function_calling, ChatAgent, OrderAdivisorAgent, ShareAgent, EditAgent, DeleteAgent
from service.user_portrait import cycle_update_portrait
from service.memory.memory_api import memory_api
from service.share_to_plaza import search_plaza_phrase_and_subphrases, test_get_plaza_share, get_all_share_ids, get_plaza_shares_by_ids, update_plaza_share_content, get_user_liked_shares, get_user_disliked_shares, get_user_shared_shares, get_user_info_shares, delete_plaza_share
from service.ConvServices import *      # 为了保持格式的一致性，注意compare对话相关的服务和统计全部写到这两个脚本里
from service.ConvStats import *

from service.user_relationship import follow_user, unfollow_user, get_following, get_followers, update_unread_count, get_unread_counts, clear_unread_count, get_following_shares
# from app.web.interaction_api import bp as interaction_bp
from db.maindb_controler import initialize_index_configs, get_order_num_in_es_database, get_index_configs, db_update, set_index_configs
from service.check.checkOrder import get_recent_order_status
from api.merchant_api import merchant_api
from service.hot_share import get_hot_share_ids
from api.tmp_api import tmp_api
from service.conversation_history import upload_conversation_history, upload_used_ids

from service.admin_service import check_is_admin, check_is_white_list

app = Flask(__name__)
app.register_blueprint(memory_api, url_prefix='/weiwei/memory')
app.register_blueprint(merchant_api, url_prefix='/weiwei/merchant')
app.register_blueprint(tmp_api, url_prefix='/weiwei/tmp')
# app.register_blueprint(interaction_bp, url_prefix='/api/interaction')
PLAZA_INDEX = "user_plaza_shares_test"
USER_RELATIONSHIP_INDEX = "user_relationships_test"

@app.route('/weiwei/chat', methods=['POST'])
def xiaomei_callback_chat():
    try:
        request_data = request.get_json()
        tts_session_id = request_data.get("tts-session-id")
        # 设置trace_id
        set_trace_id(request)
        # 如果请求中有traceId，使用请求中的traceId
        if "traceId" in request_data:
            trace_id_var.set(request_data["traceId"])
        trace_id = get_trace_id()
        logger.info(f"received request data: {request_data}")
        logger.info(f"received request header: {request.headers}")
        messages = request_data['systemParams']['messages']
        misId = request_data['userInfo']["userId"]
        misId_userId_map = lion_config.MIS_USER_ID_MAP
        token = request_data["accessToken"]

        if not token or token == "":
            logger.warning(f"access token is empty, user_id: {misId}")

        try:
            misId_userId_map = json.loads(misId_userId_map)
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析错误: {e}")
            misId_userId_map = {}

        mt_user_id = misId_userId_map.get(misId)
        if not mt_user_id:
            try :
                redis = RedisClient.get_instance().get_client()
                key = build_category_key("mis2user", "m{0}", misId)
                if redis.exists(key):
                    mt_user_id = redis.get(key)
                else:
                    logger.info(f"缓存中不存在misID {misId}，查询美团用户ID")
                    mt_user_id, others = UserClient.get_client().get_mt_userid_by_mis(misId)
                    if mt_user_id:
                        redis.set(key, mt_user_id)

            except Exception as e:
                logger.error(f"获取 mt_user_id 失败: {e}")
                return response.fail(500, "获取 mt_user_id 失败", str(e))

        # 获取新增的字段
        memory_content = request_data.get("memoryContent", "")
        user_feature = request_data.get("userFeature", "")
        personal_reply = request_data.get("personalReply", "")
        model_type = request_data.get("model", "gpt") # 前端获取是否开启深度思考
        location_query = request_data.get("locationQuery", False) # 是否查询商家地址经纬度
        order_count_query = request_data.get("orderCountQuery", False) # 是否查询商家月订单数量
        generator = ThreadedGenerator()
        tools = [
            ChatAgent.json_scheme,
            OrderAdivisorAgent.json_scheme,
            ShareAgent.json_scheme,
            EditAgent.json_scheme,
            DeleteAgent.json_scheme
        ]
        logger.info(f"传给大模型{model_type}的工具 {tools}")
        # token: str, history: list[dict['content':str]], user_id: int, tts_session_id: None,
        #       generator: ThreadedGenerator, trace_id: str, user_feature: str = "", personal_reply: str = "",
        #       memory_content: str = "", mis_id: str = "", tools:list[str]=[], model_type:str = "gpt-4o-mini"
        Thread(target=function_calling, kwargs={"function_name": "Main", "method_name": "call", "token": token, "history": messages, "user_id": mt_user_id, "tts_session_id": tts_session_id, "generator": generator, "trace_id": trace_id,
                                          "user_feature": user_feature, "personal_reply": personal_reply, "memory_content": memory_content, "mis_id": misId, "tools": tools, "model_type": model_type,
                                          "location_query": location_query, "order_count_query": order_count_query}).start()
        return Response(stream_with_context(generator), mimetype='text/event-stream')

    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        logger.error(f"Error processing request: {str(e)}\nStack trace:\n{error_trace}")
        return response.fail(500, "Internal server error", str(e))

@app.route('/weiwei/raw/chat', methods=['POST'])
def ai_chat():
    def stream_and_record(generator, data:Dict[str, Any]) -> None:
        think_content = []
        main_content = []
        conversation_id = data.pop("conversation_id","")
        last_message = data["messages"][-1]["content"]
        user_id = data.pop("mis_id","")
        message_id = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")

        start_time = time.time()
        response = send_to_ai(data, False)
        for line in response.iter_lines():
            if line and line.startswith(b"data: "):
                line = line.decode('utf-8')
                line = line.replace("data: ", "")

                message = {
                    "message_id": message_id,
                    "content": line
                }
                

                if line == "[DONE]":
                    logger.info(f"收到[DONE]，结束流")
                    break

                line_json = json.loads(line)
                reasoning_content = line_json["choices"][0]["delta"].get("reasoning_content", "")
                output_content = line_json["choices"][0]["delta"].get("content", "")
                if reasoning_content:
                    think_content.append(reasoning_content)
                if output_content:
                    main_content.append(output_content)
                generator.send(json.dumps(message, ensure_ascii=False))


        end_time = time.time()
        duration = end_time - start_time

        final_think_content = "".join(think_content).strip()
        final_main_content = "".join(main_content).strip()  

        logger.info(f"think_content: {final_think_content}")
        logger.info(f"main_content: {final_main_content}")
        logger.info(f"time cost: {duration:.2f} seconds")

        final_message = json.dumps({
            "message_id": message_id,
            "think_length":len(final_think_content),
            "main_length":len(final_main_content),
            "total_time_cost": duration,
            "status":"[DONE]"
        }, ensure_ascii=False)
        generator.send(final_message)

        try:
            history_response = upload_conversation_history(conversation_id, new_message={
                "message_id": message_id,
                "reasoning_content": final_think_content,
                "main_content": final_main_content,
                "total_time_cost": duration,
                "user_message": last_message
            }, api_name="rawchat", user_id=user_id)

            if not history_response:
                logger.error(f"上传对话历史到ES失败，对话ID: {conversation_id}")
                
            else:
                logger.info(f"上传对话历史到ES成功，对话ID: {conversation_id}")
                
        except Exception as e:
            logger.error(f"上传对话历史到ES失败，对话ID: {conversation_id}")

        try:
            upload_used_ids(user_id)
        except Exception as e:
            logger.error(f"上传mis_id到ES失败，用户ID: {user_id}")
            

        generator.close()

       ##haha


    request_json = request.get_json()
    data = request_json.get('data', None)
    if data == None:
        return response.fail(400, "message is empty")
    else:
        if data.get("stream", False):
            generator = ThreadedGenerator()
            Thread(target=stream_and_record, kwargs={"generator": generator, "data": data}).start()
            return Response(stream_with_context(generator), mimetype='text/event-stream')
        else:
            return response.success(json.dumps(send_to_ai(data, False).json(), ensure_ascii=False))
        



@app.route("/weiwei/searchByMisId", methods=["POST"])
def search_by_mis_id():
    set_trace_id(request)
    mis_id = request.json.get("misId")
    mt_user_id, others = UserClient.get_client().get_mt_userid_by_mis(mis_id)
    return response.success(mt_user_id)

@app.route('/weiwei/vex/add', methods=['GET', 'POST'])
def route_add_vex():
    set_trace_id(request)
    text:str = request.json.get('text')
    logger.info(f"add text: {text}")
    ret:dict = vex.add(text)
    return jsonify(ret)
    
@app.route('/weiwei/vex/search', methods=['GET', 'POST'])
def route_search_vex():
    set_trace_id(request)
    text = request.json.get('text')
    topk = request.json.get('topk', 5)
    logger.info(f"search text: {text}, topk: {topk}")
    return jsonify(vex.search(text, topk))

@app.route('/weiwei/vex/delete', methods=['GET', 'POST'])
def route_delete_vex():
    set_trace_id(request)
    global_id = int(request.json.get('global_id'))
    ret = vex.delete(global_id).message
    status = sql.delete_vector(global_id)
    if status.rowcount == 0:
        logger.info(f"删除Vex结果到SQL数据库信息内容为{global_id} status: {status.rowcount}, 删除失败")
    return response.success(ret)

@app.route('/weiwei/topic/recommend', methods=['GET'])
def recommend_topic():
    set_trace_id(request)
    return response.success(recommend.topic_recommend())

@app.route("/weiwei/intent", methods=['POST'])
def get_user_intent():
    set_trace_id(request)
    message = request.json.get('message')
    return response.success(get_user_intent(message, -1))

# ============================================
#           以下为ES数据库更新相关接口
# ============================================
@app.route('/weiwei/ingredient/upsert', methods=['POST'])
def upsert_ingredient():
    set_trace_id(request)
    name = request.json.get('name')
    data = request.json.get('data')
    status = request.json.get('status')
    result = ingredient.upsert_ingredient(name, data, status)
    return response.success(result)


@app.route('/weiwei/es-upsert', methods=['POST'])
def casual_es_upsert(): # 自定义插入
    set_trace_id(request)
    index_name = request.json.get('index_name')
    doc_id = request.json.get('doc_id')
    doc = request.json.get('doc', {})
    status = request.json.get("status", 1)
    logger.info(f"开始上传")
    if status == 1:
        result = es_client.upsert_data(index_name, doc_id, doc)
    else:
        result = es_client.delete_doc(index_name, doc_id)

    if result == None:
        return response.fail(f"插入ES的{index_name}出错")

    return response.success(result)


@app.route('/weiwei/es-search', methods=['POST'])
def casual_es_search(): # 自定义查询
    set_trace_id(request)
    index_name = request.json.get('index_name')
    query = request.json.get('query', {})
    result = es_client.search(index_name, query)
    if result == None:
        return response.fail(f"搜索ES的{index_name}出错")
    return response.success(result)


@app.route('/weiwei/ingredient/es-upsert', methods=['POST'])
def upsert_es_ingredient():
    # 使用ES数据库的更新
    set_trace_id(request)
    name = request.json.get('name')
    data = request.json.get('data')
    status = request.json.get('status')
    
    result = ingredient.upsert_es_ingredient(name, data, status)
    return response.success(result)


@app.route('/weiwei/ingredient/es-search', methods=['POST'])
def search_es_ingredient():
    # 查询es数据
    set_trace_id(request)
    method = request.json.get('method')
    if method == 'match':
        name = request.json.get('name')
        result = ingredient.match_ingredient_list(name)
    else:
        ingredient_list = request.json.get('ingredient_list')
        result = ingredient.terms_ingredient_list(ingredient_list)
    return response.success(result)

@app.route('/weiwei/es-init', methods=['POST'])
def initialize_es_index():
    set_trace_id(request)
    try:
        index_name = request.json.get('index')
        initialize_index_configs(index_name)
        return response.success(f"{index_name}索引初始化成功")
    except Exception as e:
        logger.error(f"初始化索引失败: {e}")
        return response.fail(f"初始化索引失败: {e}, 请参考学城文档: https://km.sankuai.com/collabpage/2708859766")

@app.route("/weiwei/get-index-config", methods=['POST'])
def get_index_config():
    set_trace_id(request)
    try:
        index_name = request.json.get('index')
        configs = get_index_configs(index_name)
        return response.success(configs)
    except Exception as e:
        logger.error(f"获取索引配置失败: {e}")
        return response.fail(f"获取索引配置失败: {e}, 请参考学城文档: https://km.sankuai.com/collabpage/2708859766")

@app.route("/weiwei/update-orderdb", methods=['POST'])
def update_orderdb():
    set_trace_id(request)
    try:
        startTime = request.json.get('startTime', "2025-01-01 00:00:00")
        endTime = request.json.get('endTime', "2025-01-01 00:00:00")
        offset = request.json.get('offset', 0)
        limit = request.json.get('limit', 100)
        userIdList = request.json.get('userIdList', [868])
        token = request.headers.get('access-token')
        result = db_update(startTime, endTime, offset, limit, userIdList, token)
        return response.success(result)
    except Exception as e:
        logger.error(f"更新订单数据库失败: {e}")
        return response.fail(f"更新订单数据库失败: {e}, 请参考学城文档: https://km.sankuai.com/collabpage/2708859766")

@app.route("/weiwei/set-index-config", methods=['POST'])
def set_index_config():
    set_trace_id(request)
    try:
        index_name = request.json.get('index')
        configs = request.json.get('configs')
        set_index_configs(index_name, configs)
        return response.success(f"{index_name}索引配置更新成功")
    except Exception as e:
        logger.error(f"更新索引配置失败: {e}")
        return response.fail(f"更新索引配置失败: {e}, 请参考学城文档: https://km.sankuai.com/collabpage/2708859766")


# ==========================================================
#           以下为因子平台相关接口
# ==========================================================


# -------- 因子相关接口 --------
# 获取所有的因子目录
@app.route("/weiwei/factor/get-factor", methods=['POST'])
def get_factors():
    set_trace_id(request)
    try:
        factor_name = request.json.get('factor_name')
        factor_value = request.json.get('factor_value')
        result = get_factors(factor_name, factor_value)
        return response.success(result)
    except Exception as e:
        logger.error(f"获取因子失败: {e}")
        return response.fail(f"获取因子失败: {e}, 请参考学城文档: https://km.sankuai.com/collabpage/2709230364")

@app.route("/weiwei/factor/create", methods=['POST'])
def create_factor():
    set_trace_id(request)
    try:
        factor_name = request.json.get('factor_name')
        factor_value = request.json.get('factor_value')
        result = create_factor(factor_name, factor_value)
        return response.success(result)
    except Exception as e:
        logger.error(f"创建因子失败: {e}")
        return response.fail(f"创建因子失败: {e}, 请参考学城文档: https://km.sankuai.com/collabpage/2709230364")


# -------- 回测相关接口 --------
# 启动回测功能
@app.route("/weiwei/experiment/evaluate", methods=['POST'])
def evaluate():
    set_trace_id(request)
    try:
        factor_name = request.json.get('factor_name')
        factor_value = request.json.get('factor_value')
        result = evaluate(factor_name, factor_value)
        return response.success(result)
    except Exception as e:
        logger.error(f"回测因子失败: {e}")
        return response.fail(f"回测因子失败: {e}, 请参考学城文档: https://km.sankuai.com/collabpage/2709230364")
    

# -------- 在线jupyter相关接口 --------
@app.route("/weiwei/jupyter/start", methods=['POST'])
def start_jupyter():
    set_trace_id(request)
    try:
        result = start_jupyter()
        return response.success(result)
    except Exception as e:
        logger.error(f"启动jupyter失败: {e}")
        return response.fail(f"启动jupyter失败: {e}, 请参考学城文档: https://km.sankuai.com/collabpage/2709230364")



# ==========================================================
#           以下为外卖商家数据相关接口
# ==========================================================

@app.route("/weiwei/merchant/alive", methods=['POST', 'GET'])
def merchant_alive():
    set_trace_id(request)
    return response.success("Merhcnat alive")

@app.route("/weiwei/merchant/get-merchant-info", methods=['POST'])
def get_merchant_info():
    set_trace_id(request)
    merchant_id = request.json.get('merchantId')
    fields_needed = request.json.get('fieldsNeeded')
    headers = {
            "Authorization": "Bearer 1838824241643597850",
            "Content-Type": "application/json",
        }
    try:
        logger.info(f"请求的URL: {MERCHANT_DATA_URL}") # Test: http://10.171.59.195:8080/merchant/search
        result = requests.post(MERCHANT_DATA_URL, headers=headers, json={"merchantId": merchant_id, "fieldsNeeded": fields_needed})
        return response.success(result.text)
    except Exception as e:
        logger.error(f"获取商家信息失败: {e}")
        
        return response.fail(f"获取商家信息失败: {e}, 请参考学城文档: https://km.sankuai.com/collabpage/2709230364")



# ==========================================================
#           以下为发起调研弹窗相关接口
# ==========================================================
@app.route("/weiwei/check/order", methods=['POST', 'GET'])
def check_order_status():
    set_trace_id(request)
    try:
        try:
            request_data = request.get_json()
            logger.info(f"request_data: {request_data}")
            # 设置trace_id
            set_trace_id(request)
            # 如果请求中有traceId，使用请求中的traceId
            if "traceId" in request_data:
                trace_id_var.set(request_data.get("traceId", 1))
            misId = request_data['userInfo']["userId"]
            misId_userId_map = lion_config.MIS_USER_ID_MAP
            # 尝试从请求体获取 token
            token = request_data.get("accessToken", None)
            
            if not token or token == "":
                logger.warning(f"access token is empty, user_id: {misId}")

            try:
                misId_userId_map = json.loads(misId_userId_map)
            except json.JSONDecodeError as e:
                logger.error(f"JSON 解析错误: {e}")
                misId_userId_map = {}

            mt_user_id = misId_userId_map.get(misId)
            if not mt_user_id:
                try :
                    redis = RedisClient.get_instance().get_client()
                    key = build_category_key("mis2user", "m{0}", misId)
                    if redis.exists(key):
                        mt_user_id = redis.get(key)
                    else:
                        logger.info(f"缓存中不存在misID {misId}，查询美团用户ID")
                        mt_user_id, others = UserClient.get_client().get_mt_userid_by_mis(misId)
                        if mt_user_id:
                            redis.set(key, mt_user_id)
                except Exception as e:
                    logger.error(f"获取 mt_user_id 失败: {e}")
                    return response.fail(500, "获取 mt_user_id 失败", str(e))
        except Exception as e:
            logger.error(f"解析参数失败: {e}")
            return response.fail(500, "解析参数失败", str(e))
        content, pic_list = get_recent_order_status(mt_user_id, token)

        result = {
            'status': 0, 'message': '成功', 
            "data":{
                "content": content,
                "pic_list": pic_list
            }
        }
        return jsonify(result)
    except Exception as e:
        logger.error(f"检测最近订单状况失败: {e}")
        return response.fail(f"检测最近订单状况失败: {e}, 请参考学城文档: https://km.sankuai.com/collabpage/2709230364")


# ==========================================================
#           以下为语音相关接口
# ==========================================================

@app.route('/weiwei/stream/asr', methods=['POST'])
def asr_service():
    set_trace_id(request)
    # 获取请求头中的session-id
    session_id = request.headers.get('session-id')

    # 获取请求头中的audio-param并解码
    audio_param_encoded = request.headers.get('audio-param')
    logger.info(f"asr stream header: {audio_param_encoded}")
    audio_param = json.loads(base64.b64decode(audio_param_encoded))

    # 获取请求体中的音频数据
    audio_data = request.data
    result = asr.call_asr_api(session_id, audio_param, audio_data)
    if result['errcode'] == 0:
        return response.success(result['data'])
    else:
        return response.fail(data=result)


@app.route('/weiwei/stream/get-tts-response')
def tts_service():
    set_trace_id(request)
    session_id = request.headers.get('tts-session-id')
    if not session_id:
        return response.fail("tts-session-id is empty") # 参数错误

    def generate_audio_stream():
        pcm_data_dict = {}
        redis_client = RedisClient.get_instance().get_client()
        current_expected_index = 1  # 当前期望的索引
        final_index = 1
        data_count = 0
        max_empty_retries = lion_config.TTS_MAX_RETRY_NUM  # 最大空返回重试次数
        empty_retry_count = 0
        squirrel_key = build_category_key("tts_pcm_data", "t{0}", session_id)
        while True:
            encoded_pcm_data_json = redis_client.rpop(squirrel_key)
            if encoded_pcm_data_json:
                empty_retry_count = 0  # 重置计数器
                try:
                    encoded_pcm_data = json.loads(encoded_pcm_data_json)
                    cur_index = encoded_pcm_data.get('index')
                    pcm_data = None
                    if encoded_pcm_data.get('pcm_data'):
                        pcm_data = base64.b64decode(encoded_pcm_data.get('pcm_data'))
                    if cur_index < 0:
                        final_index = cur_index
                    if pcm_data and not cur_index < 0:
                        pcm_data_dict[int(cur_index)] = pcm_data
                        data_count += 1
                        # 按顺序返回音频数据
                        while current_expected_index in pcm_data_dict:
                            yield pcm_data_dict.pop(current_expected_index)
                            current_expected_index += 1
                    if data_count == -final_index:
                        break
                except (json.JSONDecodeError, ValueError, KeyError) as e:
                    print(f"Error processing data: {e}")
                    continue
            else:
                empty_retry_count += 1
                if empty_retry_count >= max_empty_retries:
                    break
                time.sleep(lion_config.TTS_TIME_BETWEEN_RETRIEVE)
    try:
        return Response(stream_with_context(generate_audio_stream()), mimetype='audio/mpeg')
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return Response(status=500, response='Internal Server Error')


# ==========================================================    
#           以下为searchagent转发查询接口
# ==========================================================
@app.route("/weiwei/deepsearch/agenticStreamSearch", methods=['POST'])
def agentic_stream_search():
    set_trace_id(request)
    try:
        request_json = request.get_json()
        logger.info(f"agentic stream search request: {request_json}")
        
        # 调用外部接口
        external_api_url = "https://xmww.meituan.com/deepsearch/agenticStreamSearch"
        headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream"  # 明确接受SSE格式
        }
        
        
        def generate():

            think_content = []
            main_content = []
            poi_content = []
            conversation_id = request_json.pop("conversation_id","test_conv_1_by_llf")
            last_message = request_json.get("query","")
            user_id = request_json.pop("mis_id","")
            message_id = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")

            start_time = time.time()


            # 使用stream=True参数进行流式请求
            with requests.post(
                external_api_url,
                json=request_json,
                headers=headers,
                stream=True  # 启用流式传输
            ) as r:
                # 检查状态码
                if r.status_code != 200:
                    logger.error(f"外部API调用失败: {r.status_code}")
                    yield json.dumps({"error": f"外部API调用失败: {r.status_code}"})
                    return
                
                # 逐行传输响应
                for line in r.iter_lines():
                    if line:
                        decoded_line = line.decode('utf-8')
                        logger.debug(f"Streaming line: {decoded_line}")
                        cut_line = decoded_line.replace("data:", "")
                        decoded_json = json.loads(cut_line)

                        data_json = json.dumps({
                            "message_id": message_id,
                            "content": decoded_json
                           }, ensure_ascii=False)
                        yield f"data: {data_json}\n\n"
                        
                        

                        if decoded_json["contentList"][0]["data"]["type"] in ["thinking_title", "thinking"] :
                            think_content.append(decoded_json["contentList"][0]["data"].get("content", ""))
                        elif decoded_json["contentList"][0]["data"]["type"] == "main" :
                            main_content.append(decoded_json["contentList"][0]["data"].get("content", ""))
                        elif decoded_json["contentList"][0]["data"]["type"] in ["poi_description", "poi_info"] :
                            poi_content.append(decoded_json["contentList"][0]["data"].get("content", ""))

                end_time = time.time()
                duration = end_time - start_time

                final_think_content = "".join(think_content).strip()    
                final_main_content = "".join(main_content).strip()
                final_poi_content = "".join(poi_content).strip()

                final_message = json.dumps({
                    "message_id": message_id,
                    "think_length":len(final_think_content),
                    "main_length":len(final_main_content),
                    "poi_length":len(final_poi_content),
                    "total_time_cost": duration,
                    "status":"[DONE]"
                }, ensure_ascii=False)  
                yield f"data: {final_message}\n\n"

                logger.info(f"final_think_content: {final_think_content}")
                logger.info(f"final_main_content: {final_main_content}")
                logger.info(f"final_poi_content: {final_poi_content}")
                logger.info(f"time cost: {duration:.2f} seconds")
            
                # 上传对话历史
                try:
                    history_response = upload_conversation_history(conversation_id, new_message={
                        "message_id": message_id,
                        "reasoning_content": final_think_content,
                        "main_content": final_main_content,
                        "poi_content": final_poi_content,
                        "total_time_cost": duration,
                        "user_message": last_message
                    }, api_name="agentic", user_id=user_id)

                    if not history_response:
                        logger.error(f"上传对话历史到ES失败，对话ID: {conversation_id}")
                        
                    else:
                        logger.info(f"上传对话历史到ES成功，对话ID: {conversation_id}")
                        
                except Exception as e:
                    logger.error(f"上传对话历史到ES失败，对话ID: {conversation_id}")

                try:
                    upload_used_ids(user_id)
                except Exception as e:
                    logger.error(f"上传mis_id到ES失败，用户ID: {user_id}")

        # 返回流式响应
        res = Response(generate(), content_type='text/event-stream')
        res.headers['X-Accel-Buffering'] = 'no'
        res.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        res.headers['Pragma'] = 'no-cache'
        res.headers['Expires'] = '0'
        return res
            
    except Exception as e:
        logger.error(f"agentic stream search error: {e}")
        return response.fail(f"agentic stream search error: {e}")





# ==========================================================
#           以下为其他接口
# ==========================================================

@app.route('/monitor/alive')
def health():
    return "alive"


@app.route('/weiwei/order/query-user-order')
def query_order_by_():
    set_trace_id(request)
    user_id = request.args.get('user_id')
    orders = order.query_user_orders_by_user_id(user_id)
    return response.success(orders)


@app.route('/weiwei/order/remove-user-order')
def remove_order_by_order_id():
    set_trace_id(request)
    user_id = request.args.get('user_id')
    order.remove_orders_by_user_id(user_id)
    return response.success()


@app.route('/weiwei/mis2user/removeCache')
def remove_mis2user_cache():
    set_trace_id(request)
    mis_id = request.args.get('mis_id')
    redis = RedisClient.get_instance().get_client()
    key = build_category_key("mis2user", "m{0}", mis_id)
    redis.delete(key)
    return response.success()


@app.route('/weiwei/mis2user/queryCache')
def query_mis2user_cache():
    set_trace_id(request)
    mis_id = request.args.get('mis_id')
    redis = RedisClient.get_instance().get_client()
    key = build_category_key("mis2user", "m{0}", mis_id)
    mt_user_id = redis.get(key)
    return response.success(mt_user_id)


@app.route('/weiwei/sql/execute')
def execute_sql():
    set_trace_id(request)
    sql = request.json.get('sql')
    return response.success(execute(sql))


def pre_handle_text(text):
    # 去除圆括号中的链接（包括圆括号）
    text = re.sub(r'\([^)]*://[^)]*\)', '', text)
    # 中文冒号转为空格
    text = re.sub(r'：', '\n', text)
    # 然后清除多余的部分，保留中文字符、数字和指定标点符号
    text = re.sub(r'[^\u4e00-\u9fa50-9,.+!?，。！？\s\w]', '', text)
    return text

@app.route('/weiwei/tts/stream-synthesis', methods=['POST'])
def stream_synthesis():
    set_trace_id(request)
    tts_client = tts.TtsClient.get_instance()
    request_json = request.get_json()
    text = request_json.get('text')
    logger.info(f"before pre_handle_text: {text}")
    text = pre_handle_text(text)
    logger.info(f"after pre_handle_text: {text}")
    voiceName = request_json.get('voiceName', "meifanli")
    speed = request_json.get('speed', 50)
    volume = request_json.get('volume', 50)
    sampleRate = request_json.get('sampleRate', 16000)
    audioFormat = request_json.get('audioFormat', "mp3")
    enableExtraVolume = request_json.get('enableExtraVolume', None)
    extendParams = request_json.get('extendParams', None)
    logger.info(f"stream synthesis request: {request_json}")
    # Thread(target=tts_client.stream_synthesize_with_event_stream, args=(text, voiceName, speed, volume, sampleRate, audioFormat,
    #                                                     enableExtraVolume, extendParams, generator)).start()
    # 使用stream_with_context装饰器
    def generate():
        yield from tts_client.stream_synthesize_with_raw_pcm(
            text, voiceName, speed, volume, sampleRate, audioFormat, enableExtraVolume, extendParams
        )
    return Response(stream_with_context(generate()), mimetype='audio/mpeg')


@app.route('/weiwei/vex/get-feature', methods=['POST'])
def get_feature():
    set_trace_id(request)
    global_ids = request.json.get('global_ids')
    return response.success(vex.get_feature(global_ids))

@app.route("/weiwei/model-list", methods=['GET'])
def model_list():
    # 给前端提供支持的模型列表
    set_trace_id(request)
    return response.success(lion_config.lion_client.get_value("weiwei.support_models"))
    
@app.route('/weiwei/share', methods=['GET'])
# GET /weiwei/share?share_id=your_share_id
def share_content():
    """获取分享内容的接口
    
    参数：
        share_id: 分享内容的ID
        master_mis_id: 用户ID
        
    返回：
        成功：返回分享内容
        失败：返回错误信息
    """
    try:
        set_trace_id(request)
        # 从请求参数中获取 share_id
        share_id = request.args.get('share_id')
        master_mis_id = request.args.get('master_mis_id')  # 新增 master_mis_id 参数
        
        # 检查 master_mis_id 和share_id 是否存在
        if not share_id and not master_mis_id:
            return response.fail(400, "缺少必要参数：share_id和master_mis_id")
        
        # 调用函数获取分享内容
        result = test_get_plaza_share(share_id, master_mis_id)
        
        if result.get('code') == 200:
            return response.success(result['data'])
        elif result.get('code') == 404:
            return response.fail(404, "分享内容未找到")
        else:
            return response.fail(500, result.get('message', "获取分享内容失败"))
            
    except Exception as e:
        logger.error(f"获取分享内容时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")
    
@app.route('/weiwei/shareall', methods=['GET'])
def share_all_content():
    try:
        # 获取所有share_ids
        share_ids = get_all_share_ids()
        
        # 获取分享内容列表
        shares = get_plaza_shares_by_ids(share_ids)
        
        # 构造返回数据
        response = {
            "code": 200,
            "message": "success",
            "data": {
                "total": len(shares),
                "shares": shares  # shares已经是列表，直接使用
            }
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"获取分享内容时出错: {str(e)}")
        return jsonify({
            "code": 500,
            "message": "获取分享内容失败",
            "data": {
                "total": 0,
                "shares": []
            }
        })
    
@app.route('/weiwei/sharebypage', methods=['GET'])
def share_page_content():
    """分页获取分享内容的接口
    
    参数：
        offset: 起始位置（默认0）
        batch_size: 获取数量（默认10）
        master_mis_id: 用户ID
        
    返回：
        成功：返回分页的分享内容列表
        失败：返回错误信息
    """
    try:
        # 从请求参数中获取偏移量、批量大小和用户ID
        offset = request.args.get('offset', default=0, type=int)  # 起始位置
        batch_size = request.args.get('batch_size', default=10, type=int)  # 获取数量
        master_mis_id = request.args.get('master_mis_id')  # 用户ID
        
        # 检查 master_mis_id 是否存在
        if not master_mis_id:
            return response.fail(400, "缺少必要参数：master_mis_id")
        
        # 获取所有share_ids
        share_ids = get_all_share_ids()
        
        # 计算实际需要获取的数据范围
        end_idx = min(offset + batch_size, len(share_ids))  # 确保不超过总长度
        
        # 获取当前批次的share_ids
        batch_share_ids = share_ids[offset:end_idx]
        
        # 获取分享内容，传入master_mis_id
        shares = get_plaza_shares_by_ids(batch_share_ids, master_mis_id)
        
        response_data = {
            "code": 200,
            "message": "success",
            "data": {
                "total": len(share_ids),    # 总记录数
                "offset": offset,           # 当前偏移量
                "batch_size": batch_size,   # 批量大小
                "has_more": end_idx < len(share_ids),  # 是否还有更多数据
                "shares": shares            # 分享内容列表
            }
        }
        
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"获取分享内容时出错: {str(e)}")
        return jsonify({
            "code": 500,
            "message": "获取分享内容失败",
            "data": {
                "total": 0,
                "offset": offset,
                "batch_size": batch_size,
                "has_more": False,
                "shares": []
            }
        })

@app.route('/weiwei/share/comment/store', methods=['POST'])
def store_comment():
    """存储评论
    
    参数：
        share_id: 分享ID
        comment_mis_id: 用户ID
        comment_content: 评论内容
        comment_parent_id: 父评论ID（可选）
        
    返回：
        成功：返回成功信息
        失败：返回错误信息
    """
    try:
        set_trace_id(request)
        data = request.get_json()
        share_id = data.get('share_id')
        comment_mis_id = data.get('comment_mis_id')
        comment_content = data.get('comment_content')
        comment_parent_id = data.get('comment_parent_id') #父评论id
        
        if not share_id or not comment_mis_id or not comment_content:
            return response.fail(400, "缺少必要参数")
            
        # 获取当前时间并格式化为ISO 8601格式
        current_time = datetime.now().isoformat()
        
        # 生成唯一评论ID
        comment_id = f"comment_{int(time.time() * 1000)}_{comment_mis_id}"
        
        # 构建评论记录
        comment_record = {
            "comment_id": comment_id,
            "comment_mis_id": comment_mis_id,
            "comment_content": comment_content,
            "comment_parent_id": comment_parent_id,
            "create_time": current_time,  # 使用时间戳而不是字符串
            "is_deleted": False
        }
        
        # 获取现有的分享内容
        share = test_get_plaza_share(share_id)
        if not share:
            return response.fail(404, "分享内容不存在")
            
        # 获取现有的评论记录
        existing_comments = share.get('data', {}).get('comment_record', [])
        if not isinstance(existing_comments, list):
            existing_comments = []
            
        # 添加新评论
        existing_comments.append(comment_record)
        
        # 更新ES中的评论记录
        update_data = {
            "doc": {
                "comment_record": existing_comments,
                "comment_count": len(existing_comments)
            }
        }
        # 调用更新函数
        success = es_client.update_doc(PLAZA_INDEX, share_id, update_data)
        
        if success:
            # 获取原贴主的mis_id
            share_owner_mis_id = share.get('data', {}).get('mis_id_keyword')
            if share_owner_mis_id:
                # 更新原贴主的未读评论数量
                update_success = update_unread_count(share_owner_mis_id, 'comment')
                if update_success:
                    # 构建新评论信息
                    new_comment = {
                        "share_id": share_id,
                        "operator_id": comment_mis_id,
                        "comment_id": comment_id,
                        "comment_content": comment_content,
                        "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    # 更新用户关系文档，添加新评论记录
                    script = {
                        "script": {
                            "source": """
                                if (ctx._source.new_comments == null) {
                                    ctx._source.new_comments = [];
                                }
                                ctx._source.new_comments.add(params.comment)
                            """,
                            "lang": "painless",
                            "params": {
                                "comment": new_comment
                            }
                        }
                    }
                    
                    es_client.update_doc(USER_RELATIONSHIP_INDEX, share_owner_mis_id, script)
                    print(f"记录新评论消息成功：{new_comment}")
            
            return response.success({
                "message": "评论成功"
            })
        else:
            return response.fail(500, "评论存储失败")
            
    except Exception as e:
        logger.error(f"存储评论时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")

@app.route('/weiwei/share/comment/delete', methods=['POST'])
def delete_comment():
    """删除评论
    
    参数：
        share_id: 分享ID
        comment_id: 评论ID
        
    返回：
        成功：返回成功信息
        失败：返回错误信息
    """
    try:
        set_trace_id(request)
        data = request.get_json()
        share_id = data.get('share_id')
        comment_id = data.get('comment_id')
        
        if not share_id or not comment_id:
            return response.fail(400, "缺少必要参数")
        
        # 获取现有的分享内容
        share = test_get_plaza_share(share_id)
        if not share:
            return response.fail(404, "分享内容不存在")
        
        # 获取现有的评论记录
        existing_comments = share.get('data', {}).get('comment_record', [])
        if not isinstance(existing_comments, list):
            existing_comments = []
        
        # 查找并标记要删除的评论
        comment_found = False
        for comment in existing_comments:
            if comment['comment_id'] == comment_id:
                comment['is_deleted'] = True
                comment_found = True
                break
        
        if not comment_found:
            return response.fail(404, "评论不存在")
        
        # 更新ES中的评论记录
        update_data = {
            "doc": {
                "comment_record": existing_comments
            }
        }
        # 调用更新函数
        success = es_client.update_doc(PLAZA_INDEX, share_id, update_data)
        
        if success:
            return response.success({
                "message": "评论删除成功"
            })
        else:
            return response.fail(500, "评论删除失败")
            
    except Exception as e:
        logger.error(f"删除评论时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")

@app.route('/weiwei/share/comment/deletemore', methods=['POST'])
def delete_comments():
    """批量删除评论
    
    参数：
        share_id: 分享ID
        comment_ids: 评论ID列表
        
    返回：
        成功：返回成功信息
        失败：返回错误信息
    """
    try:
        set_trace_id(request)
        data = request.get_json()
        share_id = data.get('share_id')
        comment_ids = data.get('comment_ids', [])
        
        if not share_id or not comment_ids:
            return response.fail(400, "缺少必要参数")
            
        if not isinstance(comment_ids, list):
            return response.fail(400, "comment_ids必须是列表格式")
        
        # 获取现有的分享内容
        share = test_get_plaza_share(share_id)
        if not share:
            return response.fail(404, "分享内容不存在")
        
        # 获取现有的评论记录
        existing_comments = share.get('data', {}).get('comment_record', [])
        if not isinstance(existing_comments, list):
            existing_comments = []
        
        # 查找并标记要删除的评论
        deleted_count = 0
        for comment in existing_comments:
            if comment['comment_id'] in comment_ids:
                comment['is_deleted'] = True
                deleted_count += 1
        
        if deleted_count == 0:
            return response.fail(404, "未找到要删除的评论")
        
        # 更新ES中的评论记录
        update_data = {
            "doc": {
                "comment_record": existing_comments
            }
        }
        # 调用更新函数
        success = es_client.update_doc(PLAZA_INDEX, share_id, update_data)
        
        if success:
            # 获取原贴主的mis_id
            share_owner_mis_id = share.get('data', {}).get('mis_id_keyword')
            if share_owner_mis_id:
                # 查询用户关系文档
                query = {
                    "query": {
                        "term": {
                            "mis_id": share_owner_mis_id
                        }
                    }
                }
                result = es_client.search(USER_RELATIONSHIP_INDEX, query)
                
                if result and result.get('hits', {}).get('hits'):
                    doc = result['hits']['hits'][0]['_source']
                    new_comments = doc.get('new_comments', [])
                    
                    # 从未读评论列表中移除被删除的评论
                    remaining_comments = [
                        comment for comment in new_comments 
                        if comment.get('comment_id') not in comment_ids
                    ]
                    
                    # 如果有评论被移除，更新未读计数和消息列表
                    if len(remaining_comments) < len(new_comments):
                        removed_count = len(new_comments) - len(remaining_comments)
                        update_script = {
                            "script": {
                                "source": """
                                    ctx._source.unread_comment = params.unread_comment;
                                    ctx._source.new_comments = params.new_comments;
                                """,
                                "lang": "painless",
                                "params": {
                                    "unread_comment": max(0, doc.get('unread_comment', 0) - removed_count),
                                    "new_comments": remaining_comments
                                }
                            }
                        }
                        es_client.update_doc(USER_RELATIONSHIP_INDEX, share_owner_mis_id, update_script)
                        print(f"更新未读评论数量成功：{share_owner_mis_id}")
            
            return response.success({
                "message": f"成功删除{deleted_count}条评论",
                "deleted_count": deleted_count
            })
        else:
            return response.fail(500, "评论删除失败")
            
    except Exception as e:
        logger.error(f"批量删除评论时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")

@app.route('/weiwei/share/like', methods=['POST'])
def like_share():
    try:
        set_trace_id(request)
        data = request.get_json()
        share_id = data.get('share_id')
        mis_id = data.get('mis_id')
        
        if not share_id or not mis_id:
            return response.fail(400, "缺少必要参数")
        
        # 获取现有的分享内容
        share = test_get_plaza_share(share_id)
        if not share:
            return response.fail(404, "分享内容不存在")
        
        # 检查用户是否已经点赞
        liked_users = share.get('data', {}).get('liked_users', [])
        if mis_id in liked_users:
            return response.fail(300, "用户已点赞")
        if not isinstance(liked_users, list):
            liked_users = []
        # 添加用户到点赞列表
        liked_users.append(mis_id)
        
        # 更新ES中的点赞记录
        update_data = {
            "doc": {
                "liked_users": liked_users,
                "like_count": len(liked_users)
            }
        }
        print(f"点赞用户: {liked_users}")
        success = es_client.update_doc(PLAZA_INDEX, share_id, update_data)
    
        if success:
            # 获取原贴主的mis_id
            share_owner_mis_id = share.get('data', {}).get('mis_id_keyword')
            if share_owner_mis_id:
                # 更新原贴主的未读点赞数量
                update_success = update_unread_count(share_owner_mis_id, 'like')
                if update_success:
                    # 构建新点赞信息
                    new_like = {
                        "share_id": share_id,
                        "operator_id": mis_id,
                        "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    # 更新用户关系文档，添加新点赞记录
                    script = {
                        "script": {
                            "source": """
                                if (ctx._source.new_likes == null) {
                                    ctx._source.new_likes = [];
                                }
                                ctx._source.new_likes.add(params.like)
                            """,
                            "lang": "painless",
                            "params": {
                                "like": new_like
                            }
                        }
                    }
                    
                    es_client.update_doc(USER_RELATIONSHIP_INDEX, share_owner_mis_id, script)
                    print(f"记录新点赞消息成功：{new_like}")
            
            return response.success({
                "message": "点赞成功"
            })
        else:
            return response.fail(500, "点赞失败")
            
    except Exception as e:
        logger.error(f"点赞时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")

@app.route('/weiwei/share/unlike', methods=['POST'])
def unlike_share():
    try:
        set_trace_id(request)
        data = request.get_json()
        share_id = data.get('share_id')
        mis_id = data.get('mis_id')
        
        if not share_id or not mis_id:
            return response.fail(400, "缺少必要参数")
        
        # 获取现有的分享内容
        share = test_get_plaza_share(share_id)
        if not share:
            return response.fail(404, "分享内容不存在")
            
        # 检查用户是否已经点赞
        liked_users = share.get('data', {}).get('liked_users', [])
        if mis_id not in liked_users:
            return response.fail(300, "用户未点赞")
            
        # 从点赞列表中移除用户
        liked_users.remove(mis_id)
        
        # 更新ES中的点赞记录
        update_data = {
            "doc": {
                "liked_users": liked_users,
                "like_count": len(liked_users)
            }
        }
        success = es_client.update_doc(PLAZA_INDEX, share_id, update_data)
    
        if success:
            # 获取原贴主的mis_id
            share_owner_mis_id = share.get('data', {}).get('mis_id_keyword')
            if share_owner_mis_id:
                # 查询用户关系文档，检查是否有未读的点赞消息
                query = {
                    "query": {
                        "term": {
                            "mis_id": share_owner_mis_id
                        }
                    }
                }
                result = es_client.search(USER_RELATIONSHIP_INDEX, query)
                
                if result and result.get('hits', {}).get('hits'):
                    doc = result['hits']['hits'][0]['_source']
                    new_likes = doc.get('new_likes', [])
                    
                    # 检查是否存在该用户对该帖子的未读点赞
                    has_unread = any(
                        like['share_id'] == share_id and like['operator_id'] == mis_id 
                        for like in new_likes
                    )
                    
                    if has_unread:
                        # 如果有未读点赞，减少未读计数
                        doc['unread_like'] = max(0, doc.get('unread_like', 0) - 1)
                        
                        # 移除对应的未读点赞记录
                        new_likes = [
                            like for like in new_likes 
                            if not (like['share_id'] == share_id and like['operator_id'] == mis_id)
                        ]
                        
                        # 更新文档
                        update_script = {
                            "script": {
                                "source": """
                                    ctx._source.unread_like = params.unread_like;
                                    ctx._source.new_likes = params.new_likes;
                                """,
                                "lang": "painless",
                                "params": {
                                    "unread_like": doc['unread_like'],
                                    "new_likes": new_likes
                                }
                            }
                        }
                        es_client.update_doc(USER_RELATIONSHIP_INDEX, share_owner_mis_id, update_script)
                        print(f"更新未读点赞数量成功：{share_owner_mis_id}, 当前未读数：{doc['unread_like']}")
            
            return response.success({
                "message": "取消点赞成功"
            })
        else:
            return response.fail(500, "取消点赞失败")
            
    except Exception as e:
        logger.error(f"取消点赞时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")

@app.route('/weiwei/share/dislike', methods=['POST'])
def dislike_share():
    """点踩分享
    
    参数：
        share_id: 分享ID
        mis_id: 用户ID
        
    返回：
        成功：返回成功信息
        失败：返回错误信息
    """
    try:
        set_trace_id(request)
        data = request.get_json()
        share_id = data.get('share_id')
        mis_id = data.get('mis_id')
        
        if not share_id or not mis_id:
            return response.fail(400, "缺少必要参数")
        
        # 获取现有的分享内容
        share = test_get_plaza_share(share_id)
        if not share:
            return response.fail(404, "分享内容不存在")
        
        # 检查用户是否已经点踩
        disliked_users = share.get('data', {}).get('disliked_users', [])
        if mis_id in disliked_users:
            return response.fail(400, "用户已点踩")
        
        # 添加用户到点踩列表
        disliked_users.append(mis_id)
        
        # 更新ES中的点踩记录
        update_data = {
            "doc": {
                "disliked_users": disliked_users,
                "dislike_count": len(disliked_users)
            }
        }
        success = es_client.update_doc(PLAZA_INDEX, share_id, update_data)
        
        if success:
            return response.success({
                "message": "点踩成功"
            })
        else:
            return response.fail(500, "点踩失败")
            
    except Exception as e:
        logger.error(f"点踩时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")

@app.route('/weiwei/share/undislike', methods=['POST'])
def undislike_share():
    """取消点踩分享
    
    参数：
        share_id: 分享ID
        mis_id: 用户ID
        
    返回：
        成功：返回成功信息
        失败：返回错误信息
    """
    try:
        set_trace_id(request)
        data = request.get_json()
        share_id = data.get('share_id')
        mis_id = data.get('mis_id')
        
        if not share_id or not mis_id:
            return response.fail(400, "缺少必要参数")
        
        # 获取现有的分享内容
        share = test_get_plaza_share(share_id)
        if not share:
            return response.fail(404, "分享内容不存在")
        
        # 检查用户是否已经点踩
        disliked_users = share.get('data', {}).get('disliked_users', [])
        if mis_id not in disliked_users:
            return response.fail(400, "用户未点踩")
        
        # 从点踩列表中移除用户
        disliked_users.remove(mis_id)
        
        # 更新ES中的点踩记录
        update_data = {
            "doc": {
                "disliked_users": disliked_users,
                "dislike_count": len(disliked_users)
            }
        }
        success = es_client.update_doc(PLAZA_INDEX, share_id, update_data)
        
        if success:
            return response.success({
                "message": "取消点踩成功"
            })
        else:
            return response.fail(500, "取消点踩失败")
            
    except Exception as e:
        logger.error(f"取消点踩时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")


@app.route('/weiwei/share/search', methods=['GET'])
def search_shares():
    """搜索分享内容"""
    try:
        set_trace_id(request)
        # 获取查询参数
        keyword = request.args.get('keyword')
        offset = request.args.get('offset', default=0, type=int)
        batch_size = request.args.get('batch_size', default=10, type=int)
        master_mis_id = request.args.get('master_mis_id')  # 添加master_mis_id参数
        
        if not keyword:
            return response.fail(400, "缺少搜索关键词")
            
        if not master_mis_id:  # 检查master_mis_id是否存在
            return response.fail(400, "缺少必要参数：master_mis_id")
            
        logger.info(f"开始搜索，关键词: {keyword}, offset: {offset}, batch_size: {batch_size}, master_mis_id: {master_mis_id}")
        
        try:
            # 构建搜索查询 - 使用bool查询的should条件，只要满足一个条件就可以
            query = {
                "query": {
                    "bool": {
                        "should": [
                            {"match": {"content": keyword}},
                            {"match": {"title": keyword}}
                        ],
                        "minimum_should_match": 1  # 至少匹配一个条件
                    }
                },
                "from": offset,
                "size": batch_size
            }
            
            # 执行搜索
            logger.info(f"执行ES搜索，索引: {PLAZA_INDEX}, 查询: {query}")
            
            search_result = es_client.search(PLAZA_INDEX, query)
            
            if not search_result:
                logger.warning("ES搜索返回空结果")
                return jsonify({
                    "code": 200,
                    "message": "success",
                    "data": {
                        "total": 0,
                        "total_count": 0,
                        "offset": offset,
                        "batch_size": batch_size,
                        "has_more": False,
                        "shares": []
                    }
                })
                
            # 从搜索结果中提取文档ID
            share_ids = []
            if isinstance(search_result, dict) and 'hits' in search_result:
                hits = search_result['hits']
                if 'hits' in hits:
                    share_ids = [hit['_id'] for hit in hits['hits'] if '_id' in hit]
                    total = hits.get('total', {}).get('value', 0)
                else:
                    total = 0
            else:
                total = 0
                
            logger.info(f"找到的share_ids: {share_ids}")
            
            # 如果没有找到匹配的文档
            if not share_ids:
                return jsonify({
                    "code": 200,
                    "message": "success",
                    "data": {
                        "total": 0,
                        "total_count": total,
                        "offset": offset,
                        "batch_size": batch_size,
                        "has_more": False,
                        "shares": []
                    }
                })
            
            # 使用现有函数获取分享详情，传入master_mis_id
            shares = get_plaza_shares_by_ids(share_ids, master_mis_id)  # 添加master_mis_id参数
            
            return jsonify({
                "code": 200,
                "message": "success",
                "data": {
                    "total": len(shares),
                    "total_count": total,
                    "offset": offset,
                    "batch_size": batch_size,
                    "has_more": offset + batch_size < total,
                    "shares": shares
                }
            })
            
        except Exception as es_error:
            logger.error(f"ES搜索出错: {str(es_error)}")
            raise
            
    except Exception as e:
        logger.error(f"搜索分享内容时出错: {str(e)}")
        return jsonify({
            "code": 500,
            "message": f"服务器错误: {str(e)}",
            "data": {
                "total": 0,
                "total_count": 0,
                "offset": offset,
                "batch_size": batch_size,
                "has_more": False,
                "shares": []
            }
        })

@app.route('/weiwei/user/liked_shares', methods=['GET'])
def get_user_likedshares():
    """获取用户点赞的帖子
    
    参数：
        mis_id: 用户ID
        offset: 起始位置（默认0）
        batch_size: 获取数量（默认10）
        
    返回：
        成功：返回用户点赞的帖子列表
        失败：返回错误信息
    """
    try:
        set_trace_id(request)
        # 从请求参数中获取参数
        mis_id = request.args.get('mis_id')
        offset = request.args.get('offset', default=0, type=int)
        batch_size = request.args.get('batch_size', default=10, type=int)
        
        if not mis_id:
            return response.fail(400, "缺少必要参数：mis_id")
            
        # 获取用户点赞的帖子
        liked_shares = get_user_liked_shares(mis_id)
        
        # 计算分页
        total = len(liked_shares)
        end_idx = min(offset + batch_size, total)
        paginated_shares = liked_shares[offset:end_idx]
        
        # 构建返回数据
        result = {
            "mis_id": mis_id,
            "total": total,
            "offset": offset,
            "batch_size": batch_size,
            "has_more": end_idx < total,
            "liked_shares": paginated_shares
        }
        
        return response.success(result)
        
    except Exception as e:
        logger.error(f"获取用户点赞帖子时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")

@app.route('/weiwei/user/disliked_shares', methods=['GET'])
def get_user_dislikedshares():
    """获取用户点踩的帖子
    
    参数：
        mis_id: 用户ID
        offset: 起始位置（默认0）
        batch_size: 获取数量（默认10）
        
    返回：
        成功：返回用户点踩的帖子列表
        失败：返回错误信息
    """
    try:
        set_trace_id(request)
        # 从请求参数中获取参数
        mis_id = request.args.get('mis_id')
        offset = request.args.get('offset', default=0, type=int)
        batch_size = request.args.get('batch_size', default=10, type=int)
        
        if not mis_id:
            return response.fail(400, "缺少必要参数：mis_id")
            
        # 获取用户点踩的帖子
        disliked_shares = get_user_disliked_shares(mis_id)
        
        # 计算分页
        total = len(disliked_shares)
        end_idx = min(offset + batch_size, total)
        paginated_shares = disliked_shares[offset:end_idx]
        
        # 构建返回数据
        result = {
            "mis_id": mis_id,
            "total": total,
            "offset": offset,
            "batch_size": batch_size,
            "has_more": end_idx < total,
            "disliked_shares": paginated_shares
        }
        
        return response.success(result)
        
    except Exception as e:
        logger.error(f"获取用户点踩帖子时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")

@app.route('/weiwei/user/shared_shares', methods=['GET'])
def get_user_sharedshares():
    """获取用户分享的帖子
    
    参数：
        mis_id: 用户ID
        offset: 起始位置（默认0）
        batch_size: 获取数量（默认10）
        
    返回：
        成功：返回用户分享的帖子列表
        失败：返回错误信息
    """
    try:
        set_trace_id(request)
        # 从请求参数中获取参数
        mis_id = request.args.get('mis_id')
        offset = request.args.get('offset', default=0, type=int)
        batch_size = request.args.get('batch_size', default=10, type=int)
        
        if not mis_id:
            return response.fail(400, "缺少必要参数：mis_id")
            
        # 获取用户分享的帖子
        shared_shares = get_user_shared_shares(mis_id)
        
        # 计算分页
        total = len(shared_shares)
        end_idx = min(offset + batch_size, total)
        paginated_shares = shared_shares[offset:end_idx]
        
        # 构建返回数据
        result = {
            "mis_id": mis_id,
            "total": total,
            "offset": offset,
            "batch_size": batch_size,
            "has_more": end_idx < total,
            "shared_shares": paginated_shares
        }
        
        return response.success(result)
        
    except Exception as e:
        logger.error(f"获取用户分享帖子时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")

@app.route('/weiwei/user/follow', methods=['POST'])
def follow_user_api():
    """关注用户
    
    参数：
        mis_id: 关注者mis_id
        guanzhu_misid: 被关注者mis_id
        
    返回：
        成功：返回成功信息
        失败：返回错误信息
    """
    try:
        set_trace_id(request)
        data = request.get_json()
        mis_id = data.get('mis_id')
        guanzhu_misid = data.get('guanzhu_misid')
        
        if not mis_id or not guanzhu_misid:
            return response.fail(400, "缺少必要参数")
            
        # 调用关注服务
        success = follow_user(mis_id, guanzhu_misid)
        
        if success:
            return response.success({
                "message": "关注成功"
            })
        else:
            return response.fail(400, "关注失败，可能已经关注过该用户")
            
    except Exception as e:
        logger.error(f"关注用户时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")

@app.route('/weiwei/user/unfollow', methods=['POST'])
def unfollow_user_api():
    """取消关注用户
    
    参数：
        mis_id: 关注者mis_id
        guanzhu_misid: 被关注者mis_id
        
    返回：
        成功：返回成功信息，status=0
        失败：返回错误信息
    """
    try:
        set_trace_id(request)
        data = request.get_json()
        mis_id = data.get('mis_id')
        guanzhu_misid = data.get('guanzhu_misid')
        
        if not mis_id or not guanzhu_misid:
            return response.fail(400, "缺少必要参数")
            
        # 调用取消关注服务
        success = unfollow_user(mis_id, guanzhu_misid)
        
        if success:
            return response.success({
                "status": 0,
                "message": "取消关注成功"
            })
        else:
            return response.fail(400, "取消关注失败，可能未关注该用户")
            
    except Exception as e:
        logger.error(f"取消关注用户时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")

@app.route('/weiwei/user/following', methods=['GET'])
def get_following_api():
    """获取用户的关注列表
    
    参数：
        mis_id: 用户mis_id
        offset: 起始位置（默认0）
        batch_size: 每页数量（默认10）
        
    返回：
        成功：返回关注列表
        失败：返回错误信息
    """
    try:
        set_trace_id(request)
        mis_id = request.args.get('mis_id')
        offset = request.args.get('offset', default=0, type=int)
        batch_size = request.args.get('batch_size', default=10, type=int)
        
        if not mis_id:
            return response.fail(400, "缺少必要参数：mis_id")
            
        # 获取关注列表
        result = get_following(mis_id, offset, batch_size)
        return response.success(result)
        
    except Exception as e:
        logger.error(f"获取关注列表时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")

@app.route('/weiwei/user/followers', methods=['GET'])
def get_followers_api():
    """获取用户的粉丝列表
    
    参数：
        mis_id: 用户mis_id
        offset: 起始位置（默认0）
        batch_size: 每页数量（默认10）
        
    返回：
        成功：返回粉丝列表
        失败：返回错误信息
    """
    try:
        set_trace_id(request)
        mis_id = request.args.get('mis_id')
        offset = request.args.get('offset', default=0, type=int)
        batch_size = request.args.get('batch_size', default=10, type=int)
        
        if not mis_id:
            return response.fail(400, "缺少必要参数：mis_id")
            
        # 获取粉丝列表
        result = get_followers(mis_id, offset, batch_size)
        return response.success(result)
        
    except Exception as e:
        logger.error(f"获取粉丝列表时出错: {str(e)}")
        return response.fail(500, f"服务器错误: {str(e)}")

@app.route("/weiwei/shareadd", methods=['POST'])
def share():
    try:
        request_data = request.get_json()
        size = request_data.get("size", 10)
        query_text = request_data.get("query_text", None)
        logger.info(f"share request data: {request_data}")
        if query_text == None:
            # 随便搜10条
            query_text = "烤鸭 美食 好吃 难吃 面条 恶心 太贵了"
            result = search_plaza_phrase_and_subphrases(query_text, limit=size)
        else:
            # 目前只能搜索一句话
            result = search_plaza_phrase_and_subphrases(query_text, limit=size)
        return response.success(result)

    except Exception as e:
        logger.error(f"share request data error: {str(e)}")
        return response.fail(f"share request data error: {str(e)}")


@app.route('/weiwei/user/delete_share', methods=['POST'])
def delete_plaza_share_api():
    """删除帖子接口
    
    请求参数:
        share_id: 帖子ID
        mis_id: 用户ID
        
    返回:
        {
            "status": 0,  # 0表示成功
            "message": "成功",
            "data": {"message": "删除成功"}  # 将消息作为 data 的一部分返回
        }
    """
    try:
        # 获取请求参数
        data = request.get_json()
        share_id = data.get('share_id')
        mis_id = data.get('mis_id')
        
        # 参数校验
        if not share_id or not mis_id:
            return response.fail(400, "参数错误，请提供share_id和mis_id")
            
        # 调用删除函数
        success = delete_plaza_share(share_id, mis_id)
        
        if success:
            return response.success({"message": "删除成功"})  # 修改返回格式
        else:
            return response.fail(400, "删除失败，可能是帖子不存在或没有权限")
            
    except Exception as e:
        logger.error(f"删除帖子时出错: {str(e)}")
        return response.fail(500, "服务器内部错误")

@app.route('/weiwei/user/edit_share', methods=['POST'])
def edit_plaza_share_api():
    """编辑帖子内容接口
    
    请求参数:
        share_id: 帖子ID（必需）
        mis_id: 用户ID（必需）
        title: 新的标题（可选）
        content: 新的内容（可选）
        
    返回:
        {
            "status": 0,  # 0表示成功
            "message": "成功",
            "data": {"message": "更新成功"}
        }
    """
    try:
        # 获取请求参数
        data = request.get_json()
        share_id = data.get('share_id')
        mis_id = data.get('mis_id')
        title = data.get('title')
        content = data.get('content')
        
        # 参数校验 - 检查必需参数
        if not share_id or not mis_id:  # 修改这里，添加 mis_id 的检查
            return response.fail(400, "参数错误，请提供share_id和mis_id")
            
        # 参数校验 - 检查可选参数
        if not title and not content:
            return response.fail(400, "参数错误，请至少提供title或content中的一个")
            
        # 构建更新内容
        update_data = {}
        if title:
            update_data['title'] = title
        if content:
            update_data['content'] = content
            
        # 调用更新函数
        success = update_plaza_share_content(share_id, update_data, mis_id)
        
        if success:
            return response.success(data={"message": "更新成功"})
        else:
            return response.fail(400, "更新失败，可能是帖子不存在或没有权限")
            
    except Exception as e:
        logger.error(f"编辑帖子时出错: {str(e)}")
        return response.fail(500, "服务器内部错误")


@app.route('/weiwei/user/unread_counts', methods=['GET'])
def get_unread_counts_api():
    """获取用户未读点赞和评论数量接口
    
    请求参数:
        mis_id: 用户ID
        
    返回:
        {
            "status": 0,
            "message": "成功",
            "data": {
                "unread_like_count": 0,
                "unread_comment_count": 0
            }
        }
    """
    try:
        # 获取请求参数
        mis_id = request.args.get('mis_id')
        
        # 参数校验
        if not mis_id:
            return response.fail(400, "参数错误，请提供mis_id")
            
        # 获取未读消息数量
        counts = get_unread_counts(mis_id)
        
        return response.success(data=counts)
        
    except Exception as e:
        logger.error(f"获取未读消息数量时出错: {str(e)}")
        return response.fail(500, "服务器内部错误")

@app.route('/weiwei/user/mark_as_read', methods=['POST'])
def mark_as_read_api():
    """标记消息为已读接口
    
    请求参数:
        mis_id: 用户ID
        count_type: 消息类型，可选值：'like', 'comment'
        
    返回:
        {
            "status": 0,
            "message": "成功",
            "data": null
        }
    """
    try:
        data = request.get_json()
        mis_id = data.get('mis_id')
        count_type = data.get('count_type')
        
        if not mis_id or not count_type:
            return response.fail(400, "参数错误，请提供mis_id和count_type")
            
        if count_type not in ['like', 'comment']:
            return response.fail(400, "参数错误，count_type必须是'like'或'comment'")
            
        # 清除未读消息计数
        success = clear_unread_count(mis_id, count_type)
        
        if success:
            # 清空对应类型的消息数组
            field = 'new_likes' if count_type == 'like' else 'new_comments'
            script = {
                "script": {
                    "source": f"ctx._source.{field} = []"
                }
            }
            
            es_client.update_doc(USER_RELATIONSHIP_INDEX, mis_id, script)
            
            return response.success(data={
                "message": "标记已读成功"
            })
        else:
            return response.fail(500, "标记已读失败")
            
    except Exception as e:
        logger.error(f"标记消息为已读时出错: {str(e)}")
        return response.fail(500, "服务器内部错误")


@app.route('/weiwei/location/calculate-distance', methods=['POST'])
def api_calculate_distance():
    """计算用户与商家之间的距离
    
    请求体格式:
    {
        "user_location": {
            "latitude": 39.9042,
            "longitude": 116.4074
        },
        "poi_id": "123456"
    }
    
    或者批量请求:
    {
        "user_location": {
            "latitude": 39.9042,
            "longitude": 116.4074
        },
        "poi_ids": ["123456", "789012"],
        "max_distance": 5000  // 可选，限制最大距离（米）
    }
    """
    try:
        set_trace_id(request)
        request_data = request.get_json()
        
        # 检查请求参数
        user_location = request_data.get('user_location')
        if not user_location or 'latitude' not in user_location or 'longitude' not in user_location:
            return response.fail(400, "用户位置参数错误")
            
        user_lat = user_location['latitude']
        user_lon = user_location['longitude']
        
        # 单个商家距离计算
        if 'poi_id' in request_data:
            poi_id = request_data['poi_id']
            result = test_distance_calculation(user_lat, user_lon, poi_id)
            return response.success(result)
            
        # 批量商家距离计算
        elif 'poi_ids' in request_data:
            poi_ids = request_data['poi_ids']
            max_distance = request_data.get('max_distance')
            
            if not isinstance(poi_ids, list):
                return response.fail(400, "poi_ids必须是列表")
                
            merchants = get_merchants_by_distance(user_lat, user_lon, poi_ids, max_distance)
            return response.success({
                'success': True,
                'user_location': user_location,
                'merchants': merchants,
                'count': len(merchants)
            })
            
        else:
            return response.fail(400, "缺少poi_id或poi_ids参数")
            
    except Exception as e:
        logger.error(f"计算距离失败: {str(e)}")
        return response.fail(500, str(e))

@app.route('/weiwei/user/new_messages', methods=['GET'])
def get_new_messages_api():
    try:
        mis_id = request.args.get('mis_id')
        msg_type = request.args.get('type')
        
        if not mis_id:
            return response.fail(400, "缺少必要参数mis_id")
            
        # 获取用户关系文档
        query = {
            "query": {
                "term": {
                    "mis_id": mis_id
                }
            }
        }
        
        result = es_client.search(USER_RELATIONSHIP_INDEX, query)
        if not result or not result.get('hits', {}).get('hits'):
            return response.success({"messages": []})
            
        doc = result['hits']['hits'][0]['_source']
        
        # 根据消息类型返回对应的消息列表
        if msg_type == 'like':
            messages = doc.get('new_likes', [])
        elif msg_type == 'comment':
            messages = doc.get('new_comments', [])
        else:
            # 如果没有指定类型，返回所有消息并按时间排序
            messages = []
            likes = doc.get('new_likes', [])
            comments = doc.get('new_comments', [])
            for like in likes:
                like['type'] = 'like'
                messages.append(like)
            for comment in comments:
                comment['type'] = 'comment'
                messages.append(comment)
            # 按时间倒序排序
            messages.sort(key=lambda x: x['create_time'], reverse=True)
        
        return response.success({
            "messages": messages
        })
        
    except Exception as e:
        logger.error(f"获取新消息详情时出错: {str(e)}")
        return response.fail(500, "服务器内部错误")

@app.route('/weiwei/plaza/hot_shares', methods=['GET'])
def get_hot_shares_api():
    """分页获取热门帖子的接口
    
    参数：
        offset: 起始位置（默认0）
        batch_size: 获取数量（默认10）
        master_mis_id: 用户ID
        
    返回：
        成功：返回分页的热门帖子列表
        失败：返回错误信息
    """
    try:
        # 从请求参数中获取偏移量、批量大小和用户ID
        offset = request.args.get('offset', default=0, type=int)
        batch_size = request.args.get('batch_size', default=10, type=int)
        master_mis_id = request.args.get('master_mis_id')
        
        # 检查 master_mis_id 是否存在
        if not master_mis_id:
            return response.fail(400, "缺少必要参数：master_mis_id")
        
        # 获取按热度排序的share_ids
        hot_share_ids = get_hot_share_ids()
        if not hot_share_ids:
            return jsonify({
                "code": 200,
                "message": "success",
                "data": {
                    "total": 0,
                    "offset": offset,
                    "batch_size": batch_size,
                    "has_more": False,
                    "shares": []
                }
            })
        
        # 计算实际需要获取的数据范围
        end_idx = min(offset + batch_size, len(hot_share_ids))
        
        # 获取当前批次的share_ids
        batch_share_ids = hot_share_ids[offset:end_idx]
        
        # 获取分享内容，传入master_mis_id
        shares = get_plaza_shares_by_ids(batch_share_ids, master_mis_id)
        
        response_data = {
            "code": 200,
            "message": "success",
            "data": {
                "total": len(hot_share_ids),  # 总记录数
                "offset": offset,             # 当前偏移量
                "batch_size": batch_size,     # 批量大小
                "has_more": end_idx < len(hot_share_ids),  # 是否还有更多数据
                "shares": shares              # 分享内容列表
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"获取热门帖子时出错: {str(e)}")
        return jsonify({
            "code": 500,
            "message": "获取热门帖子失败",
            "data": {
                "total": 0,
                "offset": offset,
                "batch_size": batch_size,
                "has_more": False,
                "shares": []
            }
        })


@app.route('/weiwei/plaza/following_shares', methods=['GET'])
def get_following_shares_api():
    """获取关注用户的帖子API"""
    try:
        # 获取参数
        mis_id = request.args.get('mis_id')
        offset = request.args.get('offset', default=0, type=int)
        batch_size = request.args.get('batch_size', default=10, type=int)
        
        if not mis_id:
            return response.fail(400, "缺少必要参数：mis_id")
            
        # 获取关注用户的帖子
        result = get_following_shares(mis_id, offset, batch_size)
        
        return jsonify({
            "code": 200,
            "message": "success",
            "data": result
        })
        
    except Exception as e:
        logger.error(f"获取关注用户帖子接口出错: {str(e)}")
        return jsonify({
            "code": 500,
            "message": f"服务器错误: {str(e)}",
            "data": {
                "total": 0,
                "total_count": 0,
                "offset": offset,
                "batch_size": batch_size,
                "has_more": False,
                "shares": []
            }
        })


@app.route('/weiwei/plaza/personalized_shares', methods=['GET'])
def get_personalized_shares_api():
    """获取个性化推荐帖子API"""
    try:
        # 获取参数
        mis_id = request.args.get('mis_id')
        offset = request.args.get('offset', default=0, type=int)
        batch_size = request.args.get('batch_size', default=10, type=int)
        
        if not mis_id:
            return response.fail(400, "缺少必要参数：mis_id")
            
        # 获取推荐帖子
        result = get_personalized_shares(mis_id, offset, batch_size)
        
        return jsonify({
            "code": 200,
            "message": "success",
            "data": result
        })
        
    except Exception as e:
        logger.error(f"获取个性化推荐帖子接口出错: {str(e)}")
        return jsonify({
            "code": 500,
            "message": f"服务器错误: {str(e)}",
            "data": {
                "total": 0,
                "total_count": 0,
                "offset": offset,
                "batch_size": batch_size,
                "has_more": False,
                "shares": []
            }
        })





# 查看logger所有处理器
def show_handlers(logger, args):
    print("\n当前的处理器列表：")
    target_level = args.level
    local = args.local
    for handler_id, handler in logger._core.handlers.items():
        # 获取处理器信息
        sink = handler._sink
        level = handler._levelno
        format = handler._formatter
        
        # 获取可读的 sink 描述
        if hasattr(sink, '_file'):  # StreamSink 的情况
            if sink._file is sys.stdout:
                sink_desc = "标准输出 (stdout)"
            elif sink._file is sys.stderr:
                sink_desc = "标准错误 (stderr)"
            else:
                sink_desc = f"文件流: {sink._file}"
        elif hasattr(sink, 'path'):  # FileSink 的情况
            sink_desc = f"文件: {sink.path}"
        else:
            # 通用处理方式，检查 sink 对象是否有 handler 属性
            if hasattr(sink, 'handler') and hasattr(sink.handler, 'stream'):
                if sink.handler.stream is sys.stdout:
                    sink_desc = "标准输出 (stdout)"
                elif sink.handler.stream is sys.stderr:
                    sink_desc = "标准错误 (stderr)"
                else:
                    sink_desc = f"流类型: {sink.handler.stream}"
            else:
                sink_desc = f"类型: {sink.__class__.__name__}"
        
        # 获取可读的日志级别
        level_names = {
            10: "DEBUG",
            20: "INFO",
            30: "WARNING",
            40: "ERROR",
            50: "CRITICAL"
        }

        level_names_to_int = {
            "DEBUG": 10,
            "INFO": 20,
            "WARNING": 30,
            "ERROR": 40,
            "CRITICAL": 50
        }
        level_name = level_names.get(level, str(level))
        
        # 获取格式化器的信息
        if hasattr(format, 'format'):
            format_desc = format.format
        else:
            format_desc = "默认格式"
        
        print(f"处理器 ID: {handler_id}")
        print(f"- 输出目标: {sink_desc}")
        print(f"- 日志级别: {level_name}")
        print(f"- 格式化: {format_desc}")
        print("-" * 50)
        if sink_desc == "类型: StreamSink":
            logger.remove(handler_id)
            logger.add(sink, level=level_names_to_int[target_level])
            print(f"已将sink{sink} 的日志级别设置为 {target_level}")




# 本地测试时，可以指定日志级别和日志文件
def main():
    parser = argparse.ArgumentParser(description='服务启动程序')
    parser.add_argument('--level', type=str, default='DEBUG',
                       help='日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)')
    parser.add_argument('--logfile', type=str, default="None",
                        help='日志文件路径')
    parser.add_argument('--local', action='store_true',
                        help='是否本地测试')
    args = parser.parse_args()

    if args.logfile != "None":
        logger.add(args.logfile, rotation="100MB", level="INFO",
           format=LOG_FORMAT)
    show_handlers(logger, args)
    
    # Start the async task in a separate thread
    # Thread(target=lambda: asyncio.run(cycle_update_portrait()), daemon=True).start()
    
    # Continue with Flask app startup
    app.run(host="0.0.0.0", port=8080, debug=False)
    app.config['JSON_AS_ASCII'] = False


@app.route("/weiwei/deepsearch/streamSearch", methods=['POST'])
def stream_search():
    """
    点评流式搜索接口
    
    接口说明：
    - 该接口用于处理点评的流式搜索请求，支持实时返回搜索结果
    - 采用SSE(Server-Sent Events)格式进行流式响应
    - 支持地理位置、用户信息等上下文参数
    
    请求参数：
    - keyword: 搜索关键词
    - cityId: 城市ID
    - locateCityId: 定位城市ID
    - userId: 用户ID
    - dpid: 设备ID
    - lat: 纬度
    - lng: 经度
    
    返回格式：
    - 采用SSE格式，每行以"data:"开头
    - 返回JSON格式的数据，包含搜索结果、推荐信息等
    
    示例：
    GET /weiwei/deepsearch/streamSearch?keyword=望京正宗川菜&cityId=1&locateCityId=1&userId=108028986&dpid=xxx&lat=31.22&lng=121.42
    """
    set_trace_id(request)
    try:
        request_json = request.get_json()
        # 获取请求参数
        params = {
            'keyword': request_json.get('keyword'),
            'cityId': request_json.get('cityId'),
            'locateCityId': request_json.get('locateCityId'),
            'userId': request_json.get('userId'),
            'dpid': request_json.get('dpid'),
            'lat': request_json.get('lat'),
            'lng': request_json.get('lng'),
            "sessionId":request_json.get("conversation_id","")
        }
        
        logger.info(f"stream search request params: {params}")
        
        # 调用外部接口
        external_api_url = "https://dpllm.sankuai.com/api/sse/aisearch/evaluate"
        headers = {
            "Accept": "text/event-stream",  # 明确接受SSE格式
            "Content-Type": "application/json"
        }
        
        
        def generate():
            logger.info("Starting stream generation")
            thinking_responses = []
            main_responses = []
            thinking_mode = False
            main_mode = False
            conversation_id = request_json.pop("conversation_id","test_conv_1_by_llf")
            last_message = request_json.get("keyword","")
            user_id = request_json.pop("mis_id","")
            message_id = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
            
            start_time = time.time()

            try:
                # 使用stream=True参数进行流式请求
                with requests.get(
                    external_api_url,
                    params=params,
                    headers=headers,
                    stream=True,  # 启用流式传输
                    timeout=300  # 设置超时时间
                ) as r:
                    logger.info(f"External API response status: {r.status_code}")
                    # logger.info(f"External API response content: {r.text},{r.headers},{r.content},{r}")
                    # # 检查状态码
                    if r.status_code != 200:
                        error_msg = f"外部API调用失败: {r.status_code}"
                        logger.error(error_msg)
                        yield json.dumps({"error": error_msg}, ensure_ascii=False)
                        return
                    
                    # 逐行传输响应
                    for line in r.iter_lines():
                        if line:
                            decoded_line = line.decode('utf-8')
                            decoded_line = decoded_line.replace("data:", "")
                            logger.debug(f"Streaming line: {decoded_line}")

                            data_json = json.dumps({    
                                "message_id": message_id,
                                "content": decoded_line
                            }, ensure_ascii=False)

                            yield f"data: {data_json}\n\n"

                            if decoded_line == "深度思考：":
                                thinking_mode = True
                                main_mode = False
                                continue
                            elif decoded_line == "FollowUp:":
                                main_mode = False
                                continue

                            if thinking_mode:
                                if decoded_line.startswith('<span style="color:grey">') and decoded_line.endswith('</span>'):
                                    # 去掉前后标签
                                    content = decoded_line.replace('<span style="color:grey">', '').replace('</span>', '')
                                    thinking_responses.append(content)
                                else:
                                    # 不再是深度思考内容，切换到main_mode
                                    thinking_mode = False
                                    main_mode = True
                                    if decoded_line:  # 避免空行
                                        main_responses.append(decoded_line)
                            elif main_mode and decoded_line:
                                main_responses.append(decoded_line)

                              # SSE格式要求每条数据以data:开头
            
                    # logger.info(f"collected_responses: {collected_responses}")

                    # 统计用时
                    end_time = time.time()
                    duration = end_time - start_time

                    final_think_content = ''.join(thinking_responses).strip()
                    final_main_content = ''.join(main_responses).strip()
                    logger.info(f"深度思考内容: {final_think_content}")
                    logger.info(f"主内容: {final_main_content}")
                    logger.info(f"流式输出处理用时: {duration:.2f}秒")

                    final_message = json.dumps({
                        "message_id": message_id,
                        "think_length":len(final_think_content),
                        "main_length":len(final_main_content),
                        "total_time_cost": duration,
                        "status":"[DONE]"
                    }, ensure_ascii=False)
                    yield f"data: {final_message}\n\n"  

                                    # 上传对话历史
                    try:
                        history_response = upload_conversation_history(conversation_id, new_message={
                            "message_id": message_id,
                            "reasoning_content": final_think_content,
                            "main_content": final_main_content,
                            "total_time_cost": duration,
                            "user_message": last_message
                        }, api_name="dianping", user_id=user_id)

                        if not history_response:
                            logger.error(f"上传对话历史到ES失败，对话ID: {conversation_id}")
                            
                        else:
                            logger.info(f"上传对话历史到ES成功，对话ID: {conversation_id}")
                            
                    except Exception as e:
                        logger.error(f"上传对话历史到ES失败，对话ID: {conversation_id}")
                
                    try:
                        upload_used_ids(user_id)
                    except Exception as e:
                        logger.error(f"上传mis_id到ES失败，用户ID: {user_id}")  
                
            except Exception as e:
                logger.error(f"Stream generation error: {e}")
                yield json.dumps({"error": f"Stream generation error: {e}"})
        
        # 返回流式响应
        return Response(
            generate(),
            content_type='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no'  # 禁用 Nginx 缓冲
            }
        )

    
    except Exception as e:
        logger.error(f"stream search error: {e}")
        return response.fail(f"stream search error: {e}")


@app.route("/weiwei/compare/centralagent", methods=['POST'])
def post_centralagent():
    """
    点评中心化agent接口
    
    
    """
    set_trace_id(request)
    try:
        request_json = request.get_json()
        logger.info(f"central agent request: {request_json}")
        
        # 调用外部接口
        external_api_url = "http://i-beam.sankuai.com/ca/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream"  # 明确接受SSE格式
        }
        
        
        def generate():

            answer_content = []
            conversation_id = request_json.get("conversation_id","test_conv_1_by_llf")
            last_message = request_json.get("message",{}).get("content",[])
            if last_message:
                last_message = last_message[-1].get("text","")
            else:
                return response.fail("传入空消息！")
            user_id = request_json.get("mis_id","")
            message_id = str(uuid4())
            request_json["message_id"] = message_id
            request_json["child_message_id"] = message_id+str(random.randint(100000, 999999))


            start_time = time.time()


            # 使用stream=True参数进行流式请求
            with requests.post(
                external_api_url,
                json=request_json,
                headers=headers,
                stream=True  # 启用流式传输
            ) as r:
                # 
                external_api_ttfb_end_time = time.time()
                ttfb_duration = external_api_ttfb_end_time - start_time
                logger.info(f"External API call to {external_api_url} - Status: {r.status_code}, TTFB: {ttfb_duration:.4f} seconds")
                if r.status_code != 200:
                    logger.error(f"外部API调用失败: {r.status_code}")
                    yield json.dumps({"error": f"外部API调用失败: {r.status_code}"}, ensure_ascii=False)
                    return
                
                # 逐行传输响应
                for line in r.iter_lines():
                    if line:
                        decoded_line = line.decode('utf-8')
                        logger.debug(f"Streaming line: {decoded_line}")
                        cut_line = decoded_line.replace("data:", "")
                        if cut_line == "[DONE]":
                            break

                        cut_json = json.loads(cut_line)
                        if cut_json['choices'] == None:
                            continue
                        if cut_json.get("choices",[{}])[0].get("delta",{}).get("type","") == "ANSWER":
                            answer_content.append(cut_json.get("choices",[])[0].get("delta",{}).get("content",""))    
                        data_json = json.dumps({
                            "message_id": message_id,
                            "content": cut_json
                           }, ensure_ascii=False)
                        yield f"data: {data_json}\n\n"
                        

                end_time = time.time()
                duration = end_time - start_time
                final_answer_content = "".join(answer_content).strip()
                logger.info(f"回答内容: {final_answer_content}")
                logger.info(f"流式输出处理用时: {duration:.2f}秒")
                final_message = {
                    "message_id": message_id,
                    "main_length":len(final_answer_content),
                    "total_time_cost": duration,
                    "status":"[DONE]"
                }
                yield f"data: {final_message}\n\n"

            
                # 上传对话历史
                try:
                    history_response = upload_conversation_history(conversation_id, new_message={
                        "message_id": message_id,
                        "main_content": final_answer_content,
                        "total_time_cost": duration,
                        "user_message": last_message
                    }, api_name="centralagent", user_id=user_id)

                    if not history_response:
                        logger.error(f"上传对话历史到ES失败，对话ID: {conversation_id}")
                        
                    else:
                        logger.info(f"上传对话历史到ES成功，对话ID: {conversation_id}")
                        
                except Exception as e:
                    logger.error(f"上传对话历史到ES失败，对话ID: {conversation_id}")

                try:
                    upload_used_ids(user_id)
                except Exception as e:
                    logger.error(f"上传mis_id到ES失败，用户ID: {user_id}")

        # 返回流式响应
        res = Response(generate(), content_type='text/event-stream')

        res.headers['X-Accel-Buffering'] = 'no'
        res.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        res.headers['Pragma'] = 'no-cache'
        res.headers['Expires'] = '0'
        return res
            
    except Exception as e:
        logger.error(f"central beam agent error: {e}")
        return response.fail(f"central beam agent error: {e}")

                

@app.route("/weiwei/compare/new_conversation", methods=['POST'])
def new_conversation():
    """
    新对话接口
    
    接口说明：
    - 该接口用于处理新对话请求，根据用户id与当前时间返回新的对话id

    """
    try:
        request_json = request.get_json()
        user_id = request_json.get('user_id')
        current_time= datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
        conversation_id= f"{user_id}_{current_time}"
        return response.success(conversation_id)    
    except Exception as e:
        logger.error(f"new conversation error: {e}")
        return response.fail(f"new conversation error: {e}")
    
def upload_conversation_history(conversation_id, new_message, api_name, user_id=None):
    """
    上传对话历史到ES
    
    读入当前调用接口名以及接口的返回内容，更新es的历史记录

    """
    try:
        return upload_conv_his(conversation_id, new_message, api_name, user_id)

    except Exception as e:
        logger.error(f"upload conversation history error: {e}")
        return False

@app.route("/weiwei/compare/get_conversation_history_by_id", methods=['POST'])
def get_conversation_by_id():
    """
    根据conversation_id获取该对话的全部历史记录
    请求参数：
        conversation_id: 对话ID
    返回：
        conversation_id对应对话的所有历史记录
    """
    try:
        request_json = request.get_json()
        conversation_id = request_json.get("conversation_id","")
        if not conversation_id:
            return response.fail("No available conversation id")
        
        history = get_conv_by_id(conversation_id)
        if history:
            return response.success(history)
        else:
            return response.fail("No available conversation history")
        
    except Exception as e:
        logger.error(f"get conversation history by id error: {e}")
        return response.fail(f"get conversation history by id error: {e}")

#agentPK根据用户ID获取该用户所有的conversation_id
@app.route("/weiwei/compare/get_conversation_ids_by_user", methods=['POST'])
def get_conversation_ids_by_user_route():
    """
    根据user_id获取该用户所有的conversation_id（即compare_history索引下按照user_id字段查询到的所有_id）
    请求参数：
        user_id: 用户ID
    返回：
        conversation_id列表
    """
    try:
        request_json = request.get_json()
        user_id = request_json.get("user_id", "")
        if not user_id:
            return response.fail("缺少user_id参数")
        
        conversation_ids = get_conv_ids_by_user(user_id)

        res = {
            "user_id": user_id,
            "conversation_ids": conversation_ids,
            "total": len(conversation_ids)
        }
        return response.success(res)
    except Exception as e:
        logger.error(f"get conversation ids by user error: {e}")
        return response.fail(f"get conversation ids by user error: {e}")

#agentPK根据对话ID获取对话名称
@app.route("/weiwei/compare/get_conversation_name_by_id", methods=['POST'])
def get_conversation_name_by_id():
    """
    根据conversation_id获取该对话的名称
    请求参数：
        conversation_id: 对话ID
    返回：
        conversation_id对应对话的名称
    """
    try:
        request_json = request.get_json()
        conversation_id = request_json.get("conversation_id", "")
        if not conversation_id:
            return response.fail("No available conversation id")
        
        conversation_name = get_conv_name_by_id(conversation_id)
        return response.success(conversation_name)
                

    except Exception as e:
        logger.error(f"get conversation name by id error: {e}")
        return response.fail(f"get conversation name by id error: {e}")
    
#agentPK点赞或点踩
@app.route("/weiwei/compare/like_or_dislike_a_message", methods=['POST'])
def like_or_dislike_a_message():
    """
    点赞/点踩一条消息
    请求参数：
        conversation_id: 对话ID
        message_id: 消息ID
        api_name: 接口名称  
        like_or_dislike: 点赞/点踩
    """
    try:
        request_json = request.get_json()
        conversation_id = request_json.get("conversation_id", "")
        message_id = request_json.get("message_id", "")
        api_name = request_json.get("api_name", "")
        like_or_dislike = request_json.get("like_or_dislike", "")

        if not conversation_id or not message_id or not api_name:
            return response.fail("缺少必要参数")

        update_result = like_or_dislike_a_msg(conversation_id, message_id, api_name, like_or_dislike)
        return update_result

    except Exception as e:
        logger.error(f"like or dislike a message error: {e}")
        return response.fail(f"like or dislike a message error: {e}")
    
#agentPK平均响应时间    
@app.route("/weiwei/compare/average_response_time", methods=['POST'])
def average_response_time():
    try:
        
        request_json = request.get_json()
        user_id = request_json.get("user_id", "")
        api_name = request_json.get("api_name", "")
        if not user_id or not api_name:
            return response.fail("缺少必要参数")
        
        
        ave_time_response = ave_res_time(user_id, api_name)
        return ave_time_response
    except Exception as e:
        logger.error(f"average response time error: {e}")
        return response.fail(f"average response time error: {e}")

#agentPK管理员界面平均响应时间
@app.route("/weiwei/compare/admin/average_response_time", methods=['POST'])
def admin_average_response_time():
    try:
  
        request_json = request.get_json()
        api_name = request_json.get("api_name", "")
        if not api_name:
            return response.fail("缺少必要参数")    
        
        admin_ave_time_response = admin_ave_res_time(api_name)
        return admin_ave_time_response
        
    except Exception as e:
        logger.error(f"admin average response time error: {e}")
        return response.fail(f"admin average response time error: {e}")
        
#agentPK平均响应长度
@app.route("/weiwei/compare/average_response_length", methods=['POST'])
def average_response_length():
    try:
        
        request_json = request.get_json()
        user_id = request_json.get("user_id", "")
        api_name = request_json.get("api_name", "")
        if not user_id or not api_name:
            return response.fail("缺少必要参数")
        
        ave_length_response = ave_res_length(user_id, api_name)
        return ave_length_response
    
    except Exception as e:
        logger.error(f"average response length error: {e}")
        return response.fail(f"average response length error: {e}")

#agentPK管理员界面平均响应长度
@app.route("/weiwei/compare/admin/average_response_length", methods=['POST'])
def admin_average_response_length():
    try:
        request_json = request.get_json()
        api_name = request_json.get("api_name", "")
        
        if not api_name:
            return response.fail("缺少必要参数")    
        
        admin_ave_length_response = admin_ave_res_length(api_name)
        return admin_ave_length_response    
        
    except Exception as e:
        logger.error(f"admin average response length error: {e}")
        return response.fail(f"admin average response length error: {e}")   

#agentPK平均每个人点赞和点踩
@app.route("/weiwei/compare/total_likes_and_dislikes", methods=['POST'])
def total_likes_and_dislikes():
    try:
        
        request_json = request.get_json()   
        user_id = request_json.get("user_id", "")
        api_name = request_json.get("api_name", "")
        if not user_id or not api_name:
            return response.fail("缺少必要参数")
        
        total_likes_and_dislikes_response = total_likes(user_id, api_name)
        return total_likes_and_dislikes_response
        

    except Exception as e:
        logger.error(f"total likes and dislikes error: {e}")
        return response.fail(f"total likes and dislikes error: {e}")

#agentPK管理员界面平均每个人响应次数
@app.route("/weiwei/compare/admin/total_messages_sent_by_api", methods=['POST'])
def total_messages_sent_by_api():
    try:
        request_json = request.get_json()
        api_name = request_json.get("api_name", "")
        if not api_name:
            return response.fail("缺少必要参数")
        
        total_messages_sent_by_api_response = total_msg_sent_by_api(api_name)
        return total_messages_sent_by_api_response

        
        
    except Exception as e:
        logger.error(f"total messages sent by api error: {e}")
        return response.fail(f"total messages sent by api error: {e}")
        
#agentPK管理员界面平均每个人点赞和点踩
@app.route("/weiwei/compare/admin/total_likes_and_dislikes", methods=['POST'])
def admin_total_likes_and_dislikes():
    try:
    
        request_json = request.get_json()   
        api_name = request_json.get("api_name", "")
        
        if not api_name:
            return response.fail("缺少必要参数")
        
        total_likes_and_dislikes_response = admin_total_likes(api_name)
        return total_likes_and_dislikes_response
        

    except Exception as e:
        logger.error(f"admin total likes and dislikes error: {e}")
        return response.fail(f"admin total likes and dislikes error: {e}")

#agentPK平均每个人响应次数
@app.route("/weiwei/compare/total_messages_sent", methods=['POST'])
def total_messages_sent():
    try:
        
        request_json = request.get_json()
        user_id = request_json.get("user_id", "")
        conversation_id = request_json.get("conversation_id", "")
        
        if not user_id or not conversation_id:
            return response.fail("缺少必要参数")
        
        total_messages_sent_response = total_msgs_sent(user_id, conversation_id)
        return total_messages_sent_response 
        

    except Exception as e:
        logger.error(f"total messages sent error: {e}")
        return response.fail(f"total messages sent error: {e}")

    


@app.route("/weiwei/compare/admin/delete_a_conversation", methods=['POST'])
def delete_a_conversation():
    try:
        request_json = request.get_json()
        conversation_id = request_json.get("conversation_id", "")
        if not conversation_id: 
            return response.fail("缺少必要参数")
        
        success = delete_conv(conversation_id)
        if success:
            return response.success({"message": "删除成功"})
        else:
            return response.fail("删除失败")
    except Exception as e:
        logger.error(f"删除对话失败，对话ID: {conversation_id}")
        return response.fail(f"删除对话失败，对话ID: {conversation_id}")
            
#agentPK管理员界面平均每个人响应时间
@app.route("/weiwei/compare/admin/average_response_time_by_date_and_api", methods=['POST'])
def average_response_time_by_date_and_api():
    try:
        request_json = request.get_json()
        date_str = request_json.get("date", "")
        api_name = request_json.get("api_name", "")
        
        if not date_str or not api_name:
            return response.fail("缺少必要参数")
        
        ave_time_response = ave_res_time_by_date_and_api(date_str, api_name)
        return ave_time_response
        

    except Exception as e:
        logger.error(f"average response time by date and api error: {e}")
        return response.fail(f"average response time by date and api error: {e}")

#agentPK管理员界面平均每个人响应长度
@app.route("/weiwei/compare/admin/average_response_length_by_date_and_api", methods=['POST'])
def average_response_length_by_date_and_api():
    try:
        request_json = request.get_json()
        date_str = request_json.get("date", "")
        api_name = request_json.get("api_name", "")
        
        if not date_str or not api_name:
            return response.fail("缺少必要参数")
        
        ave_length_response = ave_res_length_by_date_and_api(date_str, api_name)
        return ave_length_response  
        

    except Exception as e:
        logger.error(f"average response length by date and api error: {e}")
        return response.fail(f"average response length by date and api error: {e}") 

#agentPK管理员界面平均每个人响应次数
@app.route("/weiwei/compare/admin/total_messages_sent_by_date_and_api", methods=['POST'])
def total_messages_sent_by_date_and_api():
    try:
        request_json = request.get_json()   
        date_str = request_json.get("date", "")
        api_name = request_json.get("api_name", "")
        
        if not date_str or not api_name:
            return response.fail("缺少必要参数")
        
        total_messages_sent_by_date_and_api_response = total_msgs_sent_by_date_and_api(date_str, api_name)
        return total_messages_sent_by_date_and_api_response 

    except Exception as e:
        logger.error(f"total messages sent by date and api error: {e}")
        return response.fail(f"total messages sent by date and api error: {e}") 

#agentPK管理员界面平均每个人点赞和点踩
@app.route("/weiwei/compare/admin/total_likes_and_dislikes_by_date_and_api", methods=['POST'])
def total_likes_and_dislikes_by_date_and_api():
    try:
        request_json = request.get_json()
        date_str = request_json.get("date", "")
        api_name = request_json.get("api_name", "")
        
        if not date_str or not api_name:
            return response.fail("缺少必要参数")
        
        total_likes_and_dislikes_response = total_likes_by_date_and_api(date_str, api_name)
        return total_likes_and_dislikes_response
    
    except Exception as e:
        logger.error(f"total likes and dislikes by date and api error: {e}")
        return response.fail(f"total likes and dislikes by date and api error: {e}")
        
        
#agentPK管理员界面检查用户是否为管理员
@app.route("/weiwei/compare/admin/check_admin_misid", methods=['POST'])
def check_admin_misid():
    try:
        request_json = request.get_json()
        mis_id = request_json.get("mis_id", "")
        
        if not mis_id:
            return response.fail(400, "缺少必要参数：mis_id")
        
        # 调用服务方法检查用户是否为管理员
        is_admin, _ = check_is_admin(mis_id)
        
        return response.success({
            "is_admin": is_admin
        })
        
    except Exception as e:
        logger.error(f"检查管理员权限失败: {str(e)}")
        return response.fail(f"检查管理员权限失败: {str(e)}")

#agentPK白名单校验
@app.route("/weiwei/compare/check_white_list", methods=['POST'])
def check_white_list():
    try:
        request_json = request.get_json()
        mis_id = request_json.get("mis_id", "")         
        
        if not mis_id:  
            return response.fail(400, "缺少必要参数：mis_id")
        
        is_white_list, _ = check_is_white_list(mis_id)
        
        return response.success({
            "is_white_list": is_white_list
        })  

    except Exception as e:
        logger.error(f"检查白名单失败: {str(e)}")
        return response.fail(f"检查白名单失败: {str(e)}")
    
@app.route("/weiwei/get_mt_userid_by_mis", methods=['POST'])
def get_mt_userid_by_mis():
    try:
        request_json = request.get_json()
        mis_id = request_json.get("mis_id", "")
        client = UserClient.get_client()
        # 获取信息
        result,_ = client.get_mt_userid_by_mis(mis_id)

        # 打印结果
        logger.info("\n查询结果:")
        logger.info(json.dumps(result, ensure_ascii=False))
        return response.success(json.dumps(result, ensure_ascii=False))
    except Exception as e:
        logger.error(f"获取美团用户ID失败: {str(e)}")
        return response.fail(f"获取美团用户ID失败: {str(e)}")
    
@app.route("/weiwei/get_mt_userid_by_mis_by_lion", methods=['POST'])
def get_mt_userid_by_mis_by_lion():
    try:
        request_json = request.get_json()
        mis_id = request_json.get("mis_id", "")
        misId_userId_map = lion_config.lion_client.get_value("id_maps")
        try:
            misId_userId_map = json.loads(misId_userId_map)
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析错误: {e}")
            misId_userId_map = {}
        
        mt_user_id = misId_userId_map.get(mis_id)
        # 获取信息
        if not mt_user_id:
            return response.fail(f"获取美团用户ID失败: {mis_id} 不存在lion配置中")
        return response.success({
            "mt_user_id": mt_user_id
        })

    except Exception as e:
        logger.error(f"获取美团用户ID失败: {str(e)}")
        return response.fail(f"获取美团用户ID失败: {str(e)}")
    
@app.route("/weiwei/compare/ask_the_ai_by_batch", methods=['POST'])
def ask_the_ai_by_batch_separate_thread_api():
    """
    批量请求AI接口
    
    该接口用于批量请求AI接口，并记录结果到mysql。

    该接口进入后会执行初始化操作，然后新开一个进程单独执行相关操作，不会再在主接口内部执行逻辑
    
    参数:
        batch_job_id: 批处理任务ID (必需)
        prompt_list: 提示列表 (必需)
        start_time: 开始执行时间，格式为 "YYYY-MM-DD HH:MM:SS" (可选)
        其他参数: 根据需要传递给各个API的参数
    """

    set_trace_id(request)
    request_json = request.get_json()
    batch_job_id = request_json.pop("batch_job_id", "")
    repeat_mode = request_json.pop("repeat_mode", "no_repeat")
    job_name = request_json.pop("job_name", "")
    
    if not batch_job_id:
        return response.fail("缺少batch_job_id")
    
    prompt_list = request_json.pop("prompt_list", [])
    num_of_prompts = len(prompt_list)
    
    # 提取开始时间参数
    start_time = request_json.pop("start_time", None)
    
    # 如果设置了开始时间，检查是否早于当前时间
    if start_time:
        try:
            scheduled_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            current_time = datetime.now()
            
            if scheduled_time <= current_time:
                return response.fail(f"开始时间不能早于或等于当前时间。当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}, 设置时间: {start_time}")
        except ValueError:
            return response.fail("开始时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式")

    ini_res = initialize_batch_job(batch_job_id, prompt_list, start_time)
    if ini_res:
        return response.fail(f"初始化批处理任务失败, {ini_res}")
    
    # 等待3秒后再开始后续进程
    time.sleep(3)
    
    
    generator = ThreadedGenerator()
    Thread(target=ask_the_ai_by_batch_separate_thread, args=(generator, prompt_list, batch_job_id, request_json, num_of_prompts, start_time, repeat_mode, job_name)).start()


    
    
    return Response(stream_with_context(generator), 
                    content_type='application/x-ndjson', 
                    headers={'X-Accel-Buffering': 'no', 
                             'Cache-Control': 'no-cache, no-store, must-revalidate', 
                             'Pragma': 'no-cache', 
                             'Expires': '0'})


@app.route("/weiwei/compare/batch_job_result_search", methods=['POST'])
def batch_job_result_search():
    """
    批量请求AI接口结果查询
    
    该接口用于查询批量请求AI接口的结果。
    
    传入需要查询的batch job id（sub_task id和api_name可选），返回对应id匹配搜索的结果
    """
    try:
        request_json = request.get_json()
        batch_job_id = request_json.get("batch_job_id", "")
        sub_task_id = request_json.get("sub_task_id", "")
        api_name = request_json.get("api_name", "")
        if not batch_job_id:
            return response.fail("缺少batch_job_id")
        
        result = get_batch_job_result(batch_job_id, sub_task_id, api_name)

        if result:
            return response.success(result)
        else:
            return response.fail("没有找到结果")
    except Exception as e:
        logger.error(f"批量请求AI接口失败: {e}")
        return response.fail(f"批量请求AI接口失败: {e}")
    


@app.route("/weiwei/compare/create_batch_job_id", methods=['POST'])
def create_batch_job_id():
    try:
        request_json = request.get_json()
        logger.info(f"创建批处理任务: {request_json}")
        creator_mis_id = request_json.get("mis_id", "")
        job_name = request_json.get("job_name", "")
        repeat_mode = request_json.get("repeat_mode", "no_repeat")
        if not creator_mis_id:
            return response.fail("缺少creator_mis_id")
        created_time = datetime.now().strftime('%Y-%m-%d-%H-%M-%S')
        if repeat_mode == "every_day":
            batch_job_id = f"{creator_mis_id}_batch_{created_time}?mode=every_day"
        elif repeat_mode == "every_week":
            batch_job_id = f"{creator_mis_id}_batch_{created_time}?mode=every_week"
        else:
            batch_job_id = f"{creator_mis_id}_batch_{created_time}?mode=no_repeat"
        doc = {
            "doc":{
                "job_name": job_name,
                "repeat_mode": repeat_mode,
                "created_time": created_time,
                "creator_mis_id": creator_mis_id,
                "completed_tasks":0,
                "total_tasks":0,
                "status": "未启动"
            },
            "doc_as_upsert": True
        }
        logger.info(f"创建批处理任务: {doc}, job_name: {job_name}")
        batch_job_id_index = lion_config.lion_client.get_value_with_default("batch_job_id_index", "batch_job_status")
        res = es_client.upsert_data(batch_job_id_index, batch_job_id, doc)
        if not res:
            return response.fail("生成batch_job_id失败")

        return response.success({
            "batch_job_id": batch_job_id,
            "job_name": job_name,
            "created_time": created_time

        })
    except Exception as e:
        logger.error(f"生成batch_job_id失败: {e}")
        return response.fail(f"生成batch_job_id失败: {e}")

# 添加获取最近批处理任务ID的函数

# 添加对应的API路由
@app.route("/weiwei/compare/get_recent_batch_job_ids", methods=['POST'])
def get_recent_batch_job_ids_api():
    """
    获取最近的批处理任务ID列表API
    
    请求参数：
        limit: 最大返回数量（可选，默认1000）
        mis_id: 创建者mis_id（可选，默认""）
    返回：
        按创建时间从近到远排序的batch_job_id列表
    """
    try:
        request_json = request.get_json()
        limit = request_json.get("limit", 1000)
        mis_id = request_json.get("mis_id", "")
        # 限制最大返回数量，防止请求过大
        if limit > 10000:
            limit = 10000
            
        batch_job_ids = get_recent_batch_job_ids(limit, mis_id)
        
        return response.success({
            "total": len(batch_job_ids),
            "batch_job_ids": batch_job_ids
        })
    except Exception as e:
        logger.error(f"获取最近批处理任务ID列表失败: {e}")
        return response.fail(f"获取最近批处理任务ID列表失败: {e}")
    
@app.route("/weiwei/compare/get_recent_batch_job_info", methods=['POST'])
def get_recent_batch_job_info_api():
    """
    获取最近批处理任务的详细信息
    """
    try:
        request_json = request.get_json()
        limit = request_json.get("limit", 1000)
        mis_id = request_json.get("mis_id", "")
        if limit > 10000:
            limit = 10000
        batch_job_info = get_recent_batch_job_info(limit, mis_id)
        return response.success(batch_job_info)
    except Exception as e:
        logger.error(f"获取最近批处理任务信息失败: {e}")
        return response.fail(f"获取最近批处理任务信息失败: {e}")

@app.route("/weiwei/compare/get_batch_job_detail", methods=['POST'])
def get_batch_job_detail_api():
    """
    获取批处理任务的详细信息
    """
    try:
        request_json = request.get_json()
        batch_job_id = request_json.get("batch_job_id", "")
        batch_job_detail = get_batch_job_detail(batch_job_id)
        return response.success(batch_job_detail)
    except Exception as e:
        logger.error(f"获取批处理任务详细信息失败: {e}")
        return response.fail(f"获取批处理任务详细信息失败: {e}")    
    
@app.route("/weiwei/compare/update_user_feedback", methods=['POST'])
def update_user_feedback_api():
    """
    更新用户反馈
    """
    try:
        request_json = request.get_json()
        batch_job_id = request_json.get("batch_job_id", "")
        sub_task_id = request_json.get("sub_task_id", "")
        user_comment = request_json.get("user_comment", None)
        user_score = request_json.get("user_score", None)
        res = ai_batch_response.update_user_feedback(batch_job_id, sub_task_id, user_comment, user_score)
        if res:
            return response.success("更新用户反馈成功")
        else:
            return response.fail("更新用户反馈失败")
    except Exception as e:
        logger.error(f"更新用户反馈失败: {e}")
        return response.fail(f"更新用户反馈失败: {e}")

@app.route("/weiwei/compare/stop_batch_job", methods=['POST'])
def stop_batch_job():
    """
    停止批处理任务
    """
    try:
        request_json = request.get_json()
        batch_job_id = request_json.get("batch_job_id", "")
        stop_res = batch_job_stop_status.insert_stop_id(batch_job_id)
        if stop_res:
            return response.success("插入停止批处理任务成功，任务将在结束当前对话后结束")
        else:
            return response.fail("插入停止批处理任务失败")
    except Exception as e:
        logger.error(f"插入停止批处理任务失败: {e}")
        return response.fail(f"插入停止批处理任务失败: {e}")

@app.route("/weiwei/compare/set_prompt_templates", methods=['POST'])
def set_prompt_templates_api():
    """
    设置prompt模板
    """
    try:
        request_json = request.get_json()
        mis_id = request_json.get("mis_id", "")
        prompt_name = request_json.get("prompt_name", "")
        prompt_list = request_json.get("prompt_list", [])
        if not mis_id:
            return response.fail("缺少mis_id")
        if not prompt_name:
            return response.fail("缺少prompt_name")
        if not prompt_list:
            return response.fail("缺少prompt_list")
        res = set_prompt_templates(mis_id, prompt_name, prompt_list)
        return response.success(res)
    except Exception as e:
        logger.error(f"设置prompt模板失败: {e}")
        return response.fail(f"设置prompt模板失败: {e}")

@app.route("/weiwei/compare/get_prompt_templates", methods=['POST'])
def get_prompt_templates_api():
    """
    获取prompt模板
    """
    try:
        request_json = request.get_json()
        mis_id = request_json.get("mis_id", "")
        if not mis_id:
            return response.fail("缺少mis_id")
        prompt_name = request_json.get("prompt_name", "")
        res = get_prompt_templates(mis_id, prompt_name)
        return response.success(res)
    except Exception as e:
        logger.error(f"获取prompt模板失败: {e}")
        return response.fail(f"获取prompt模板失败: {e}")
    
@app.route("/weiwei/compare/delete_prompt_templates", methods=['POST'])
def delete_prompt_templates_api():
    """
    删除prompt模板
    """
    try:
        request_json = request.get_json()
        mis_id = request_json.get("mis_id", "")
        if not mis_id:
            return response.fail("缺少mis_id")
        prompt_name = request_json.get("prompt_name", "")   
        if not prompt_name:
            return response.fail("缺少prompt_name")
        res = delete_prompt_templates(mis_id, prompt_name)
        return response.success(res)
    except Exception as e:
        logger.error(f"删除prompt模板失败: {e}")
        return response.fail(f"删除prompt模板失败: {e}")
if __name__ == '__main__':
    logger.info("---------------- service start ----------\n\n")
    Cat.init_cat(APP_KEY)
    main()

