from configs.config import CURRENT_ENV, APP_KEY, CELL
from utils.lion_connection import LionClient
import threading
from utils.logger import logger
import json

LION_CLIENT_ID = "xiaomei_toolexecute_admin"
LION_SECRET = "XFDCTVU8SC"

lion_client = LionClient(client_id=LION_CLIENT_ID,
                         client_secret=LION_SECRET,
                         env=CURRENT_ENV,   
                         app_name=APP_KEY)


AI_MODEL = lion_client.get_value_with_cell("weiwei.ai_model", CELL)
INTENT_MODEL = lion_client.get_value("weiwei.intent_model")
MODEL_MAP = json.loads(lion_client.get_value("weiwei.model_map"))

# 管理员列表，默认为一个空字符串，由Lion配置更新
ADMIN_LIST = lion_client.get_value_with_default("weiwei.admin_list", "")

# 白名单列表，默认为一个空字符串，由Lion配置更新
WHITE_LIST = lion_client.get_value_with_default("weiwei.white_list", "")

# 以下是需要按模型配置的参数(key)
MODEL_CONFIG = json.loads(lion_client.get_value(f"weiwei.{AI_MODEL}"))
EXTRA_INFO = MODEL_CONFIG.get("weiwei.extra_info")
SYSTEM_PROMPT = MODEL_CONFIG.get("weiwei.system_prompt")
CONSUMER_THOUGHT_CHAINS_MAIN = MODEL_CONFIG.get("weiwei.consumer_thought_chains_main")
CHAT_PROMPT_TEMPLATE = MODEL_CONFIG.get("weiwei.chat_prompt_template")
VEX_SEARCH_NUM = int(MODEL_CONFIG.get("weiwei.vex_search_num"))
MERCHANTS_SUP_NUM = int(MODEL_CONFIG.get("weiwei.merchants_sup_num"))
INSIGHT_TOPK = int(MODEL_CONFIG.get("weiwei.insight_topk"))
ORDER_CACHE_NUM = int(MODEL_CONFIG.get("weiwei.order_cache_num"))
ES_MATCH_NUM = int(MODEL_CONFIG.get("weiwei.es.match_num"))
INTENT_NUM = int(MODEL_CONFIG.get("weiwei.intent_num"))
AI_ORDER_LIMIT = int(MODEL_CONFIG.get("weiwei.ai_order_limit")) or 30  # 默认发送30单给AI
COT_MODEL = MODEL_CONFIG.get("weiwei.cot_model")
COT_PROMPT = MODEL_CONFIG.get("weiwei.cot_prompt")
COT_FUNC = MODEL_CONFIG.get("weiwei.cot_func")
try:
    BASE_FUNC = MODEL_CONFIG.get("weiwei.base_func")
except:
    BASE_FUNC = "base"
TOKEN_CHUNK = int(MODEL_CONFIG.get("weiwei.token_chunk"))

# 与模型无关的参数
INDEX_INGREDIENT_ANALYSIS = lion_client.get_value("weiwei.index_ingredient_analysis") # "weiwei_ingredient_analysis"
INDEX_SHOP_FOOD_INFO = lion_client.get_value("weiwei.index_shop_food_info") # "weiwei_merchants_info"
INDEX_INTENT_KEYWORD = lion_client.get_value("weiwei.index_intent_keyword") # "weiwei_intent_map"
ES_ACCESS_KEY = lion_client.get_value("weiwei.es.key")
RECOMMEND_TOPIC = lion_client.get_value("weiwei.recommend_topic")
MIS_USER_ID_MAP = lion_client.get_value("misId_mtuserId_map")
INTENT_PROMPT = lion_client.get_value("weiwei.intent_prompt")
INTENT_PROMPT_FORMAT = lion_client.get_value("weiwei.intent_prompt_format")
REDIS_UPDATE_PER_SECONDS = int(lion_client.get_value("weiwei.redis_update_per_seconds"))
REDIS_INIT_TIME_RANGE = int(lion_client.get_value("weiwei.redis_init_time_range"))
ORG_CLIENT_ID = lion_client.get_value("weiwei.org_client_id")
ORG_OBJECT_CODE = lion_client.get_value("weiwei.org_object_code")
TTS_APP_KEY = lion_client.get_value("weiwei.tts.app_key")
TTS_SECRET = lion_client.get_value("weiwei.tts.secret")
TTS_MAX_RETRY_NUM = int(lion_client.get_value_with_default("weiwei.tts.max_retry_num", 5))
TTS_TIME_BETWEEN_RETRIEVE = float(lion_client.get_value_with_default("weiwei.tts.time_between_retrieve", 2.0))
REFLECT_PROMPT = lion_client.get_value("weiwei.reflect_prompt")
DEEPTHINKING_MODELS = json.loads(lion_client.get_value("weiwei.deepthinking_models"))
PRODUCT_PICTURE_URL = lion_client.get_value("weiwei.product_picture_url")
ES_CONFIGS_INDEX = lion_client.get_value("weiwei.es_configs_index")
MAINDB_INDEX = lion_client.get_value("weiwei.maindb_index")

def update_model_config():
    global MODEL_CONFIG, EXTRA_INFO, SYSTEM_PROMPT, CONSUMER_THOUGHT_CHAINS_MAIN, CHAT_PROMPT_TEMPLATE, \
            VEX_SEARCH_NUM, MERCHANTS_SUP_NUM, INSIGHT_TOPK, ORDER_CACHE_NUM, ES_MATCH_NUM, INTENT_NUM, AI_ORDER_LIMIT, \
            COT_MODEL, COT_PROMPT, COT_FUNC, BASE_FUNC, TOKEN_CHUNK

    # 以下是需要按模型配置的参数(key)
    MODEL_CONFIG = json.loads(lion_client.get_value(f"weiwei.{AI_MODEL}"))
    EXTRA_INFO = MODEL_CONFIG.get("weiwei.extra_info")
    SYSTEM_PROMPT = MODEL_CONFIG.get("weiwei.system_prompt")
    CONSUMER_THOUGHT_CHAINS_MAIN = MODEL_CONFIG.get("weiwei.consumer_thought_chains_main")
    CHAT_PROMPT_TEMPLATE = MODEL_CONFIG.get("weiwei.chat_prompt_template")
    VEX_SEARCH_NUM = int(MODEL_CONFIG.get("weiwei.vex_search_num"))
    MERCHANTS_SUP_NUM = int(MODEL_CONFIG.get("weiwei.merchants_sup_num"))
    INSIGHT_TOPK = int(MODEL_CONFIG.get("weiwei.insight_topk"))
    ORDER_CACHE_NUM = int(MODEL_CONFIG.get("weiwei.order_cache_num"))
    ES_MATCH_NUM = int(MODEL_CONFIG.get("weiwei.es.match_num"))
    INTENT_NUM = int(MODEL_CONFIG.get("weiwei.intent_num"))
    AI_ORDER_LIMIT = int(MODEL_CONFIG.get("weiwei.ai_order_limit")) or 30  # 默认发送30单给AI
    COT_MODEL = MODEL_CONFIG.get("weiwei.cot_model")
    COT_PROMPT = MODEL_CONFIG.get("weiwei.cot_prompt")
    COT_FUNC = MODEL_CONFIG.get("weiwei.cot_func")
    try:
        BASE_FUNC = MODEL_CONFIG.get("weiwei.base_func")
    except:
        BASE_FUNC = "base"
    TOKEN_CHUNK = int(MODEL_CONFIG.get("weiwei.token_chunk"))

def update_lion_config():
    """
    定时更新lion配置，需要自动更新的lion配置需要在此函数中登记
    注意：需要自动更新的变量，不能使用from configs.lion_config import xxx 的方式直接引用
    需要使用 from configs import lion_config , 然后 lion_config.xxx 的方式访问
    """
    update_time = 60

    global AI_MODEL, INTENT_MODEL, MODEL_CONFIG, EXTRA_INFO, SYSTEM_PROMPT, \
        CONSUMER_THOUGHT_CHAINS_MAIN, CHAT_PROMPT_TEMPLATE, VEX_SEARCH_NUM, \
        MERCHANTS_SUP_NUM, INSIGHT_TOPK, ORDER_CACHE_NUM, ES_MATCH_NUM, \
        INTENT_NUM, AI_ORDER_LIMIT, INDEX_INGREDIENT_ANALYSIS, \
        INDEX_SHOP_FOOD_INFO, INDEX_INTENT_KEYWORD, ES_ACCESS_KEY, RECOMMEND_TOPIC, MIS_USER_ID_MAP, \
        INTENT_PROMPT, INTENT_PROMPT_FORMAT, REDIS_UPDATE_PER_SECONDS, REDIS_INIT_TIME_RANGE, \
        ORG_CLIENT_ID, ORG_OBJECT_CODE, TTS_APP_KEY, TTS_SECRET, COT_MODEL, COT_PROMPT, COT_FUNC, TOKEN_CHUNK, \
        MODEL_MAP, TTS_MAX_RETRY_NUM, TTS_TIME_BETWEEN_RETRIEVE, REFLECT_PROMPT, DEEPTHINKING_MODELS, PRODUCT_PICTURE_URL,\
        ES_CONFIGS_INDEX, MAINDB_INDEX, ADMIN_LIST, WHITE_LIST

    try:
        MODEL_MAP = json.loads(lion_client.get_value("weiwei.model_map"))
        INTENT_MODEL = lion_client.get_value("weiwei.intent_model")

        # 与模型无关的参数
        INDEX_INGREDIENT_ANALYSIS = lion_client.get_value("weiwei.index_ingredient_analysis") # "weiwei_ingredient_analysis"
        INDEX_SHOP_FOOD_INFO = lion_client.get_value("weiwei.index_shop_food_info") # "weiwei_merchants_info"
        INDEX_INTENT_KEYWORD = lion_client.get_value("weiwei.index_intent_keyword") # "weiwei_intent_map"
        ES_ACCESS_KEY = lion_client.get_value("weiwei.es.key")
        RECOMMEND_TOPIC = lion_client.get_value("weiwei.recommend_topic")
        MIS_USER_ID_MAP = lion_client.get_value("misId_mtuserId_map")
        INTENT_PROMPT = lion_client.get_value("weiwei.intent_prompt")
        REDIS_UPDATE_PER_SECONDS = int(lion_client.get_value("weiwei.redis_update_per_seconds"))
        REDIS_INIT_TIME_RANGE = int(lion_client.get_value("weiwei.redis_init_time_range"))
        ORG_CLIENT_ID = lion_client.get_value("weiwei.org_client_id")
        ORG_OBJECT_CODE = lion_client.get_value("weiwei.org_object_code")
        TTS_APP_KEY = lion_client.get_value("weiwei.tts.app_key")
        TTS_SECRET = lion_client.get_value("weiwei.tts.secret")
        INTENT_PROMPT_FORMAT = lion_client.get_value("weiwei.intent_prompt_format")
        TTS_MAX_RETRY_NUM = int(lion_client.get_value_with_default("weiwei.tts.max_retry_num", 5))
        TTS_TIME_BETWEEN_RETRIEVE = float(lion_client.get_value_with_default("weiwei.tts.time_between_retrieve", 1.0))
        REFLECT_PROMPT = lion_client.get_value("weiwei.reflect_prompt")
        DEEPTHINKING_MODELS = json.loads(lion_client.get_value("weiwei.deepthinking_models"))
        PRODUCT_PICTURE_URL = lion_client.get_value("weiwei.product_picture_url")
        ES_CONFIGS_INDEX = lion_client.get_value("weiwei.es_configs_index")
        MAINDB_INDEX = lion_client.get_value("weiwei.maindb_index")
        
        # 更新管理员列表
        ADMIN_LIST = lion_client.get_value_with_default("weiwei.admin_list", "")
        
        # 更新白名单列表
        WHITE_LIST = lion_client.get_value_with_default("weiwei.white_list", "")
        logger.info("Auto Update lion config Success.")

    except Exception as e:
        logger.info(f"auto update lion config failed: {e}")
    # 设置10秒后再次调用此函数
    threading.Timer(update_time, update_lion_config).start()


update_lion_config()