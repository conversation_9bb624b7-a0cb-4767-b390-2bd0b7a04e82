import json
import requests
import json
# CodeBase Files
from es.es_client import search, update_doc, delete_doc
from utils.position import get_merchant_position
from utils.utils import time2UnixTimestamp, unixTimestamp2Time
from service.order import get_order_summary, get_order_details
from configs.lion_config import lion_client, ES_CONFIGS_INDEX, MAINDB_INDEX
from utils.logger import logger
from service.merchant_search_api.merchant_client import get_merchant_info, merchant_field_mapping

def get_index_configs(index:str)->None|dict:
    query = {
        "query": {
            "term": {
                "index": index
            }
        }
    }
    hits = search(ES_CONFIGS_INDEX, query)['hits']['hits']
    if len(hits) == 0:
        logger.info(f"ES索引{index}不存在，请尝试初始化后再使用,具体方法请查看学城文档: https://km.sankuai.com/collabpage/2708859766")
        return None
    return json.loads(hits[0]['_source']['configs'])

# 查询某个索引的数目
def get_order_num_in_es_database(index:str)->None|dict:
    configs = get_index_configs(index)
    if configs is None:
        return None
    return configs.get("num", 0)

# 获取索引的doc_id_template
def get_doc_id_template(index:str)->str:
    configs = get_index_configs(index)
    if configs is None:
        # logger.error(f"ES索引{index}不存在，请尝试使用")
        return None
    return configs.get("doc_id_template", None)

# 初始化一个索引的config信息
def initialize_index_configs(index:str):
    result = get_index_configs(index)
    if result is None:
        configs = {
            "num": 0, # 索引的数目
            "doc_id_template": index + "-{ngium}"
        }
        update_info = {
            "index": index,
            "doc_id": index,
            "configs": json.dumps(configs)
        }
        update_body = {
            "doc_as_upsert": True,
            "doc": update_info
        }
        update_doc(ES_CONFIGS_INDEX, index, update_body)
        logger.info(f"初始化索引{index}的config信息成功, 初始化的信息为: {update_info}。")
    else:
        logger.info(f"请勿重复初始化索引{index}的config信息, 如需强制重制，请手动操作。")

def set_index_configs(index:str, new_configs:dict):
    configs = get_index_configs(index)
    if configs is None:
        initialize_index_configs(index) # 初始化一个基本的索引
        configs = get_index_configs(index)

    for key, value in new_configs.items():
        configs[key] = value

    update_info = {
        "index": index,
        "doc_id": index,
        "configs": json.dumps(configs)
    }
    update_body = {
        "doc_as_upsert": True,
        "doc": update_info
    }
    update_doc(ES_CONFIGS_INDEX, index, update_body)

def generate_order_sentence(time, merchant_name: str, price: str, food_name_list: list[str], food_price:list[str], position: str, merchant_type: str)->str:
    ret = f"<时间:{time}> <店铺:{merchant_name}> <位置:{position}> <类型:{merchant_type}> <总价:{price}>"
    for food, price in zip(food_name_list, food_price):
        ret += f" <{food} {price}元> "
    return ret

def db_update(begin_time, end_time, offset, limit, userIdList:list[int]=None, access_token:str=None):
    # 时间转换
    startTime = time2UnixTimestamp(begin_time)
    endTime = time2UnixTimestamp(end_time)
    doc_template = get_doc_id_template(MAINDB_INDEX)

    # 获取用于构建数据库的用户UserId
    if userIdList is None:
        userIdList = [user_id for _, user_id in json.loads(lion_client.get_value("misId_mtuserId_map")).items()]

    
    total_num = get_order_num_in_es_database(MAINDB_INDEX)+1 # 当前数据库中的条目数目
    update_num = 0
    demo_food = []
    # 遍历用户
    for userid in userIdList:
        # 获取用户订单概览
        orderNum, orderList = get_order_summary(access_token, userid, startTime, endTime, offset, limit) # 具体的订单，主要获取订单Id
        logger.info(f"userid: {userid} 获取订单概览成功, {orderList}")
        # 遍历订单
        for order in orderList:
            # 获取订单详情
            logger.info(f"userid: {userid} 获取订单详情, order: {order}")
            orderDetails = get_order_details(access_token, userid, order["orderId"]) # 具体的订单详情
            logger.info(f"userid: {userid} 获取订单详情成功, orderDetails: {orderDetails}")
            merchant_name = order["title"] # poi_name
            logger.info(f"userid: {userid} 获取商家名称成功, merchant_name: {merchant_name}")
            info = json.loads(order["info"])
            logger.info(f"userid: {userid} 获取订单信息成功, info: {info}")
            time = info.get("info1", "")
            logger.info(f"userid: {userid} 获取订单时间成功, time: {time}")
            price = info.get("info2", "")
            logger.info(f"userid: {userid} 获取订单价格成功, price: {price}")
            poiId = order["poiId"] # poiId
            logger.info(f"userid: {userid} 获取商家ID成功, poiId: {poiId}")
            merchant_info = get_merchant_info(poiId)
            logger.info(f"userid: {userid} 获取商家信息成功, merchant_info: {merchant_info}")

            
            food_type = merchant_info.get("WM_POI_FIELD_TAG_IDS") # 食品类型
            logger.info(f"userid: {userid} 获取食品类型成功, food_type: {food_type}")
            address = merchant_info.get("WM_POI_FIELD_ADDRESS") # 地址
            logger.info(f"userid: {userid} 获取地址成功, address: {address}")
            longitude = merchant_info.get("WM_POI_FIELD_LONGITUDE") # 经度
            logger.info(f"userid: {userid} 获取经度成功, longitude: {longitude}")
            latitude = merchant_info.get("WM_POI_FIELD_LATITUDE") # 纬度
            logger.info(f"userid: {userid} 获取纬度成功, latitude: {latitude}")
            status = (merchant_info.get("WM_POI_FIELD_STATUS")==1) # 状态
            logger.info(f"userid: {userid} 获取状态成功, status: {status}")


            foodIdList = [food["wm_food_id"] for food in orderDetails] # foodId
            logger.info(f"userid: {userid} 获取食品ID成功, foodIdList: {foodIdList}")
            spuIdList = [food["wm_food_spu_id"] for food in orderDetails] # spuId
            logger.info(f"userid: {userid} 获取SPU ID成功, spuIdList: {spuIdList}")
            foodNameList = [food["food_name"] for food in orderDetails] # foodName
            logger.info(f"userid: {userid} 获取食品名称成功, foodNameList: {foodNameList}")
            foodPrice = [food["food_price"] for food in orderDetails]
            logger.info(f"userid: {userid} 获取食品价格成功, foodPrice: {foodPrice}")
            # logger.info(f"userid: {userid} 获取订单详情成功, foodIdList: {foodIdList}, spuIdList: {spuIdList}, foodNameList: {foodNameList}, foodPrice: {foodPrice}")
            for foodId, spuId, foodName, foodPrice in zip(foodIdList, spuIdList, foodNameList, foodPrice):
                db_info = search(MAINDB_INDEX, query = {
                                        "query":{
                                            "bool":{
                                                "must":[
                                                    {"term":{"poi_id": poiId}},
                                                    {"term":{"spu_id": spuId}}
                                                ]
                                            }
                                        },
                                        "size":1
                                    })
                
                hits = db_info['hits']['hits']
                if len(hits) != 0:
                    update_info = hits[0]['_source']
                    update_info["order_num"] += 1
                    update_body = {
                        "doc_as_upsert": True,
                        "doc": update_info
                    }
                    update_doc(MAINDB_INDEX, update_info["doc_id"], update_body)
                    continue
                    # continue
                else:
                    sentence = generate_order_sentence(time, merchant_name, foodPrice, [foodName], [foodPrice], address, food_type) # 生成订单描述，用于Vex召回
                    doc_id = doc_template.format(num=total_num)
                    update_info = {
                                "poi_name.keyword": merchant_name,
                                "poi_name.text": merchant_name,
                                "food_name.keyword": foodName,
                                "food_name.text": foodName,
                                "poi_id": poiId,
                                "spu_id": spuId,
                                "food_id": foodId,
                                "order_num": 1,
                                "validation": status,
                                "position.longtitude": longitude,
                                "position.latitude": latitude,
                                "extra":"None",
                                "content": sentence,
                                "doc_id": doc_id
                            }
                    update_body = {
                            "doc_as_upsert": True,
                            "doc": update_info
                        }
                    update_doc(MAINDB_INDEX, doc_id, update_body)
                    total_num+=1 # 数据库条目数加一
                if len(demo_food) < 10:
                    demo_food.append(update_info)
                update_num += 1
    set_index_configs(MAINDB_INDEX, {"num": total_num}) 
    return demo_food
            
   