import base64
from utils.logger import logger
import requests
from opensearchpy import OpenSearch
from configs.config import APP_KEY, CURRENT_ENV
from configs import lion_config


def generate_token(appkey, accesskey):
    """生成Basic Auth的TOKEN"""
    s = f"{appkey}:{accesskey}"
    return base64.b64encode(s.encode()).decode()


def call_openapi(appkey, accesskey, cluster_name):
    """调用OpenAPI获取集群节点信息"""
    token = generate_token(appkey, accesskey)
    headers = {
        "Authorization": f"Basic {token}"
    }
    url = f"{openapi_url}/clusters/{cluster_name}/nodes"
    response = requests.get(url, headers=headers)
    response.raise_for_status()  # 如果请求失败则抛出异常

    data = response.json()
    if data["code"] != 0:
        raise Exception(f"Error fetching nodes: {data['message']}")

    # 提取节点的HTTP地址
    nodes = [{"host": node['ip'], "port": node['httpPort'], "scheme": "http"} for node in data["data"]]
    return nodes


# 配置参数
cluster_name = "eaglenode_us-es" if CURRENT_ENV == "test" else "ai_aicc_default"
# cluster_name = "nlp_xiaomeies_default"
# cluster_name = "ai_aicc_default"
openapi_url = "http://openapi.eagle.test.sankuai.com/openapi" if CURRENT_ENV == "test" else "http://eagleweb.sankuai.com/openapi/"
# openapi_url = "http://eagleweb.sankuai.com/openapi/"

# 获取节点列表
nodes = call_openapi(APP_KEY, lion_config.ES_ACCESS_KEY, cluster_name)
# python3.10和Elasticsearch的7.10.0版本不兼容，因此使用OpenSearch
client = OpenSearch(
    hosts=nodes,
    http_auth=(APP_KEY, lion_config.ES_ACCESS_KEY),
    use_ssl=False,
    verify_certs=False,
    sniff_on_start=True,
    sniff_on_connection_fail=True,
    sniffer_timeout=60
)

INDEX_INGREDIENT_ANALYSIS = "weiwei_ingredient_analysis"
INDEX_MERCHANT_INFO = "maindb_v1"
INDEX_MERCHANT_INFO_HISTORY = "weiwei_merchants_info_history"


def upsert_data(index_name, doc_id, doc):
    try:
        response = client.update(index=index_name, id=doc_id, body=doc)
    except Exception as e:
        logger.error(f"插入{index_name}出错: {str(e)}")
        response = None
    return response



def update_doc(index_name, doc_id, update_body):
    """更新文档的指定字段
    
    Args:
        index_name (str): 索引名称
        doc_id (str): 文档ID
        update_body (dict): 更新内容，格式为 {"doc": {"field": "new_value"}}
        
    Returns:
        dict: 更新操作的响应
    """
    try:
        response = client.update(index=index_name, id=doc_id, body=update_body)
        return response
    except Exception as e:
        logger.error(f"更新{index_name}文档出错: {str(e)}")
        return None



def search(index_name, query):
    try:
        response = client.search(index=index_name, body=query)
    except Exception as e:
        logger.error(f"搜索{index_name}出错: {str(e)}")
        response = None
    return response


def delete_doc(index_name, doc_id):
    try:
        response = client.delete(index=index_name, id=doc_id)
    except Exception as e:
        logger.error(f"删除{index_name}出错: {str(e)}")
        response = None

    return response

def get_num(index_name):
    try:
        response = client.count(index=index_name)
    except Exception as e:
        logger.error(f"获取{index_name}出错: {str(e)}")
        response = None

    return response

async def upsert_data_async(index_name, doc_id, doc):
    try:
        response = client.update(index=index_name, id=doc_id, body=doc)
    except Exception as e:
        logger.error(f"插入{index_name}出错: {str(e)}")
        response = None
    return response


async def search_async(index_name, query):
    try:
        response = client.search(index=index_name, body=query)
    except Exception as e:
        logger.error(f"搜索{index_name}出错: {str(e)}")
        response = None
    return response


async def delete_doc_async(index_name, doc_id):
    try:
        response = client.delete(index=index_name, id=doc_id)
    except Exception as e:
        logger.error(f"删除{index_name}出错: {str(e)}")
        response = None

    return response


if __name__ == "__main__":
    print(search("compare_history", {"query": {"match": {"_id": "testid"}}}))