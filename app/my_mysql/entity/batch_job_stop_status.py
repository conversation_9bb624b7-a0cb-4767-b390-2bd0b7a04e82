from my_mysql import sql_client
from sqlalchemy import Table, Column, String, MetaData, select, and_, delete
from utils.logger import logger

"""
CREATE TABLE `batch_job_stop_status` (
  `batch_job_id` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '批处理任务ID',
  PRIMARY KEY (`batch_job_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='批处理任务停止状态表'
"""

# 定义表
batch_job_stop_status = Table(
    'batch_job_stop_status', MetaData(),
    Column('batch_job_id', String(128), primary_key=True, comment='批处理任务ID')
)

def insert_stop_id(batch_job_id: str):
    """
    插入需要停止的批处理任务ID
    
    Args:
        batch_job_id (str): 批处理任务ID
        
    Returns:
        bool: 插入成功返回True，失败返回False
    """
    ins = batch_job_stop_status.insert().values(
        batch_job_id=batch_job_id
    )
    try:
        sql_client.insert_return_id(ins)
        logger.info(f"成功插入批处理任务停止状态 - batch_job_id: {batch_job_id}")
        return True
    except Exception as e:
        logger.error(f"插入批处理任务停止状态失败: {e}")
        return False

def delete_stop_id(batch_job_id: str):
    """
    完成后删除需要停止的批处理任务id
    
    Args:
        batch_job_id (str): 批处理任务ID
        
    Returns:
        bool: 删除成功返回True，失败返回False
    """
    stmt = batch_job_stop_status.delete().where(
        batch_job_stop_status.c.batch_job_id == batch_job_id
    )
    try:
        result = sql_client.update(stmt)
        affected_rows = result.rowcount if result else 0
        if affected_rows > 0:
            logger.info(f"成功删除批处理任务停止状态 - batch_job_id: {batch_job_id}")
            return True
        else:
            logger.warning(f"未找到要删除的记录 - batch_job_id: {batch_job_id}")
            return False
    except Exception as e:
        logger.error(f"删除批处理任务停止状态失败: {e}")
        return False

def is_job_need_stop(batch_job_id: str):
    """
    查询任务是否是需要停止的
    
    Args:
        batch_job_id (str): 批处理任务ID
        
    Returns:
        bool: 任务已停止返回True，否则返回False
    """
    stmt = select(batch_job_stop_status).where(
        batch_job_stop_status.c.batch_job_id == batch_job_id
    )
    try:
        result = sql_client.select_one(stmt)
        return result is not None
    except Exception as e:
        logger.error(f"查询批处理任务停止状态失败: {e}")
        return False

def get_all_jobs_need_stop():
    """
    获取所有需要停止的批处理任务ID
    
    Returns:
        list: 需要停止的批处理任务ID列表
    """
    stmt = select(batch_job_stop_status.c.batch_job_id)
    try:
        results = sql_client.select_many(stmt)
        return [row[0] for row in results] if results else []
    except Exception as e:
        logger.error(f"获取所有需要停止的批处理任务失败: {e}")
        return []

# 测试函数
def test_batch_job_stop_status():
    print("==== 测试插入 ====")
    insert_stop_id("test_batch_job_1")
    insert_stop_id("test_batch_job_2")
    
    print("==== 测试查询 ====")
    print(f"test_batch_job_1 是否需要停止: {is_job_need_stop('test_batch_job_1')}")
    print(f"test_batch_job_3 是否需要停止: {is_job_need_stop('test_batch_job_3')}")
    
    print("==== 测试获取所有 ====")
    all_need_stop = get_all_jobs_need_stop()
    print(f"所有需要停止的任务: {all_need_stop}")
    
    print("==== 测试删除 ====")
    delete_stop_id("test_batch_job_1")
    print(f"删除后 test_batch_job_1 是否需要停止: {is_job_need_stop('test_batch_job_1')}")
    
    # 清理测试数据
    delete_stop_id("test_batch_job_2")

if __name__ == '__main__':
    # test_batch_job_stop_status()
    delete_stop_id("liulingfeng05_batch_2025-05-20-16-20-48")
