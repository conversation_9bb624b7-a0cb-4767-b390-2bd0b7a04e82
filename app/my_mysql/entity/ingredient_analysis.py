import json
from json.decoder import <PERSON><PERSON><PERSON><PERSON><PERSON>Error
from typing import List

from my_mysql import sql_client
from sqlalchemy import Table, Column, Integer, String, MetaData, insert, update, select, \
     text, TIMESTAMP, Index, and_, Text, or_
from sqlalchemy.dialects.mysql import BIGINT, TINYINT
from sqlalchemy.exc import SQLAlchemyError
from utils.logger import logger

"""
CREATE TABLE `ingredient_analysis` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `ingredient` varchar(200) NOT NULL DEFAULT '' COMMENT '原料名称',
  `analysis` text COMMENT '原料分析数据',
  `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态 1=有效 0=无效',
  `add_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_ingredient` (`ingredient`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
"""

# 定义表
ingredient_analysis = Table(
    'ingredient_analysis', MetaData(),
    Column('id', BIGINT(unsigned=True), primary_key=True, autoincrement=True, comment='自增id'),
    Column('ingredient', String(200), nullable=False, default='', comment='原料名称'),
    Column('analysis', Text, comment='原料分析数据'),
    Column('status', TINYINT(unsigned=True), nullable=False, default=1, comment='状态 1=有效 0=无效'),
    Column('add_time', TIMESTAMP, nullable=False, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间'),
    Column('update_time', TIMESTAMP, nullable=False, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='修改时间'),
    Index('idx_ingredient', 'ingredient')
)


# 上传数据
# name: 原料名称
# data: 原料分析数据
def upsert_data(name: str, data: str):
    select_stmt = ingredient_analysis.select().where(
        ingredient_analysis.c.ingredient == name,
        ingredient_analysis.c.status == 1
    )
    result = sql_client.select_one(select_stmt)
    if result:
        logger.info(f"exist ingredient {name} update")
        update_stmt = (
            update(ingredient_analysis).
            where(ingredient_analysis.c.id == result.id).
            values(analysis=data)
        )
        sql_client.update(update_stmt)
        return result
    else:
        logger.info(f"insert ingredient {name}")
        sql = insert(ingredient_analysis).values(ingredient=name, analysis=data)
        return sql_client.insert_return_id(sql)


def select_ingredient(ingredient: str):
    sql = select(ingredient_analysis).where(
        ingredient_analysis.c.ingredient == ingredient,
        ingredient_analysis.c.status == 1
    )
    return sql_client.select_one(sql)


def delete_ingredient(ingredient: str):
    sql = (
        update(ingredient_analysis).
        where(ingredient_analysis.c.ingredient == ingredient).
        values(status=0)
    )
    return sql_client.update(sql)


def test_sql():
    upsert_data("测试", "测试")
    select_ingredient("测试")


def search_ingredient(user_input: str|list, topk: int = 10) -> List[str]:
    """
    搜索配料相关信息
    Args:
        user_input: 用户输入的intent（JSON格式）
        topk: 返回结果数量限制
    Returns:
        List[str]: 匹配的配料分析信息列表
    """
    if isinstance(user_input, str):
        if not user_input or not user_input.strip():
            return []

        try:
            # JSON解析
            intent_data = json.loads(user_input)
        except JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return []

        # 收集配料
        ingredients: List[str] = []
        for key, values in intent_data.items():
            if isinstance(values, list):
                # 清理配料名称并过滤掉空字符串
                valid_values = [str(v).strip() for v in values if str(v).strip()]
                ingredients.extend(valid_values)
        
        if not ingredients:
            return []
    elif isinstance(user_input, list):
        ingredients = user_input
    else:
        return []
    
    # 限制配料数量，防止SQL IN子句过长
    if len(ingredients) > 100:  # 设置合理的上限
        logger.warning(f"配料数量过多: {len(ingredients)}，已截断")
        ingredients = ingredients[:100]
    
    try:
        stmt = select(ingredient_analysis).where(
            and_(
                ingredient_analysis.c.ingredient.in_(ingredients),
                ingredient_analysis.c.status == 1
            )
        ).limit(topk)
        
        insight_list = sql_client.select_many(stmt)
        # 返回配料分析信息字段（analysis字段）
        return [insight_info[2] for insight_info in insight_list]
        
    except SQLAlchemyError as e:
        logger.error(f"数据库查询失败: {str(e)}")
        return []


if __name__ == '__main__':
    test_sql()
