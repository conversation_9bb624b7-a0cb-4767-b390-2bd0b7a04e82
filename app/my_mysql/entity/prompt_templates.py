import json
from typing import List, Dict, Optional

from my_mysql import sql_client
from sqlalchemy import Table, Column, Integer, String, MetaData, insert, update, select, \
     text, TIMESTAMP, Index, and_, Text, or_, delete
from sqlalchemy.exc import SQLAlchemyError
from utils.logger import logger

"""
CREATE TABLE `prompt_templates`(
  mis_id VARCHAR(100) NOT NULL COMMENT "记录用户id",
  prompt_name VARCHAR(255) NOT NULL COMMENT "prompt列表模版名",
  prompt_list TEXT COMMENT "记录模版内的prompt list。每个prompt之间用三个中文分号链接成一个字符串。",
  PRIMARY KEY (mis_id, prompt_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
"""

# 定义表
prompt_templates = Table(
    'prompt_templates', MetaData(),
    Column('mis_id', String(100), primary_key=True, comment='记录用户id'),
    Column('prompt_name', String(255), primary_key=True, comment='prompt列表模版名'),
    Column('prompt_list', Text, comment='记录模版内的prompt list。每个prompt之间用三个中文分号链接成一个字符串。')
)

def insert_prompt_template(
    mis_id: str,
    prompt_name: str,
    prompt_list: List[str]
):
    """
    插入或更新prompt模板
    
    Args:
        mis_id (str): 用户ID
        prompt_name (str): 模板名称
        prompt_list (List[str]): prompt列表，输入时会把每个prompt之间用三个中文分号链接成一个字符串。
        
    Returns:
        bool: 插入成功返回True，失败返回False
    """
    prompt_list_str = "；；；".join(prompt_list)
    ins = prompt_templates.insert().values(
        mis_id=mis_id,
        prompt_name=prompt_name,
        prompt_list=prompt_list_str
    )
    try:
        sql_client.insert_return_id(ins)
        return True
    except SQLAlchemyError as e:
        # 如果主键冲突，尝试更新
        if "Duplicate entry" in str(e):
            return update_prompt_template(mis_id, prompt_name, prompt_list)
        logger.error(f"插入prompt_templates失败: {e}")
        return False

def update_prompt_template(
    mis_id: str,
    prompt_name: str,
    prompt_list: List[str]
):
    """
    更新prompt模板
    
    Args:
        mis_id (str): 用户ID
        prompt_name (str): 模板名称
        prompt_list (List[str]): prompt列表，输入时会把每个prompt之间用三个中文分号链接成一个字符串。
        
    Returns:
        bool: 更新成功返回True，失败返回False
    """
    prompt_list_str = "；；；".join(prompt_list)
    stmt = (
        prompt_templates.update()
        .where(
            and_(
                prompt_templates.c.mis_id == mis_id,
                prompt_templates.c.prompt_name == prompt_name
            )
        )
        .values(prompt_list=prompt_list_str)
    )
    try:
        result = sql_client.update(stmt)
        affected_rows = result.rowcount if result else 0
        if affected_rows > 0:
            logger.info(f"成功更新prompt模板 - mis_id: {mis_id}, prompt_name: {prompt_name}")
            return True
        else:
            logger.warning(f"未找到要更新的记录 - mis_id: {mis_id}, prompt_name: {prompt_name}")
            return False
    except Exception as e:
        logger.error(f"更新prompt模板失败: {e}")
        return False

def query_by_mis_id(mis_id: str, prompt_name: Optional[str] = None):
    """
    按用户ID和可选的模板名称查询prompt模板
    
    Args:
        mis_id (str): 用户ID
        prompt_name (str, optional): 模板名称，不提供则查询该用户的所有模板
        
    Returns:
        list: 查询结果列表
    """
    conds = [prompt_templates.c.mis_id == mis_id]
    if prompt_name:
        conds.append(prompt_templates.c.prompt_name == prompt_name)
    
    stmt = select(prompt_templates).where(and_(*conds))
    try:
        return sql_client.select_many(stmt)
    except Exception as e:
        logger.error(f"查询prompt_templates失败: {e}")
        return []

def delete_prompt_template(mis_id: str, prompt_name: str):
    """
    删除指定的prompt模板
    
    Args:
        mis_id (str): 用户ID
        prompt_name (str): 模板名称
        
    Returns:
        bool: 删除成功返回True，失败返回False
    """
    stmt = prompt_templates.delete().where(
        and_(
            prompt_templates.c.mis_id == mis_id,
            prompt_templates.c.prompt_name == prompt_name
        )
    )
    try:
        result = sql_client.update(stmt)
        affected_rows = result.rowcount if result else 0
        if affected_rows > 0:
            logger.info(f"成功删除prompt模板 - mis_id: {mis_id}, prompt_name: {prompt_name}")
            return True
        else:
            logger.warning(f"未找到要删除的记录 - mis_id: {mis_id}, prompt_name: {prompt_name}")
            return False
    except Exception as e:
        logger.error(f"删除prompt模板失败: {e}")
        return False

def delete_all_by_mis_id(mis_id: str):
    """
    删除用户的所有prompt模板
    
    Args:
        mis_id (str): 用户ID
        
    Returns:
        bool: 删除成功返回True，失败返回False
    """
    stmt = prompt_templates.delete().where(
        prompt_templates.c.mis_id == mis_id
    )
    try:
        result = sql_client.update(stmt)
        affected_rows = result.rowcount if result else 0
        if affected_rows > 0:
            logger.info(f"成功删除用户所有prompt模板 - mis_id: {mis_id}")
            return True
        else:
            logger.warning(f"未找到要删除的记录 - mis_id: {mis_id}")
            return False
    except Exception as e:
        logger.error(f"删除用户所有prompt模板失败: {e}")
        return False

# 测试函数
def test_prompt_templates():
    print("==== 测试插入 ====")
    insert_prompt_template(
        mis_id="test_user_1",
        prompt_name="常用问题",
        prompt_list=["如何使用这个产品？", "产品有哪些功能？", "如何联系客服？"]
    )
    insert_prompt_template(
        mis_id="test_user_1",
        prompt_name="技术问题",
        prompt_list=["如何配置API？", "如何处理错误？", "如何优化性能？"]
    )
    insert_prompt_template(
        mis_id="test_user_2",
        prompt_name="常用问题",
        prompt_list=["产品价格是多少？", "有没有折扣？", "如何退款？"]
    )
    
    print("==== 测试按mis_id查询 ====")
    res1 = query_by_mis_id(mis_id="test_user_1")
    print(res1)
    
    print("==== 测试按mis_id和prompt_name查询 ====")
    res2 = query_by_mis_id(mis_id="test_user_1", prompt_name="常用问题")
    print(res2)
    
    print("==== 测试更新 ====")
    update_prompt_template(
        mis_id="test_user_1",
        prompt_name="常用问题",
        prompt_list=["如何使用这个产品？", "产品有哪些功能？", "如何联系客服？", "如何注册账号？"]
    )
    res3 = query_by_mis_id(mis_id="test_user_1", prompt_name="常用问题")
    print(res3)
    
    print("==== 测试删除 ====")
    delete_prompt_template("test_user_1", "常用问题")
    res4 = query_by_mis_id(mis_id="test_user_1")
    print(res4)
    
    print("==== 测试删除用户所有模板 ====")
    delete_all_by_mis_id("test_user_1")
    delete_all_by_mis_id("test_user_2")
    res5 = query_by_mis_id(mis_id="test_user_1")
    print(res5)

if __name__ == '__main__':
    # 取消注释以运行测试
    test_prompt_templates()
    pass
