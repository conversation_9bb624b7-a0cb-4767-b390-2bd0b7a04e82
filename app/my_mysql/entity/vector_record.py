from my_mysql import sql_client
from sqlalchemy import Table, Column, Integer, String, MetaData, insert, update, select, \
    Text, TIMESTAMP, Index, text
from sqlalchemy.dialects.mysql import BIGINT, TINYINT
from utils.logger import logger
from utils.utils import calculate_md5

"""
CREATE TABLE `vector_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `global_id` BIGINT NOT NULL DEFAULT '0' COMMENT 'vex全局id',
  `origin_content` varchar(10000) NOT NULL DEFAULT '' COMMENT '向量原始内容',
  `content_md5` varchar(100) NOT NULL DEFAULT '' COMMENT '向量MD5',
  `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态 1=有效 0=无效',
  `add_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_global_id` (`global_id`),
  KEY `idx_content_md5` (`content_md5`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
"""

# 定义表
vector_record = Table(
    'vector_record', MetaData(),
    Column('id', BIGINT(unsigned=True), primary_key=True, autoincrement=True, comment='自增id'),
    Column('global_id', BIGINT, nullable=False, default='0', comment='vex全局id'),
    Column('origin_content', String(10000), nullable=False, default='', comment='向量原始内容'),
    Column('content_md5', String(100), nullable=False, default='', comment='向量内容MD5，直接使用内容本身作为索引会溢出'),
    Column('status', TINYINT(unsigned=True), nullable=False, default=1, comment='状态 1=有效 0=无效'),
    Column('add_time', TIMESTAMP, nullable=False, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间'),
    Column('update_time', TIMESTAMP, nullable=False, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='修改时间'),
    Index('idx_global_id', 'global_id'),
    Index('idx_content_md5', 'content_md5')
)


def insert_vector(global_id: int, origin_content: str):
    select_stmt = vector_record.select().where(vector_record.c.global_id == global_id)
    result = sql_client.select_one(select_stmt)
    if result:
        logger.info(f"exist global_id {global_id} content {origin_content}")
        return result
    else:
        logger.info(f"insert global_id {global_id}")
        md5 = calculate_md5(origin_content)
        sql = insert(vector_record).values(global_id=global_id, origin_content=origin_content, content_md5=md5)
        return sql_client.insert_return_id(sql)


def select_vector_by_id(global_id: int):
    sql = select(vector_record).where(vector_record.c.global_id == global_id)
    return sql_client.select_one(sql)


def select_vector_by_content(origin_content: str):
    md5 = calculate_md5(origin_content)
    sql = select(vector_record).where(vector_record.c.content_md5 == md5)
    return sql_client.select_one(sql)


def delete_vector(global_id: int):
    sql = update(vector_record).where(vector_record.c.global_id == global_id).values(status=0)
    return sql_client.update(sql)


def test_sql():
    insert_vector("test_global_id", "test_origin_content")
    select_vector_by_id("test_global_id")


if __name__ == '__main__':
    test_sql()
