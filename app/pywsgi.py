from gevent import monkey
monkey.patch_all()

import signal
import sys
from utils.logger import logger
from gevent.pywsgi import WSGIServer
from app import app

def signal_handler(signum, frame):
    logger.info(f"收到信号 {signum}，准备关闭服务器...")
    http_server.stop()
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)

logger.info("---------------- wsgi start ----------\n\n")
http_server = WSGIServer(('0.0.0.0', 8080), app)

try:
    http_server.serve_forever()
except Exception as e:
    logger.error(f"服务器运行出错: {str(e)}")
    sys.exit(1)
