import redis
import json
from datetime import datetime
from typing import Any, Optional
from utils.logger import logger

# Redis配置
REDIS_HOST = "localhost"
REDIS_PORT = 6379
REDIS_DB = 0

# 创建Redis连接
try:
    redis_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        db=REDIS_DB,
        decode_responses=True
    )
except Exception as e:
    logger.error(f"连接Redis失败: {str(e)}")
    redis_client = None

def get_cache(key: str) -> Optional[Any]:
    """从缓存中获取数据
    
    Args:
        key (str): 缓存键
        
    Returns:
        Any or None: 缓存的数据或None
    """
    try:
        if not redis_client:
            return None
            
        data = redis_client.get(key)
        if not data:
            return None
            
        return json.loads(data)
        
    except json.JSONDecodeError as e:
        logger.error(f"解析缓存数据时出错: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"获取缓存时出错: {str(e)}")
        return None

def set_cache(key: str, value: Any, expire: int = 3600) -> bool:
    """设置缓存数据
    
    Args:
        key (str): 缓存键
        value (Any): 要缓存的数据
        expire (int): 过期时间（秒）
        
    Returns:
        bool: 是否成功设置缓存
    """
    try:
        if not redis_client:
            return False
            
        # 序列化数据
        data = json.dumps(value)
        
        # 设置缓存
        redis_client.set(key, data, ex=expire)
        return True
        
    except (TypeError, json.JSONEncodeError) as e:
        logger.error(f"序列化缓存数据时出错: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"设置缓存时出错: {str(e)}")
        return False

def clear_cache(key: str) -> bool:
    """清除指定的缓存
    
    Args:
        key (str): 缓存键
        
    Returns:
        bool: 是否成功清除缓存
    """
    try:
        if not redis_client:
            return False
            
        return bool(redis_client.delete(key))
        
    except Exception as e:
        logger.error(f"清除缓存时出错: {str(e)}")
        return False

def clear_all_cache() -> bool:
    """清除所有缓存
    
    Returns:
        bool: 是否成功清除所有缓存
    """
    try:
        if not redis_client:
            return False
            
        return bool(redis_client.flushdb())
        
    except Exception as e:
        logger.error(f"清除所有缓存时出错: {str(e)}")
        return False
