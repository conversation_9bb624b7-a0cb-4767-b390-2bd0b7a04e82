# import os

# # 禁用tokenizers的并行化
# os.environ["TOKENIZERS_PARALLELISM"] = "false"

# import torch
# from torch import Tensor
# from transformers import AutoModelForSequenceClassification, AutoTokenizer

# Rerank_tokenizer = AutoTokenizer.from_pretrained('BAAI/bge-reranker-v2-m3')
# Rerank_model = AutoModelForSequenceClassification.from_pretrained('BAAI/bge-reranker-v2-m3')


# @torch.no_grad()
# def rerank_info(query:list, doc:list, batch_size=16)->list[list[str, Tensor]]:
#     # 将doc分批
#     doc_score = []
#     query = " ".join(query)
#     batches = [doc[i:i+batch_size] for i in range(0, len(doc), batch_size)]
#     for batch in batches:
#         inputs = Rerank_tokenizer([[query, doc] for doc in batch], padding=True, truncation=True, return_tensors='pt', max_length=512)
#         scores = Rerank_model(**inputs, return_dict=True).logits.view(-1, ).float()
#         doc_score.extend(zip(batch, scores))
#     # 排序
#     doc_score.sort(key=lambda x: x[1], reverse=True)
#     return doc_score

# @torch.no_grad()
# def rerank_info_plus(query_embeddings:Tensor, doc_embeddings:list[Tensor], batch_size=16)->list[list[str, Tensor]]:
#     pass

