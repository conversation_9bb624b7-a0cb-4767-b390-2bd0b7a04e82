import json
import time
from abc import ABC, ABCMeta, abstractmethod
from typing import List, Dict, Any, Optional
from utils.logger import logger
from service.COT.function_box import BaseKnowledge, SQL, VexDB
from service.ai_client import send_to_ai
from service.order import get_user_orders_v2
from service.COT.function_box import BaseKnowledgeTool, SQLTool, VexDBTool, function_calling
import vex.vex_thrift as vex
from configs import lion_config
from utils.extract import fill_shop_dict
from service import ingredient
from service.COT.registry_pool import LocalRegistry, Registry
from service.COT.function_box import function_calling, get_tool_json_scheme



# 基类，所有的Agent都应该继承这个类，会自动注册到Agent池中，用于以后的调用
class BaseAgent(ABC):
    def __init__(self, model, tools:Optional[List[str]|List[Dict[str, Any]]]=None, response_format:Optional[str]=None, role_prompt:Optional[str]=None, agent_name:Optional[str]=None, description:Optional[str]=None, parameters:Optional[List[Dict[str, Any]]]=None):
        self.model = model
        self.tools = tools # 一个Agent应该有自己的工具箱，知道如何调用
        if self.tools:
            if len(self.tools) == 0:
                pass
            else:
                for i in range(len(self.tools)):
                    if isinstance(self.tools[i], str):
                        self.tools[i] = get_tool_json_scheme(self.tools[i])

        self.response_format = response_format # 一个Agent可以选择构建格式
        self.role_prompt = role_prompt
        self._init_json_scheme_(agent_name, description, parameters)
        assert hasattr(self, "json_scheme"), "json_scheme 必须被设置"
        self.history = [] # 保存ReAct的历史信息

    def _init_json_scheme_(self, agent_name, description, parameters):
        if agent_name:
            # 将类注册到 Registry
            # Registry.register(agent_name)(cls)
            requires = []
            properties = {}
            for param in parameters:
                param_type = param["type"]
                param_name = param["name"]
                param_description = param["description"]
                required = param.get("required", False)
                properties[param_name] = {
                    "type": param_type,
                    "description": param_description,
                } if param_type not in ["object", "array"] else ({
                    "type": param_type,
                    "description": param_description,
                    "items": param.get("items", {
                        "type": "string",
                    })
                } if param_type == "array" else {
                    "type": param_type,
                    "description": param_description,
                    "properties": param.get("properties", {})
                })
                if required:
                    requires.append(param_name)
            json_scheme = {
                "type": "function",
                "function": {
                    "name": agent_name,
                    "description": description,
                    "parameters": {
                        "type": "object",
                        "required": requires,
                        "properties": properties
                        }
                    }
                }
            
            # 设置为类属性而不是模块属性
            setattr(self, "json_scheme", json_scheme)

    def _send_llm(self, messages:List[Dict[str, Any]], final:bool=False)->str:
        data = {
            "model": self.model,
            "messages": messages,
            "temperature": 0.6
        }

        # 最后一步模型返回的时候，不会调用工具
        if self.tools and not final:
            data["tools"] = self.tools

        if self.response_format and final:
            data["response_format"] = self.response_format
            
        response = send_to_ai(data)
        return response.text

    @abstractmethod
    def call(self, query:str, **kwargs)->str:
        pass

    def step(self):
        pass

class BaseAgentV1(ABC):
    def __init__(self, model, tools, response_format, role_prompt):
        self.model = model
        self.tools = tools # 一个Agent应该有自己的工具箱，知道如何调用
        self.response_format = response_format # 一个Agent可以选择构建格式
        self.role_prompt = role_prompt

        self.history = [] # 保存ReAct的历史信息

    @abstractmethod
    def call(self, query:str, **kwargs)->str:
        pass

    def step(self):
        pass

class CostumeRAGAgent(BaseAgentV1):
    def __init__(self, model=None, tools=[], response_format=None, role_prompt=None):
        super().__init__(model, tools, response_format, role_prompt)

    # TODO: 将来可能需要把不同的code存到数据库中，然后这里的code改为对应的doc_id来查找。
    @staticmethod
    def call(code:str, query:str, **kwargs)->str:

        # 获取用户订单
        def get_user_orders(limit:int = 10)->dict:
            try:
                return get_user_orders_v2(kwargs.get("token", None), kwargs.get("user_id", None), limit)
            except Exception as e:
                logger.error(f"get_user_orders error: {e}")
                return {}

        local_namespace = {}
        global_namespace = {
            # 工具函数
            "BaseKnowledge": BaseKnowledge, \
            "SQL": SQL, \
            "VexDB": VexDB, \
            "send_to_ai": send_to_ai,
            "get_user_orders": get_user_orders,
            

            # 数据信息
            "user_id": kwargs.get("user_id", None), \
            "user_orders": kwargs.get("user_orders", None), \
            "user_feature": kwargs.get("user_feature", None), \
            "memory_content": kwargs.get("memory_content", None), \
            "query": query,
            "history": kwargs.get("history", None),
                            }
        
        exec(code, global_namespace, local_namespace)
        result = local_namespace.get("result", None)
        if result is None:
            result_func = local_namespace.get("result_func", None)
            if result_func is not None:
                result = result_func()
            else:
                logger.error(f"自定义RAG Agent召回的结果为空，且result_func为空")
                return ""
        logger.info(f"自定义RAG Agent召回的结果为: {result}")
        return result
        


class RAGAgent(BaseAgentV1):
    def __init__(self, model=None, tools=[], response_format=None, role_prompt=None, pre_tools=[]):
        if model is None:
            model = "gpt-4.1"
        if role_prompt is None:
            role_prompt = "你是一个外卖推荐专家，根据用户的需求，给出最合适的推荐。"
        
        super().__init__(model, tools, response_format, role_prompt)
        self.pre_tools = pre_tools
    def _get_stream_dict(self, pre_tools:list=[])->str:
        """构建流式响应字典
        
        Args:
            step_num: 工具步骤序号
            tool_status: 工具状态，例如"RUNNING"、"FINISHED"
            search_word_list: 搜索关键词列表
            reason: 分析说明文本
            tool_return: 工具返回结果列表
            last_one: 是否最后一个响应
            
        Returns:
            dict: 流式响应字典
        """
 
        return json.dumps({
            "created": int(time.time()),
            "loadingStatus": False,
            "lastOne": False,
            "pre_tools": pre_tools
        }, ensure_ascii=False)
    
    def _construct_pretools(self, name:str, step_num:int, tool_status:str, search_word_list:list, reason:str, tool_return:list=None, last_one:bool=False)->list:
        return {
            "tool": name,
            "toolStep": step_num,
            "toolStatus": tool_status,
            "tool_input": {
                "search_word_list": search_word_list if isinstance(search_word_list, list) else [search_word_list],
                "reason": reason
            },
            "tool_return": tool_return,
            "description": "这是一条测试信息"
        } if tool_return is not None else {
            "tool": name,
            "toolStep": step_num,
            "toolStatus": tool_status,
            "tool_input": {
                "search_word_list": search_word_list if isinstance(search_word_list, list) else [search_word_list],
                "reason": reason
            },
            "description": "这是一条测试信息"
        }

    def call(self, query:str, intent:list[str], user_orders:dict, user_id, generator=None)->str:
        """处理分析请求

        Args:
            intent: 模型返回的rag 输入
            current_user_id: 当前用户ID
            user_orders: 用户订单数据
            generator: 用于流式输出处理过程的生成器

        Returns:
            str or None: 分析结果
        """

        pre_tools = self.pre_tools
        try:
            # ============================ Vex数据库相关 ============================    
            try:
                vex_name = lion_config.lion_client.get_value_with_default("weiwei.vex_rag_search_name", "向量数据库检索")
                tmp_intent = intent.copy()
                vex_idx = 0
                while vex_idx < int(lion_config.lion_client.get_value_with_default("weiwei.vex_cycle_num", 5)):
                    logger.info(f"Vex数据库召回第{vex_idx}次")
                    if vex_idx == 0:
                        pre_tools.append(self._construct_pretools(
                            name=vex_name,
                            step_num=vex_idx+1,
                            tool_status="RUNNING",
                            search_word_list=tmp_intent,
                            reason="正在从向量数据库检索相关信息..."
                        ))
                        if generator:
                            generator.send(self._get_stream_dict(pre_tools))
                        merchants_dict, ignore_info = function_calling("VexDB", "call", intent=tmp_intent, num=lion_config.VEX_SEARCH_NUM)
                        pre_tools[-1]["toolStatus"] = "FINISHED" # 更新工具状态
                        if generator:
                            generator.send(self._get_stream_dict(pre_tools))
                        vex_idx += 1
                    else:
                        # 使用上一次的召回结果作为新的召回条件
                        tools = [
                            VexDBTool.json_scheme
                        ]
                        data = {
                            "model": self.model,
                            "tools": tools,
                            "messages": [
                                {"role": "system", "content": lion_config.lion_client.get_value_with_default("weiwei.vex_react_prompt", "")},
                                {"role": "user", "content": f"# 当前的召回结果为：\n{merchants_dict} \n\n # 用户需要的主题 \n {query} \n # 之前生成的关键词 \n {intent}"}
                            ],
                            "temperature": 0.6
                        }
                        response = send_to_ai(data)
                        json_data = json.loads(response.text)
                        json_data = json_data["choices"][0]["message"]
                        logger.info(f"ReAct过程中Vex数据库召回结果: {json_data}")
                        func_details = json_data.get("tool_calls", [])
                        if len(func_details) == 0:
                            break
                        else:
                            for func_detail in func_details:
                                func_name = func_detail.get("function")["name"]
                                func_args = json.loads(func_detail.get("function")["arguments"])
                                if func_name == "VexDB":
                                    # 开始进一步调用
                                    pre_tools.append(self._construct_pretools(
                                        name=lion_config.lion_client.get_value_with_default("weiwei.vex_rag_search_name", "向量数据库检索"),
                                        step_num=vex_idx+1,
                                        tool_status="RUNNING",
                                        search_word_list=func_args["intent"],
                                        reason="正在从向量数据库检索相关信息..." if json_data.get("content", None) is None else json_data.get("content")
                                    ))
                                    generator.send(self._get_stream_dict(pre_tools))
                                _merchants_dict, _ignore_info = function_calling("VexDB", "call", intent=func_args["intent"], num=lion_config.VEX_SEARCH_NUM)
                                pre_tools[-1]["toolStatus"] = "FINISHED" # 更新工具状态
                                generator.send(self._get_stream_dict(pre_tools))

                                # 将搜索结果合并
                                for key, value in _merchants_dict.items():
                                    if key in merchants_dict:
                                        merchants_dict[key].extend(value)
                                    else:
                                        merchants_dict[key] = value
                                ignore_info.extend(_ignore_info)
                                vex_idx += 1
                # ======================================================================
            except Exception as e:
                logger.error(f"Vex数据库召回失败: {e}")
                merchants_dict = {}
                ignore_info = []

            logger.info(f"### Vex数据库召回结果: {merchants_dict}")
            logger.info(f"### Vex数据库召回结果: {ignore_info}")

            # ============================ ES数据库相关 ============================    
            try:
                es_name = lion_config.lion_client.get_value_with_default("weiwei.es_rag_search_name", "ES数据库检索")
                es_tmp_intent = intent.copy()
                es_idx = 0
                es_result = []
                while es_idx < int(lion_config.lion_client.get_value_with_default("weiwei.es_cycle_num", 5)):
                    if es_idx == 0:
                        pre_tools.append(
                            self._construct_pretools(
                                name=es_name,
                                step_num=es_idx+1,
                                tool_status="RUNNING",
                                search_word_list=es_tmp_intent,
                                reason="正在从ES数据库检索相关信息..."
                            )
                        )
                        if generator:
                            generator.send(self._get_stream_dict(pre_tools))
                        es_result = function_calling("BaseKnowledge", "call", keywords=es_tmp_intent)
                        pre_tools[-1]["toolStatus"] = "FINISHED" # 更新工具状态
                        if generator:
                            generator.send(self._get_stream_dict(pre_tools))
                    else:
                        # 使用上一次的召回结果作为新的召回条件
                        tools = [
                            BaseKnowledgeTool.json_scheme
                        ]
                        data = {
                            "model": self.model,
                            "tools": tools,
                            "messages": [
                                {"role": "system", "content": lion_config.lion_client.get_value_with_default("weiwei.es_react_prompt", "")},
                                {"role": "user", "content": f"# 当前的召回结果为：\n{es_result} \n\n # 用户需要的主题 \n {query} \n # 之前生成的关键词 \n {intent}"}
                            ],
                            "temperature": 0.6
                        }
                        response = send_to_ai(data)
                        json_data = json.loads(response.text)
                        json_data = json_data["choices"][0]["message"]
                        logger.info(f"## ReAct过程中模型返回的结果: {json_data}")
                        func_details = json_data.get("tool_calls", [])
                        if len(func_details) == 0:
                            break
                        else:
                            func_args = []
                            func_detail = func_details[0]
                            func_name = func_detail.get("function")["name"]
                            for func_detail in func_details:
                                func_args = json.loads(func_detail.get("function")["arguments"])
                                if isinstance(func_args.get("keywords"), list):
                                    es_tmp_intent.extend(func_args["keywords"])
                                else:
                                    es_tmp_intent.append(func_args["keywords"])
                                    
                            if func_name == "BaseKnowledge":
                                pre_tools.append(
                                    self._construct_pretools(
                                        name=es_name,
                                        step_num=es_idx+1,
                                        tool_status="RUNNING",
                                        search_word_list=func_args["keywords"],
                                        reason="正在从ES数据库检索相关信息..." if json_data.get("content", None) is None else json_data.get("content")
                                    ))
                                generator.send(self._get_stream_dict(pre_tools))
                                _es_result = function_calling("BaseKnowledge", "call", keywords=func_args["keywords"])
                                pre_tools[-1]["toolStatus"] = "FINISHED" # 更新工具状态
                                generator.send(self._get_stream_dict(pre_tools))
                                es_result.extend(_es_result)
                    es_idx += 1
                # ======================================================================
            except Exception as e:
                logger.error(f"ES数据库召回失败: {e}")
                es_result = []
                if generator:
                    stream_dict = self._get_stream_dict(
                        step_num=2,
                        tool_status="FAILED",
                        search_word_list=intent,
                        reason=f"食材信息检索过程中出现错误: {str(e)}"
                    )
                    generator.send(json.dumps(stream_dict))

            logger.info(f"### ES数据库召回结果: {es_result}")

            # # 开始构造Prompt结构
            # 订单数据
            result_str = "下面是用户自己近期的订单数据，格式是json：\n"
            result_str += json.dumps(user_orders, ensure_ascii=False)
            result_str += "\n用户近期的订单到此结束。\n\n"

            # 热门订单
            result_str += "这是附近的热门订单数据，不是用户自己的订单。如果用户需要推荐时，请参考这些数据。格式为<content></content>，下面是具体的数据：\n"
            for shop_name in merchants_dict:
                idx = 0
                for result_dict in merchants_dict[shop_name]:
                    text = result_dict['content']
                    result_str += f"<content>{text}</content>\n"
                    idx += 1
                    if idx == lion_config.MERCHANTS_SUP_NUM:
                        break
            result_str += "附近的热门订单数据到此结束。\n\n"

            # es召回的模糊匹配的食材信息
            result_str += "下面是相关的食材信息, 请参考这些数据。格式为<content></content>下面是具体的数据：\n"
            # result_str += f"<content>{es_result}</content>\n"
            result_str += "相关的分析信息到此结束。\n\n"
            return result_str, pre_tools
        except Exception as e:
            logger.error(f"系统， 用户{user_id} " + f"处理分析请求时出错: {str(e)}")
            if generator:
                stream_dict = self._get_stream_dict(
                    step_num=1,
                    tool_status="FAILED",
                    search_word_list=intent if 'intent' in locals() else [],
                    reason=f"RAG处理过程中发生错误: {str(e)}",
                    last_one=True
                )
                generator.send(json.dumps(stream_dict))
            return None, None

    
        
        