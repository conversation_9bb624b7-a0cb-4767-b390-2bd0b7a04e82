import pandas as pd
import numpy as np
from scipy import stats
import os

# Define file paths
desktop_path = os.path.expanduser("~/Desktop")
file1_path = os.path.join(desktop_path, "总的结果对比.xlsx")
file2_path = os.path.join(desktop_path, "不同仪器的测试结果.xlsx")

# Part 1: Analysis of first Excel file
print("分析文件1: 总的结果对比.xlsx")
df1 = pd.read_excel(file1_path)
columns = df1.columns[:2]  # Get the first two columns

for col in columns:
    values = df1[col].dropna()
    mean = values.mean()
    variance = values.var()
    print(f"{col} - 均值: {mean:.4f}, 方差: {variance:.4f}")

# Part 2: Analysis of second Excel file
print("\n分析文件2: 不同仪器的测试结果.xlsx")
df2 = pd.read_excel(file2_path)
columns = df2.columns[:2]  # Get the first two columns

# Shapiro-Wilk test for normality
for col in columns:
    values = df2[col].dropna()
    stat, p_value = stats.shapiro(values)
    print(f"{col} - Shapiro-Wilk 检验 p值: {p_value:.4f}")
    if p_value > 0.05:
        print(f"  {col} 符合正态分布 (p > 0.05)")
    else:
        print(f"  {col} 不符合正态分布 (p < 0.05)")

# Paired t-test
col1_values = df2[columns[0]].dropna()
col2_values = df2[columns[1]].dropna()

# Ensure the arrays have the same length for paired test
min_len = min(len(col1_values), len(col2_values))
col1_values = col1_values[:min_len]
col2_values = col2_values[:min_len]

stat, p_value = stats.ttest_rel(col1_values, col2_values)
print(f"\n配对t检验结果: p值 = {p_value:.4f}")
if p_value < 0.05:
    print("存在显著差异 (p < 0.05)")
else:
    print("无显著差异 (p > 0.05)")

# Wilcoxon rank test
stat, p_value = stats.wilcoxon(col1_values, col2_values)
print(f"\nWilcoxon 秩和检验结果: p值 = {p_value:.4f}")
if p_value < 0.05:
    print("存在显著差异 (p < 0.05)")
else:
    print("无显著差异 (p > 0.05)")
