import json
import threading
import time
from uuid import uuid4
import base64
from service.tts import TtsClient
from utils.squirrel import RedisClient, build_category_key
from utils.logger import logger
from configs import lion_config
from utils.meituan_link import add_meituan_link
from service.ai_client import send_to_ai, convert_standard_format_to_claude_format
import re

class CoT:
    def __init__(self):
        self.func = None
        
    def Get_CoT(self, cot_name:str|None=None):
        if hasattr(self, lion_config.COT_FUNC if cot_name == None else cot_name):
            self.func = getattr(self, lion_config.COT_FUNC if cot_name == None else cot_name)
        else:
            self.func = None
        return self.func

    def Get_Base(self, base_name:str|None=None):
        if hasattr(self, lion_config.BASE_FUNC if base_name == None else base_name):
            self.func = getattr(self, lion_config.BASE_FUNC if base_name == None else base_name)
        else:
            self.func = None
        return self.func
    
    def base(self, messages: list[dict['role': str, 'content': str]], stream: bool = False, tts_session_id: None = None, model=None, token:str|None=None) -> None:
        temp_line = ""
        meituan_link = []
        content_lines = [] # 每一行的信息
        temporary_merchant_info = {"id":None, "name":None} # 当前即将要处理的商家信息
        content = ""
        accumulated_content = ""

        
        model = lion_config.AI_MODEL if model == None else model

        data = {
            "model": model,
            "messages": messages,
            "stream": stream,
            "temperature": 0.0,
            "max_tokens": 4000,
        }
        first_output = 0
        start_time = time.time()
        response = send_to_ai(data)

        if response is None:
            if stream:
                yield "抱歉，目前的网络好像有点问题，下次再来和小美喂喂聊天吧。"
                
            return None
        
        if response.status_code != 200:
            if stream:
                yield "抱歉，目前的网络好像有点问题，下次再来和小美喂喂聊天吧。"
                
            return None
        accumulated_content = ""
        complete_sentence = ""
        index = 1
        session_id = str(uuid4())
        delimiters = {"。", "，", "\n"}
        concat_reasoning_content = ""
        if stream: # 需要流式输出到用户端
            logger.info("模型开始流式返回")
            for line in response.iter_lines():
                if line and line.startswith(b"data: "):
                    try:
                        json_data = json.loads(line[6:])
                        tts_session_id = json_data.get("id", "")
                        delta = json_data.get("choices", [{}])[0].get("delta", {})
                        content = delta.get("content", "")
                        reasoning_content = delta.get("reasoning_content", None)
                        
                        # 开始返回回复内容
                        if content:
                            if first_output == 0:
                                first_output = 1
                                first_output_time = round(time.time() - start_time, 2)
                                logger.info(f"流式输出开始，耗时: {first_output_time}")
                            accumulated_content += content
                            temp_line += content

                            # TTS
                            complete_sentence += content
                            if tts_session_id is not None and tts_session_id != "":
                                complete_sentence = complete_sentence.strip()
                                complete_sentence = pre_handle_one_line_text(complete_sentence)
                                for char_index, char in enumerate(complete_sentence):
                                    if char in delimiters:
                                        special_signal_index = char_index
                                        origin_complete_sentence = complete_sentence
                                        complete_sentence = complete_sentence[:special_signal_index]
                                        if complete_sentence:
                                            thread = threading.Thread(
                                                target=generate_pcm_data,
                                                args=(tts_session_id, index, complete_sentence)
                                            )
                                            thread.start()
                                            index += 1
                                            complete_sentence = origin_complete_sentence[special_signal_index + 1:].strip()
                                            logger.info(f"after complete_sentence:{complete_sentence}")
                                            break
                            
                            # 添加美团购买链接
                            temp_line, accumulated_content, meituan_link = add_meituan_link(
                                content, 
                                temp_line, 
                                content_lines, 
                                accumulated_content, 
                                temporary_merchant_info,
                                meituan_link,
                                token
                            )

                            if "\n" in temp_line:
                                temp_line = temp_line[temp_line.find('\n')+1:]

                            # 流式返回信息
                            stream_data = {
                                "choices": [{
                                    "delta":{
                                        "reasoning_content": reasoning_content,
                                    },
                                    "finish_reason": None,
                                    "index": 0,
                                    "message": {
                                        "content": content,
                                        "role": "assistant"
                                    }
                                }],
                                "session_id": session_id,
                                "content": accumulated_content,
                                "created": int(time.time()),
                                "id": json_data.get("id", ""),
                                "lastOne": False
                            }

                            yield json.dumps(stream_data, ensure_ascii=False)
                    except json.JSONDecodeError:
                        continue
            
            if tts_session_id is not None and tts_session_id != "":
                if complete_sentence.strip():
                    threading.Thread(
                        target=generate_pcm_data,
                        args=(tts_session_id, index, complete_sentence)
                    ).start()
                threading.Thread(
                    target=generate_final_pcm_data,
                    args=(tts_session_id, index)
                ).start()
            final_data = {
                "choices": [{
                    "delta":{
                        "reasoning_content": reasoning_content,
                    },
                    "finish_reason": "stop",
                    "index": 0,
                    "message": {
                        "content": "",
                        "role": "assistant"
                    }
                }],
                "content": accumulated_content,
                "session_id": session_id,
                "created": int(time.time()),
                "id": json_data.get("id", ""),
                "lastOne": True
            }
            logger.info("模型最终的思考过程为: {}".format("空"))
            logger.info("模型最终的回复为: {}".format(accumulated_content))
            yield json.dumps(final_data, ensure_ascii=False)
            return
        else:
            json_data = json.loads(response.text)
            logger.info(f"模型直接返回的的结果: {json_data}")
            yield json_data["choices"][0]["message"]["content"]
        return None
        # ["gpt-4o-2024-05-13", "deepseek-r1-friday", "anthropic.claude-3.7-sonnet"]

    def demo(self, messages: list[dict['role': str, 'content': str]], stream: bool = False, tts_session_id: None = None, model: str = "", token:str|None=None) -> dict:
        """调用美团AI接口获取回复"""
        try:
            content_lines = []
            temporary_merchant_info={"id":None, "name":None}
            content = ""
            accumulated_content = ""
            meituan_link = []
            temp_line = ""# 为回复添加美团购物链接
            raw_message = messages
            # =================== 思维过程 ======================
            accumulated_cot = ""
            messages = [{
            "role":"assistant",
            "content": lion_config.COT_PROMPT.format(system_prompt=messages[0]['content'], chat_history=messages[1:])
            }]
            model = model if model != "" else lion_config.AI_MODEL
            data = {
                "model": model,
                "messages": messages,
                "stream": stream,
                "temperature": 0.0,
                "max_tokens": 4000,
            }
            logger.info("开始产生思维过程")
            response = send_to_ai(data)
            if response is None:
                logger.error(f"在调用思维链的调用失败，ai未获得返回值.")
                return None
            if response.status_code != 200:
                logger.error(f"在调用思维链的调用失败，状态码: {response.status_code}")
                logger.error(f"错误信息: {response.text}")
                return None
            for line in response.iter_lines():
                if line and line.startswith(b"data: "):
                    try:
                        json_data = json.loads(line[6:])
                        delta = json_data.get("choices", [{}])[0].get("delta", {})
                        content = delta.get("content", "")
                        # 开始返回回复内容
                        if content:
                            accumulated_cot += content
                            # 流式返回信息
                            stream_data = {
                                "choices": [{
                                    "delta":{
                                        "reasoning_content": content,
                                    },
                                    "finish_reason": None,
                                    "index": 0,
                                    "message": {
                                        "content": "",
                                        "role": "assistant"
                                    }
                                }],
                                "session_id": None,
                                "content": "",
                                "created": int(time.time()),
                                "id": json_data.get("id", ""),
                                "lastOne": False
                            }
                            yield json.dumps(stream_data, ensure_ascii=False)
                    except json.JSONDecodeError:
                        continue
                final_data = {
                    "choices": [{
                        "delta":{
                            "reasoning_content": None,
                        },
                        "finish_reason": "stop",
                        "index": 0,
                        "message": {
                            "content": None,
                            "role": "assistant"
                        }
                    }],
                    "content": "",
                    "session_id": None,
                    "created": int(time.time()),
                    "id": json_data.get("id", ""),
                    "lastOne": False
                }
                yield json.dumps(final_data, ensure_ascii=False) # 返回final_data
            # =================== 思维过程结束 =======================

            messages = raw_message

            data = {
                "model": lion_config.AI_MODEL,
                "messages": messages,
                "stream": True,
                "temperature": 0.0,
                "max_tokens": 4000,
            }
            logger.info("开始产生正式答案")
            response = send_to_ai(data)
            accumulated_content = ""
            complete_sentence = ""
            first_output = 0
            start_time = time.time()
            index = 1
            session_id = str(uuid4())
            delimiters = {"。", "，", "\n"}
            need_to_remove_delimiters = ["*", "#", "#"]
            for line in response.iter_lines():
                if line and line.startswith(b"data: "):
                    try:
                        json_data = json.loads(line[6:])
                        tts_session_id = json_data.get("id", "")
                        delta = json_data.get("choices", [{}])[0].get("delta", {})
                        content = delta.get("content", "")

                        # 开始返回回复内容
                        if content:
                            if first_output == 0:
                                first_output = 1
                                first_output_time = round(time.time() - start_time, 2)
                                logger.info(f"流式输出开始，耗时: {first_output_time}")
                            accumulated_content += content
                            temp_line += content
                            complete_sentence += content
                            for delimiter in need_to_remove_delimiters:
                                complete_sentence = complete_sentence.replace(delimiter, "")
                            if tts_session_id is not None and tts_session_id != "":
                                complete_sentence = complete_sentence.strip()
                                for char_index, char in enumerate(complete_sentence):
                                    if char in delimiters:
                                        special_signal_index = char_index
                                        origin_complete_sentence = complete_sentence
                                        complete_sentence = complete_sentence[:special_signal_index]
                                        if complete_sentence:
                                            thread = threading.Thread(
                                                target=generate_pcm_data,
                                                args=(tts_session_id, index, complete_sentence)
                                            )
                                            thread.start()
                                            index += 1
                                            complete_sentence = origin_complete_sentence[special_signal_index + 1:].strip()
                                            logger.info(f"after complete_sentence:{complete_sentence}")
                                            break

                            # 添加美团购物链接
                            temp_line, accumulated_content, meituan_link = add_meituan_link(
                                content, 
                                temp_line, 
                                content_lines, 
                                accumulated_content, 
                                temporary_merchant_info,
                                meituan_link
                            )

                            # 流式返回信息
                            stream_data = {
                                "choices": [{
                                    "delta":{
                                        "reasoning_content": None,
                                    },
                                    "finish_reason": None,
                                    "index": 0,
                                    "message": {
                                        "content": content,
                                        "role": "assistant"
                                    }
                                }],
                                "session_id": session_id,
                                "content": accumulated_content,
                                "created": int(time.time()),
                                "id": json_data.get("id", ""),
                                "lastOne": False
                            }

                            yield json.dumps(stream_data, ensure_ascii=False)
                    except json.JSONDecodeError:
                        continue

            if tts_session_id is not None and tts_session_id != "":
                if complete_sentence.strip():
                    threading.Thread(
                        target=generate_pcm_data,
                        args=(tts_session_id, index, complete_sentence)
                    ).start()
                threading.Thread(
                    target=generate_final_pcm_data,
                    args=(tts_session_id, index)
                ).start()

            final_data = {
                "choices": [{
                    "delta":{
                        "reasoning_content": None,
                    },
                    "finish_reason": "stop",
                    "index": 0,
                    "message": {
                        "content": "",
                        "role": "assistant"
                    }
                }],
                "content": accumulated_content,
                "session_id": session_id,
                "created": int(time.time()),
                "id": json_data.get("id", ""),
                "lastOne": True
            }
            logger.info(f"系统 思考内容: {accumulated_cot}")
            logger.info(f"系统 发送回复: {accumulated_content}")
            yield json.dumps(final_data, ensure_ascii=False)
        except Exception as e:
            logger.error(f"使用思维链时，调用美团AI接口时出错: {str(e)}")
        return None

    def deepseek(self, messages, stream = True, tts_session_id = None, model: str = "", token:str|None=None):
        logger.info(f"开始使用deepseek 的DeepThinking过程")
        content_lines = []
        temporary_merchant_info={"id":None, "name":None}
        content = ""
        accumulated_content = ""
        temp_line = ""
        meituan_link = []

        model = lion_config.AI_MODEL if model == "" else model
        logger.info(f'使用模型： {model}')

        data = {
            "model": model,
            "messages": messages,
            "stream": True,
            "temperature": 0.0,
            "max_tokens": 4000,
        }
        logger.info(messages)
        first_output = 0
        start_time = time.time()
        response = send_to_ai(data)

        if response is None:
            yield "抱歉，目前的网络好像有点问题，下次再来和小美喂喂聊天吧。"
            return None

        if response.status_code != 200:
            logger.error(f"API调用失败，状态码: {response.status_code}， 错误信息: {response.text}")
            yield "抱歉，目前的网络好像有点问题，下次再来和小美喂喂聊天吧。"
            return None
        accumulated_content = ""
        complete_sentence = ""
        index = 1
        session_id = str(uuid4())
        delimiters = {"。", "，", "\n"}
        need_to_remove_delimiters = ["*", "#", "#"]
        concat_reasoning_content = ""
        for line in response.iter_lines():
            # logger.info("返回内容" + line.decode("utf-8"))
            if line and line.startswith(b"data: "):
                try:
                    json_data = json.loads(line[6:])
                    tts_session_id = json_data.get("id", "")
                    delta = json_data.get("choices", [{}])[0].get("delta", {})
                    content = delta.get("content", "")
                    reasoning_content = delta.get("reasoning_content", None)

                    # DeepSeek深度思考
                    if reasoning_content: # Deepseek的深度思考
                        concat_reasoning_content += reasoning_content
                        stream_data = {
                            "choices": [{
                                "delta":{
                                    "reasoning_content": reasoning_content,
                                },
                                "finish_reason": None,
                                "index": 0,
                                "message": {
                                    "content": content,
                                    "role": "assistant"
                                }
                            }],
                            "content": accumulated_content,
                            "created": int(time.time()),
                            "id": json_data.get("id", ""),
                            "lastOne": False
                        }
                        yield json.dumps(stream_data, ensure_ascii=False)

                    # 开始返回回复内容
                    if content:
                        if first_output == 0:
                            first_output = 1
                            first_output_time = round(time.time() - start_time, 2)
                            logger.info(f"流式输出开始，耗时: {first_output_time}")
                        accumulated_content += content
                        temp_line += content


                        # TTS
                        complete_sentence += content
                        for delimiter in need_to_remove_delimiters:
                            complete_sentence = complete_sentence.replace(delimiter, "")
                        if tts_session_id is not None and tts_session_id != "":
                            complete_sentence = complete_sentence.strip()
                            for char_index, char in enumerate(complete_sentence):
                                if char in delimiters:
                                    special_signal_index = char_index
                                    origin_complete_sentence = complete_sentence
                                    complete_sentence = complete_sentence[:special_signal_index]
                                    if complete_sentence:
                                        thread = threading.Thread(
                                            target=generate_pcm_data,
                                            args=(tts_session_id, index, complete_sentence)
                                        )
                                        thread.start()
                                        index += 1
                                        complete_sentence = origin_complete_sentence[special_signal_index + 1:].strip()
                                        logger.info(f"after complete_sentence:{complete_sentence}")
                                        break

                        # 添加美团购物链接
                        temp_line, accumulated_content, meituan_link = add_meituan_link(
                            content, 
                            temp_line, 
                            content_lines, 
                            accumulated_content, 
                            temporary_merchant_info,
                            meituan_link
                        )

                        # 流式返回信息
                        stream_data = {
                            "choices": [{
                                "delta":{
                                    "reasoning_content": reasoning_content,
                                },
                                "finish_reason": "stop",
                                "index": 0,
                                "message": {
                                    "content": content,
                                    "role": "assistant"
                                }
                            }],
                            "session_id": session_id,
                            "content": accumulated_content,
                            "created": int(time.time()),
                            "id": json_data.get("id", ""),
                            "lastOne": False
                        }

                        yield (json.dumps(stream_data, ensure_ascii=False))
                except json.JSONDecodeError:
                    continue

        if tts_session_id is not None and tts_session_id != "":
            if complete_sentence.strip():
                threading.Thread(
                    target=generate_pcm_data,
                    args=(tts_session_id, index, complete_sentence)
                ).start()
            threading.Thread(
                target=generate_final_pcm_data,
                args=(tts_session_id, index)
            ).start()

        final_data = {
            "choices": [{
                "delta":{
                    "reasoning_content": reasoning_content,
                },
                "finish_reason": "stop",
                "index": 0,
                "message": {
                    "content": "final data",
                    "role": "assistant"
                }
            }],
            "content": accumulated_content,
            "session_id": session_id,
            "created": int(time.time()),
            "id": json_data.get("id", ""),
            "lastOne": True
        }
        logger.info(f"系统 思考内容: {concat_reasoning_content}")
        logger.info(f"系统 发送回复: {accumulated_content}")
        yield json.dumps(final_data, ensure_ascii=False)

    def _ranker(self, query:list[dict], response_info:list[str], token:str|None=None)->list[float]:

        pass

    def claude_base(self, messages: list[dict['role': str, 'content': str]], stream: bool = False, tts_session_id: None = None, model=None, token:str|None=None)->None:
        logger.info(f"进入claude base生成过程")
        temp_line = ""
        meituan_link = []
        content_lines = [] # 每一行的信息
        temporary_merchant_info = {"id":None, "name":None} # 当前即将要处理的商家信息
        content = ""
        accumulated_content = ""

        
        model = lion_config.AI_MODEL if model == None else model
        messages, system_prompt = convert_standard_format_to_claude_format(messages)
        data = {
            "model": model,
            "messages": messages,
            "stream": stream,
            "temperature": 0.0,
            "max_tokens": 10000,
        }
        if system_prompt != None:
            data["system"] = system_prompt

        first_output = 0
        start_time = time.time()
        response = send_to_ai(data)

        if response is None:
            if stream:
                yield "抱歉，目前的网络好像有点问题，下次再来和小美喂喂聊天吧。"
            return None
        
        if response.status_code != 200:
            logger.error(f"API调用失败，状态码: {response.status_code}， 错误信息: {response.text}")
            if stream:
                yield "抱歉，目前的网络好像有点问题，下次再来和小美喂喂聊天吧。"
            return None
        accumulated_content = ""
        complete_sentence = ""
        index = 1
        session_id = str(uuid4())
        delimiters = {"。", "，", "\n"}
        if stream: # 需要流式输出到用户端
            logger.info("模型开始流式返回")
            tts_session_id = None
            reasoning_content = ""
            for line in response.iter_lines():
                
                if line and line.startswith(b"data: "):
                    try:
                        line = line.decode('utf-8')
                        json_data = json.loads(line[6:])
                        logger.info("返回的内容 {}".format(json_data))
                        response_type = json_data.get("type", None)
                        if response_type == "message_start": # 开始
                            tts_session_id = json_data.get("message", {}).get("id", "")
                            continue
                        elif response_type == "content_block_start": # 内容回复开始
                            continue
                        elif response_type == "content_block_delta":
                            content:str|None = json_data.get("delta", {}).get("text", None) # 流式返回数据

                        # 开始返回回复内容
                        if content: # 内容是存在的
                            if first_output == 0:
                                first_output = 1
                                first_output_time = round(time.time() - start_time, 2)
                                logger.info(f"流式输出开始，耗时: {first_output_time}")
                            accumulated_content += content
                            temp_line += content
                            # TTS
                            complete_sentence += content
                            if tts_session_id is not None and tts_session_id != "":
                                complete_sentence = complete_sentence.strip()
                                complete_sentence = pre_handle_one_line_text(complete_sentence)
                                for char_index, char in enumerate(complete_sentence):
                                    if char in delimiters:
                                        special_signal_index = char_index
                                        origin_complete_sentence = complete_sentence
                                        complete_sentence = complete_sentence[:special_signal_index]
                                        if complete_sentence:
                                            thread = threading.Thread(
                                                target=generate_pcm_data,
                                                args=(tts_session_id, index, complete_sentence)
                                            )
                                            thread.start()
                                            index += 1
                                            complete_sentence = origin_complete_sentence[special_signal_index + 1:].strip()
                                            logger.info(f"after complete_sentence:{complete_sentence}")
                                            break
                            
                            # 添加美团购买链接
                            temp_line, accumulated_content, meituan_link = add_meituan_link(
                                content, 
                                temp_line, 
                                content_lines, 
                                accumulated_content, 
                                temporary_merchant_info,
                                meituan_link
                            )

                            if "\n" in temp_line:
                                temp_line = temp_line[temp_line.find('\n')+1:]

                            # 流式返回信息
                            stream_data = {
                                "choices": [{
                                    "delta":{
                                        "reasoning_content": reasoning_content,
                                    },
                                    "finish_reason": None,
                                    "index": 0,
                                    "message": {
                                        "content": content,
                                        "role": "assistant"
                                    }
                                }],
                                "session_id": session_id,
                                "content": accumulated_content,
                                "created": int(time.time()),
                                "id": tts_session_id,
                                "lastOne": False
                            }

                            yield json.dumps(stream_data, ensure_ascii=False)
                    except json.JSONDecodeError:
                        continue
            
            if tts_session_id is not None and tts_session_id != "":
                if complete_sentence.strip():
                    threading.Thread(
                        target=generate_pcm_data,
                        args=(tts_session_id, index, complete_sentence)
                    ).start()
                threading.Thread(
                    target=generate_final_pcm_data,
                    args=(tts_session_id, index)
                ).start()
            final_data = {
                "choices": [{
                    "delta":{
                        "reasoning_content": reasoning_content,
                    },
                    "finish_reason": "stop",
                    "index": 0,
                    "message": {
                        "content": "",
                        "role": "assistant"
                    }
                }],
                "content": accumulated_content,
                "session_id": session_id,
                "created": int(time.time()),
                "id": tts_session_id,
                "lastOne": True
            }
            logger.info("模型最终的思考过程为: {}".format("空"))
            logger.info("模型最终的回复为: {}".format(accumulated_content))
            yield json.dumps(final_data, ensure_ascii=False)
            return
        else:
            json_data = json.loads(response.text)
            logger.info(f"模型直接返回的的结果: {json_data}")
            yield json_data["choices"][0]["message"]["content"]
        return None

    def claude_thinking(self, messages: list[dict['role': str, 'content': str]], stream: bool = False, tts_session_id: None = None, model=None, token:str|None=None) -> None:
        logger.info(f"进入claude thinking生成过程")
        temp_line = ""
        meituan_link = []
        content_lines = [] # 每一行的信息
        temporary_merchant_info = {"id":None, "name":None} # 当前即将要处理的商家信息
        content = ""
        accumulated_content = ""
        concat_reasoning_content = ""
        
        model = lion_config.AI_MODEL if model == None else model
        messages, system_prompt = convert_standard_format_to_claude_format(messages)
        data = {
            "model": model,
            "messages": messages,
            "stream": stream,
            "temperature": 1.0,
            "max_tokens": 10000,
            "thinking": {
                "type": "enabled",
                "budget_tokens": 4000 
            }
        }
        if system_prompt != None:
            data["system"] = system_prompt

        first_output = 0
        start_time = time.time()
        response = send_to_ai(data)

        if response is None:
            if stream:
                yield "抱歉，目前的网络好像有点问题，下次再来和小美喂喂聊天吧。"
            return None
        
        if response.status_code != 200:
            logger.error(f"API调用失败，状态码: {response.status_code}， 错误信息: {response.text}")
            if stream:
                yield "抱歉，目前的网络好像有点问题，下次再来和小美喂喂聊天吧。"
            return None
        accumulated_content = ""
        complete_sentence = ""
        index = 1
        session_id = str(uuid4())
        delimiters = {"。", "，", "\n"}
        if stream: # 需要流式输出到用户端
            logger.info("模型开始流式返回")
            tts_session_id = None
            reasoning_content = ""
            for line in response.iter_lines():
                if line and line.startswith(b"data: "):
                    try:
                        line = line.decode('utf-8')
                        json_data = json.loads(line[6:])
                        # logger.info("json {}".format(json_data))
                        response_type = json_data.get("type", None)
                        if response_type == "message_start": # 开始
                            tts_session_id = json_data.get("message", {}).get("id", "")
                            continue
                        elif response_type == "content_block_start": # 内容回复开始
                            continue
                        elif response_type == "content_block_delta":
                            content:str|None = json_data.get("delta", {}).get("text", "") # 流式返回数据
                            reasoning_content:str|None = json_data.get("delta", {}).get("thinking", None)
                        if reasoning_content:
                            concat_reasoning_content += reasoning_content
                            stream_data = {
                                "choices": [{
                                    "delta":{
                                        "reasoning_content": reasoning_content,
                                    },
                                    "finish_reason": None,
                                    "index": 0,
                                    "message": {
                                        "content": content,
                                        "role": "assistant"
                                    }
                                }],
                                "content": accumulated_content,
                                "created": int(time.time()),
                                "id": tts_session_id,
                                "lastOne": False
                            }
                            yield json.dumps(stream_data, ensure_ascii=False)
                            continue

                        # 开始返回回复内容
                        if content != "" or content == None: # 内容是存在的
                            if first_output == 0:
                                first_output = 1
                                first_output_time = round(time.time() - start_time, 2)
                                logger.info(f"流式输出开始，耗时: {first_output_time}")
                            accumulated_content += content
                            temp_line += content
                            # TTS
                            complete_sentence += content
                            if tts_session_id is not None and tts_session_id != "":
                                complete_sentence = complete_sentence.strip()
                                complete_sentence = pre_handle_one_line_text(complete_sentence)
                                for char_index, char in enumerate(complete_sentence):
                                    if char in delimiters:
                                        special_signal_index = char_index
                                        origin_complete_sentence = complete_sentence
                                        complete_sentence = complete_sentence[:special_signal_index]
                                        if complete_sentence:
                                            thread = threading.Thread(
                                                target=generate_pcm_data,
                                                args=(tts_session_id, index, complete_sentence)
                                            )
                                            thread.start()
                                            index += 1
                                            complete_sentence = origin_complete_sentence[special_signal_index + 1:].strip()
                                            logger.info(f"after complete_sentence:{complete_sentence}")
                                            break
                            
                            # 添加美团购买链接
                            temp_line, accumulated_content, meituan_link = add_meituan_link(
                                content, 
                                temp_line, 
                                content_lines, 
                                accumulated_content, 
                                temporary_merchant_info,
                                meituan_link
                            )

                            if "\n" in temp_line:
                                temp_line = temp_line[temp_line.find('\n')+1:]

                            # 流式返回信息
                            stream_data = {
                                "choices": [{
                                    "delta":{
                                        "reasoning_content": reasoning_content,
                                    },
                                    "finish_reason": None,
                                    "index": 0,
                                    "message": {
                                        "content": content,
                                        "role": "assistant"
                                    }
                                }],
                                "session_id": session_id,
                                "content": accumulated_content,
                                "created": int(time.time()),
                                "id": tts_session_id,
                                "lastOne": False
                            }

                            yield json.dumps(stream_data, ensure_ascii=False)
                    except json.JSONDecodeError:
                        continue
            
            if tts_session_id is not None and tts_session_id != "":
                if complete_sentence.strip():
                    threading.Thread(
                        target=generate_pcm_data,
                        args=(tts_session_id, index, complete_sentence)
                    ).start()
                threading.Thread(
                    target=generate_final_pcm_data,
                    args=(tts_session_id, index)
                ).start()
            final_data = {
                "choices": [{
                    "delta":{
                        "reasoning_content": reasoning_content,
                    },
                    "finish_reason": "stop",
                    "index": 0,
                    "message": {
                        "content": "",
                        "role": "assistant"
                    }
                }],
                "content": accumulated_content,
                "session_id": session_id,
                "created": int(time.time()),
                "id": tts_session_id,
                "lastOne": True
            }
            logger.info("模型最终的思考过程为: {}".format(concat_reasoning_content))
            logger.info("模型最终的回复为: {}".format(accumulated_content))
            yield json.dumps(final_data, ensure_ascii=False)
            return
        else:
            json_data = json.loads(response.text)
            logger.info(f"模型直接返回的的结果: {json_data}")
            yield json_data["choices"][0]["message"]["content"]
        return None

def generate_pcm_data(session_id: str, index: int, complete_sentence: str):
    logger.info(f"generate_pcm_data, index:{index}, complete_sentence:{complete_sentence}")
    tts_response = TtsClient.get_instance().call_synthesize(complete_sentence)
    redis_client = RedisClient.get_instance().get_client()
    if tts_response.data:
        base64_pcm_data = base64.b64encode(tts_response.data).decode("utf-8")
        message = {
            'index': index,
            'pcm_data': base64_pcm_data
        }
        logger.info(f"push index_{index} pcm data into squirrel")
        redis_client.lpush(build_category_key('tts_pcm_data', "t{0}", session_id), json.dumps(message))
        redis_client.expire(build_category_key('tts_pcm_data', "t{0}", session_id), 300)
    else:
        message = {
            'index': index,
            'pcm_data': None
        }
        logger.info(f"push index_{index} pcm data into squirrel")
        redis_client.lpush(build_category_key('tts_pcm_data', "t{0}", session_id), json.dumps(message))
        redis_client.expire(build_category_key('tts_pcm_data', "t{0}", session_id), 300)

def generate_final_pcm_data(session_id: str, final_index: int):
    logger.info(f"generate_final_pcm_data, session_id:{session_id}")
    redis_client = RedisClient.get_instance().get_client()
    message = {
        'index': -final_index,
        'pcm_data': None
    }
    redis_client.lpush(build_category_key('tts_pcm_data', "t{0}", session_id), json.dumps(message))
    redis_client.expire(build_category_key('tts_pcm_data', "t{0}", session_id), 300)

def pre_handle_one_line_text(text):
    # 去除链接（包括圆括号）
    text = re.sub(r'\([^)]*://[^)]*\)', '', text)
    # 中文冒号转为空格
    text = re.sub(r'[：*#]', '\n', text)
    # 然后清除多余的部分，保留中文字符、数字和指定标点符号
    text = re.sub(r'[^\u4e00-\u9fa50-9,.+!?，。！？\s\w]', '', text)
    return text

