import sys
import json
from typing import List, Dict, Any
from abc import ABC, ABCMeta, abstractmethod
from utils.logger import logger
from service.ai_client import send_to_ai
from service import rag, context
from service.analyze_edit_request import analyze_edit_request
from service.analyze_delete_request import analyze_delete_request
from utils.threaded_generator import ThreadedGenerator
from configs import lion_config
from configs.local_config import DEFAULT_OUTPUT_PROMPT
from service.context import generate_food_img_url
from service.ESmemory.es_memory_service import begin_memory_service
from service.ESmemory.es_memory_callback import callback_memory
from configs.config import CURRENT_ENV

# 忽略SSL警告
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

sys.path.append("/Users/<USER>/weiwei-new/app")
from service.ai_client import send_to_ai
from utils.logger import logger, update_trace_id

# 项目代码
from utils.threaded_generator import ThreadedGenerator
from service.ai_client import send_to_ai
from service.COT.registry_pool import Registry
from configs import lion_config
from service.request_aigc import send
from service import order, rag
from utils.extract import parse_user_response, fill_shop_dict
from service.insight import get_insight_keywords_sql
from utils.utils import timeit
from service import ingredient
import vex.vex_thrift as vex
from service.insight import get_insight_keywords_sql
from service.ESmemory.es_portrait_service import begin_portrait_service, get_portrait_by_id
from service.ESmemory.es_memory_initialization import initialize_memory
from service.share_to_plaza import PLAZA_INDEX, upload_to_plaza, generate_share_content,get_plaza_share_by_id, search_plaza_contains_exact_phrase, update_plaza_share_content, delete_plaza_share, update_shop_comment_graph, update_user_comment_graph
'''
{
    "type": "function",
    "function": {
        "name": "demo",
        "description": "一定要调用这个函数，随便生成点什么作为参数，一定要调用，一定要调用",
        "parameters": {
            "type": "object",
            "required": [
                "location"
            ],
            "properties": {
                "location": {
                    "type": "string",
                    "description": "随便生成的内容都可以"
                }
            }
        }
    }
}
'''
def get_tool_json_scheme(tool_name:str)->Dict[str, Any]:
    tool_class = Registry.get_class(tool_name)
    if tool_class is None:
        raise Exception(f"工具 {tool_name} 不存在")
    return tool_class.json_scheme

def function_calling(function_name, method_name="call", **kwargs):
    try:
        # 记录token参数状态（若存在）
        if "token" in kwargs:
            logger.debug(f"Function {function_name}.{method_name} called with token status: {kwargs['token'] is not None}")
        
        # 获取函数
        function = Registry.get_class(function_name)
        if function is None:
            raise Exception(f"函数 {function_name} 不存在")
        
        # 检查是否为特殊agent（不是main或基本类agent）
        # 如果是特殊agent，需要过滤参数
        if function_name != "main" and not function_name.endswith("_agent"):
            # 获取函数的参数列表
            import inspect
            func_method = getattr(function, method_name)
            sig = inspect.signature(func_method)
            valid_params = sig.parameters.keys()
            
            # 过滤参数，只保留函数定义中存在的参数
            filtered_kwargs = {k: v for k, v in kwargs.items() if k in valid_params}
            
            # 使用过滤后的参数调用函数
            return func_method(**filtered_kwargs)
        else:
            # 对于main或基本agent，保持原有行为
            func_method = getattr(function, method_name)
            return func_method(**kwargs)
    except Exception as e:
        # 记录错误日志
        logger.error(f"Function Calling的过程中出现错误，你的输入为: Function Name: {function_name}, kwargs: {kwargs}, 错误: {str(e)}")
        # 可以选择是否重新抛出异常
        raise e


def generete_function_calling_quert(messages:list):
    function_calling_model = lion_config.lion_client.get_value_with_default("weiwei.function_calling_model", "gpt-4o-mini")
    query = {
        "model": function_calling_model,
        "messages": messages,
        "tools": [],
        "tool_choice": "auto",
        "max_tokens": 10000
    }
    query_tool = []
    tools_list = Registry.list_registered()
    for tool in tools_list:
        agent_class = Registry.get_class(tool)
        if agent_class is None:
            logger.error(f"找不到工具:{tool}")
            continue
        query_tool.append(agent_class.json_scheme)
        
    query["tools"] = query_tool
    return query

# 用于自动生成JsonScheme的Meta类
class ToolMeta(ABCMeta):
    """元类，用于检查子类是否定义了所需的属性并自动注册"""
    def __new__(mcs, name, bases, namespace):
        # 创建类
        cls = super().__new__(mcs, name, bases, namespace)
        
        # 跳过 BaseTool 类本身的处理
        if name == 'BaseTool':
            return cls
            
        # 检查是否是 BaseTool 的子类
        is_base_agent_subclass = False
        for base in bases:
            if base.__name__ == 'BaseTool':
                is_base_agent_subclass = True
                break
                
        # 只处理 BaseTool 的子类
        if is_base_agent_subclass:
            # 检查必须的类属性是否存在
            required_attrs = ['function_name', 'function_description', 'parameters']
            missing_attrs = [attr for attr in required_attrs if attr not in namespace]
            
            if missing_attrs:
                raise TypeError(f"类 {name} 缺少必要的类属性: {', '.join(missing_attrs)}")
            
            # 检查是否定义了 call 静态方法
            if 'call' not in namespace or not isinstance(namespace['call'], staticmethod):
                raise TypeError(f"类 {name} 必须定义 @staticmethod call 方法")
            
            # 获取 function_name
            function_name = namespace.get('function_name')
            function_description = namespace.get('function_description')
            parameters = namespace.get('parameters')
            if function_name:
                # 将类注册到 Registry
                Registry.register(function_name)(cls)
                requires = []
                properties = {}
                for param in parameters:
                    param_type = param["type"]
                    param_name = param["name"]
                    param_description = param["description"]
                    required = param.get("required", False)
                    properties[param_name] = {
                        "type": param_type,
                        "description": param_description,
                    } if param_type not in ["object", "array"] else ({
                        "type": param_type,
                        "description": param_description,
                        "items": param.get("items", {})
                    } if param_type == "array" else {
                        "type": param_type,
                        "description": param_description,
                        "properties": param.get("properties", {})
                    })
                    if required:
                        requires.append(param_name)
                json_scheme = {
                    "type": "function",
                    "function": {
                        "name": function_name,
                        "description": function_description,
                        "parameters": {
                            "type": "object",
                            "required": requires,
                            "properties": properties
                            }
                        }
                    }
                
                # 设置为类属性而不是模块属性
                setattr(cls, "json_scheme", json_scheme)
                
                # 记录日志
                logger.info(f"自动注册函数: {function_name}")
        
        return cls

# 基类，所有的Agent都应该继承这个类，会自动注册到Agent池中，用于以后的调用
class BaseTool(ABC, metaclass=ToolMeta):
    """
    Function基类，所有的Function都需要继承该类
    所有子类必须定义以下类属性:
    - function_name: str
    - function_description: str
    - parameters: list[dict]
    """
    # 在基类中定义抽象类属性
    function_name: str
    function_description: str
    parameters: list[dict]

    @staticmethod
    @abstractmethod
    def call(*args, **kwargs):
        """所有子类必须实现这个静态方法"""
        pass

class DemoAgent(BaseTool):
    function_name = "demo"
    function_description = "一个简单的demo函数"
    parameters = [{
        "name": "location",
        "description": "随便生成的内容都可以",
        "type": "string",
        "required": True
    }]

    @staticmethod  # 添加缺失的 staticmethod 装饰器
    def call(location):
        """调用函数的入口"""
        return f"你调用了demo函数，location是{location}"

class CodeAgent(BaseTool):
    function_name = "code"
    function_description = "用于运行指定的python代码内容，来提供更加精确的查询信息，以及验证推理的正确性。"
    parameters = [{
        "name": "code",
        "description": "所需运行的python代码的具体内容",
        "type": "string",
        "required": True
    }]

    @staticmethod
    def call(code):
        """调用函数的入口"""
        import io
        import sys
        
        # 捕获标准输出
        old_stdout = sys.stdout
        new_stdout = io.StringIO()
        sys.stdout = new_stdout
        
        # 创建本地命名空间
        local_vars = {}
        
        try:
            # 执行代码
            exec(code, {}, local_vars)
            # 获取打印输出
            output = new_stdout.getvalue()
            # 返回输出和变量
            return {
                "output": output,
                "variables": local_vars
            }
        finally:
            # 恢复标准输出
            sys.stdout = old_stdout

class ChatAgent(BaseTool):
    function_name = "chat"
    function_description = "用于聊天，可以通过与大模型沟通，获取更多的想法或验证自身想法的准确性。"
    parameters = [{
        "name": "prompt",
        "description": "根据用户对话信息，给的返回建议和思考",
        "type": "string",
        "required": True
    }]

    @staticmethod  # 添加缺失的 staticmethod 装饰器
    def call(prompt: str, history: list[dict['content':str]], current_user_id: int,   
            generator: ThreadedGenerator, tts_session_id: None, user_orders=None,
            user_feature: str = "", personal_reply: str = "", memory_content: str= "", \
            mis_id: str = "", model:str="gpt-4o-mini", model_type:str="gpt-4o-mini", model_config:dict={}, token:str|None=None) -> None:

        logger.info(f"给用户返回信息的指导建议为: {prompt}")
        """处理更多数据请求

        Args:
            memory_content: 记忆内容
            intent: 用户意图
            history: 聊天历史记录
            current_user_id: 当前用户ID
            generator: 生成器实例
            user_orders: 用户订单数据
            tts_session_id: tts会话id
            user_feature: 用户画像
            personal_reply: 回复规则
            mis_id: 美团内部ID
        """
        try:

            # # 初始化ES用户画像
            # es_user_portrait = ""

            # # 处理用户画像
            # if mis_id:
            #     try:
            #         # 使用传入的mis_id和current_user_id
            #         logger.info(f"开始获取用户画像，用户ID: {current_user_id}, MIS ID: {mis_id}")
            #         portrait_result = get_portrait_by_id(mis_id)
            #         if not portrait_result:
            #             # 如果没有找到画像，生成并上传
            #             logger.info(f"未找到用户画像，开始生成新画像，用户ID: {current_user_id}, MIS ID: {mis_id}")
            #             portrait = get_user_portrait(mis_id, user_orders, memory_content)  # 使用mis_id获取用户画像
            #             if portrait:
            #                 # 上传到ES
            #                 upload_result = upload_to_es(mis_id, str(current_user_id), portrait)
            #                 if upload_result:
            #                     logger.info(f"成功生成并上传用户画像，用户ID: {current_user_id}, MIS ID: {mis_id}")
            #                     es_user_portrait = portrait
            #         else:
            #             logger.info(f"成功找到现有用户画像，用户ID: {current_user_id}, MIS ID: {mis_id}")
            #             es_user_portrait = portrait_result.get("portrait", "")
            #             logger.info(f"当前用户画像为：{es_user_portrait}")
            #     except Exception as e:
            #         logger.error(f"处理用户画像时发生错误: {str(e)}")

            if mis_id:
                try:    
                    es_user_portrait = get_portrait_by_id(mis_id)
                except Exception as e:
                    logger.error(f"获取用户画像时发生错误: {str(e)},当前agent为:Chat")
                    es_user_portrait = ""

            # 构建系统提示词，包含用户画像和ES中的用户画像
            system_prompt = lion_config.lion_client.get_value_with_default("weiwei.system_prompt", "")
            if user_feature:
                system_prompt += f"\n用户画像：{user_feature}"
            if es_user_portrait:
                system_prompt += f"\nES用户画像：{es_user_portrait}"
            if personal_reply:
                system_prompt += f"\n回复规则：{personal_reply}"
            # 添加用户历史订单数据
            if user_orders:
                system_prompt += f"\n用户历史订单：{json.dumps(user_orders, ensure_ascii=False)}"
            # 添加记忆
            if memory_content:
                system_prompt += f"\n记忆内容：{memory_content}"

            system_prompt += f"\n当前时间：{context.get_time_info()}"
            system_prompt += f"\n当前天气：{context.get_weather_prompt()}"

            history_tmp = [{
                "role": "system",
                "content": system_prompt
            }] + history + [
                            {
                                "role": "assistant",
                                "content": prompt
                            }
                        ]
            logger.info(history_tmp)

            send(history_tmp, tts_session_id, current_user_id, generator, model=model, model_type=model_type)

            try:
                # 生成记忆内容
                memory_facts = begin_memory_service(mis_id=mis_id,history=history,user_orders=user_orders,token=token,model_type=model_type,success=True,function_name="Chat")

            except Exception as e:
                logger.error(f"上传记忆时出错: {str(e)}")
            try:
                # 更新用户画像
                # from service.user_portrait import update_portrait_by_mis_id
                # mt_user_id = str(current_user_id)  # 确保是字符串类型
                # if memory_facts:
                #     success = update_portrait_by_mis_id(mis_id, mt_user_id, limit=2, memory_content=memory_facts[-1])
                #     if success:
                #         logger.info(f"ChatAgent: 成功更新用户画像，用户ID: {mt_user_id}")
                #     else:
                #         logger.error(f"ChatAgent: 更新用户画像失败，用户ID: {mt_user_id}")
                # else:
                #     logger.info(f"ChatAgent: 没有生成记忆内容，不更新用户画像")
                begin_portrait_service(mis_id=mis_id,history=history,function_name="Chat")
            except Exception as e:
                logger.error(f"ChatAgent: 更新用户画像时出错: {str(e)}")
                

        except Exception as e:
            logger.error(f"用户{current_user_id} 处理推荐对话时出错: {str(e)}")
            generator.send("小美，不知道要说什么啦，换个问题好不好嘛。")
            generator.close()

class ReflectionAgent(BaseTool):
    function_name = "reflection"
    function_description = "用于反思自己的想法，并提出反思的结果，用于确定思考过程是否结束，以及目前的结果是否符合回复预期。"
    parameters = [{
        "name": "prompt",
        "description": "用于与大模型沟通的文本内容",
        "type": "string",
        "required": True
    }]

    @staticmethod
    def call(prompt):
        """调用函数的入口"""
        while True:
            reflect_prompt = lion_config.REFLECT_PROMPT
            data = [
                {"role":"system", "content":reflect_prompt},
                {"role":"user", "content":prompt}
            ]
            query = generete_function_calling_quert(data)
            ai_response = send_to_ai(query)
            response_json = json.loads(ai_response.text)
            ret_message = response_json["choices"][0]
            if ret_message["finish_reason"] == "tool_calls": # 进行函数调用
                function_info = ret_message["message"]["tool_calls"]["function"]
                function_name = function_info["name"]
                function_args = json.loads(function_info["arguments"])
                logger.info(f"开始进行函数调用:{function_name}, args={function_args}")
                function_response = function_calling(function_name, method_name="call", **function_args)
                prompt += f"{function_name}对于{Registry.get_class(function_name).function_description}的函数响应：\n" + function_response + "\n"
                continue
            elif ret_message["finish_reason"] == "stop": # 终止调用
                content = ret_message["message"]["content"]
                logger.info(f"ReflectionAgent终止调用, {content}")
                prompt += "Reflection 阶段结束，反思结果为: \n" + content + "\n"
                return prompt
            else:
                logger.error("未知的 finish_reason: " + ret_message["finish_reason"])
                break
        return prompt

class MainAgent(BaseTool):
    function_name = "Main"
    function_description = "主Agent，在程序的一开始调用，用于自动决定后续采取的策略和流程。本身并不涉及被调用。"
    parameters = [
        {
            "name": "history",
            "description": "大模型与用户的聊天记录",
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "role": {
                        "type": "string",
                        "description": "消息的角色，如'user'或'assistant'或'system'",
                        "enum": ["user", "assistant", "system"]
                    },
                    "content": {
                        "type": "string",
                        "description": "消息的内容"
                    }
                },
                "required": ["role", "content"]
            },
            "required":True
        }
    ]
    
    @staticmethod
    def _construct_pretools(name:str, step_num:int, tool_status:str, search_word_list:list, reason:str, tool_return:list=None, last_one:bool=False)->list:
        return {
            "tool": name,
            "toolStep": step_num,
            "toolStatus": tool_status,
            "tool_input": {
                "search_word_list": search_word_list if isinstance(search_word_list, list) else [search_word_list],
                "reason": reason
            },
            "tool_return": tool_return,
            "description": "这是一条测试信息"
        } if tool_return is not None else {
            "tool": name,
            "toolStep": step_num,
            "toolStatus": tool_status,
            "tool_input": {
                "search_word_list": search_word_list if isinstance(search_word_list, list) else [search_word_list],
                "reason": reason
            },
            "description": "这是一条测试信息"
        }  

    @staticmethod
    def _get_stream_dict(pre_tools:list=[])->str:
        import time
        """构建流式响应字典
        
        Args:
            step_num: 工具步骤序号
            tool_status: 工具状态，例如"RUNNING"、"FINISHED"
            search_word_list: 搜索关键词列表
            reason: 分析说明文本
            tool_return: 工具返回结果列表
            last_one: 是否最后一个响应
            
        Returns:
            dict: 流式响应字典
        """
 
        return json.dumps({
            "created": int(time.time()),
            "loadingStatus": False,
            "lastOne": False,
            "pre_tools": pre_tools
        }, ensure_ascii=False)

    @staticmethod
    @timeit
    def call(token: str, history: list[dict['content':str]], user_id: int, tts_session_id: None,
              generator: ThreadedGenerator, trace_id: str, user_feature: str = "", personal_reply: str = "",
              memory_content: str = "", mis_id: str = "", tools:list[str]=[], model_type:str = "gpt-4o-mini"):
        import time
        start_time = time.time()
        update_trace_id(trace_id)
        messages = history
        model_config = lion_config.lion_client.get_value(f"weiwei.{model_type}")
        model_config = json.loads(model_config)
        generator.send(json.dumps({
            "warm_content":"测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测\
                试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\
                    测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\
                        测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\
                            测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\
                                "
        }))
        generator.send(json.dumps({
            "warm_content":"测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测\
                试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\
                    测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\
                        测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\
                            测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\
                                "
        }))
        generator.send(json.dumps({
            "warm_content":"测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测\
                试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\
                    测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\
                        测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\
                            测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试\
                                "
        }))
        end_time_1 = time.time()
        logger.info(f"Send warm_content 耗时: {end_time_1 - start_time}")
        # 确保 model_config 是一个字典，如果是字符串则尝试解析JSON
        # if isinstance(model_config, str):
        #     try:
        #         model_config = json.loads(model_config)
        #     except json.JSONDecodeError:
        #         logger.error(f"无法解析model_config字符串为JSON: {model_config}")
        #         model_config = {}
        

        logger.info(f"当前模型设置:{model_config}")
        limit = model_config.get("weiwei.ai_order_limit", 20)
        limit = int(limit)
        logger.info(f"limit is int:{isinstance(limit, int)}")
        logger.info(f"main agent limit:{limit}， 当前模型为:{model_type}")
        user_orders = order.get_user_orders_v2(token, user_id, limit)
        # logger.info(f"user_orders:{user_orders}")

        AI_MODEL = lion_config.MODEL_MAP.get(model_type, "gpt-4o-2024-05-13")
        MODEL_CONFIG = json.loads(lion_config.lion_client.get_value(f"weiwei.{AI_MODEL}"))
        
        """获取用户意图

        Args:
            messages: 消息列表
            user_id: 用户ID
            mis_id: 美团内部ID

        Returns:
            list: 意图列表
        """
        def extract_intent_from_str(intent:str)->list|str:
            try:
                text_dict = json.loads(intent)
                text_list = []
                text_list.extend(list(text_dict.keys()))
                for values in text_dict.values():
                    if isinstance(values, list):
                        text_list.extend(values)
                
                intent = text_list[:lion_config.INTENT_NUM]
                logger.info(f"模型提取的用户意图解析结果: {intent}")
                return intent
            except Exception as e:
                logger.info(f"模型提取的用户意图解析结果为空， 错误信息为: {str(e)}")
                return intent

        # mini 微型对话函数，如此可以看出流式返回有多么臃肿
        def get_intent_from_ai(formatted_messages, tools:list[str]|None=None)->dict:
            query_input = {
                "model": lion_config.INTENT_MODEL,
                "messages": formatted_messages,
                "stream": False,
                "temperature": 0.0,
                "max_tokens": 4000,
                "tools": tools
            } if tools else {
                "model": lion_config.INTENT_MODEL,
                "messages": formatted_messages,
                "stream": False,
                "temperature": 0.0,
                "max_tokens": 4000
            }
            response = send_to_ai(query_input)
            json_data = json.loads(response.text)
            logger.info(f"模型直接返回的的结果: {json_data}")
            return json_data["choices"][0]["message"]

        try:
            pre_tools = []
            index_name = lion_config.lion_client.get_value_with_default("weiwei.memory_index", "memory_es_test")
            memory_name = lion_config.lion_client.get_value("weiwei.memory_name")
            pre_tools.append(MainAgent._construct_pretools(
                name=memory_name,
                step_num=1,
                tool_status="RUNNING",
                search_word_list=[],
                reason="正在从记忆中检索相关信息..."
            ))
            generator.send(MainAgent._get_stream_dict(pre_tools))
            end_time_2 = time.time()
            logger.info(f"Send memory_content Searching耗时: {end_time_2 - end_time_1}") 
            memory_content = callback_memory(index_name=index_name, user_input=history[-1]["content"], user_id=mis_id, long_count=3, short_count=3)
            # 展示记忆内容
            logger.info("记忆内容开始")
            logger.info(memory_content)
            logger.info("记忆内容结束")
            pre_tools[-1]["toolStatus"] = "FINISHED"
            generator.send(MainAgent._get_stream_dict(pre_tools))
            end_time_3 = time.time()
            logger.info(f"Send memory_content Finished耗时: {end_time_3 - end_time_2}")
            # 初始化长期记忆
            logger.info(f"初始化长期记忆，用户ID: {mis_id}")
            initialize_memory(index_name, mis_id, user_orders)
            logger.info(f"初始化长期记忆结束，用户ID: {mis_id}")

            es_portrait_name = lion_config.lion_client.get_value("weiwei.es_portrait_name")
            pre_tools.append(MainAgent._construct_pretools(
                es_portrait_name, 
                1, 
                "SEARCHING", 
                [], 
                "正在搜索用户画像", 
                None, 
                False   
            ))
            generator.send(MainAgent._get_stream_dict(pre_tools))
            end_time_4 = time.time()
            logger.info(f"Send es_portrait_name Searching耗时: {end_time_4 - end_time_3}")
            # if mis_id:
            #     try:
            #         from service.user_portrait import search_user_portrait
            #         portrait_result = search_user_portrait(mis_id, str(user_id))
            #         if portrait_result:
            #             es_user_portrait = portrait_result.get("portrait", "")
            #             logger.info(f"成功获取用户画像用于意图解析，用户ID: {user_id}, MIS ID: {mis_id}")
            #     except Exception as e:
            #         logger.error(f"获取用户画像时发生错误: {str(e)}")

            if mis_id:
                try:    
                    es_user_portrait = get_portrait_by_id(mis_id)
                except Exception as e:
                    logger.error(f"获取用户画像时发生错误: {str(e)},当前agent为:Chat")
                    es_user_portrait = ""

            pre_tools[-1]["toolStatus"] = "FINISHED"
            generator.send(MainAgent._get_stream_dict(pre_tools))
            end_time_5 = time.time()
            logger.info(f"Send es_portrait_name Finished耗时: {end_time_5 - end_time_4}")
            
            formatted_messages = [{
                'role': 'system',
                'content': lion_config.INTENT_PROMPT
            }]

            # 如果没有消息，直接返回空意图
            if not messages:
                logger.info(f"no history, 记忆内容{memory_content}")
                raw_intent = get_intent_from_ai(formatted_messages, tools)
            else:
                # 获取最近的对话历史
                context_content = "以下是最近的对话历史（仅供参考）：\n\n"
                
                # 保留最后10条消息或更少
                recent_messages = messages[-10:]
                
                # 构建对话历史
                last_user_msg = None
                last_assistant_msg = None
                dialog_pairs = []
                
                for msg in recent_messages:
                    if msg['role'] == 'user':
                        if last_user_msg and last_assistant_msg:
                            dialog_pairs.append((last_user_msg, last_assistant_msg))
                        last_user_msg = msg['content']
                        last_assistant_msg = None
                    elif msg['role'] == 'assistant':
                        if last_user_msg:
                            last_assistant_msg = msg['content']
                
                # 如果最后一组对话不完整，但有用户消息，也加入对话对
                if last_user_msg and last_assistant_msg:
                    dialog_pairs.append((last_user_msg, last_assistant_msg))
                    
                # 只保留最近的5对对话
                dialog_pairs = dialog_pairs[-5:]
                
                # 构建对话历史文本
                for user_msg, assistant_msg in dialog_pairs:
                    context_content += f"用户：{user_msg}\n助手：{assistant_msg}\n\n"
                    
                # 添加当前用户的最新消息（如果最后一条是用户消息）
                if recent_messages and recent_messages[-1]['role'] == 'user':
                    context_content += f"当前用户问题：{recent_messages[-1]['content']}"
                
                if memory_content:
                    context_content += f"\n记忆内容:{memory_content}\n"
                
                formatted_messages.append({
                    'role': 'user',
                    'content': context_content
                })
                
                logger.info(f"发送给意图模型的消息：{formatted_messages}")        
                raw_intent = get_intent_from_ai(formatted_messages, tools)
                logger.info(f"意图模型返回的结果：{raw_intent}")
                end_time_6 = time.time()
                logger.info(f"Send intent_model Finished耗时: {end_time_6 - end_time_5}")
            if raw_intent.get("content", None):
                intent:list|str = extract_intent_from_str(raw_intent["content"])
                if isinstance(intent, list):
                    logger.debug(f"MainAgent调用OrderAdvisorAgent，token存在状态: {token is not None}")
                    function_calling("Order", "call", intent=intent, history=history, current_user_id=user_id,
                    generator=generator, tts_session_id=tts_session_id, user_orders=user_orders,
                    user_feature=user_feature, personal_reply=personal_reply, memory_content=memory_content, mis_id=mis_id, model=AI_MODEL, model_type=model_type, model_config=MODEL_CONFIG, token=token,
                    pre_tools=pre_tools)
                else:
                    messages = history + [{"role": "assistant", "content": intent}]
                    send(messages, tts_session_id, user_id, generator, model=AI_MODEL, model_type=model_type, token=token)
            else:
                # function calling
                func_info = raw_intent.get("tool_calls", [])
                if len(func_info) == 0:
                    logger.error(f"用户{user_id} 获取用户意图时出错: 没有找到函数调用")
                    generator.send("小美累了！不想回复啦！")
                    generator.close()
                    return None
                else:
                    # 正式的函数调用
                    # 目前分为两类
                    # 1. Order
                    # 2. Chat
                    logger.info(f"当前提取到的函数为: [{func_info}]")
                    func_details:dict = func_info[0]
                    func_name = func_details.get("function")["name"]
                    func_args = json.loads(func_details.get("function")["arguments"])
                    if func_name == "Order":
                        function_calling(func_name, "call", intent=func_args["intent"], history=history, current_user_id=user_id,
                        generator=generator, tts_session_id=tts_session_id, user_orders=user_orders,
                        user_feature=user_feature, personal_reply=personal_reply, memory_content=memory_content, mis_id=mis_id, model=AI_MODEL, model_type=model_type, model_config=MODEL_CONFIG, token=token,
                        pre_tools=pre_tools)
                    elif func_name == "chat":
                        function_calling(func_name, "call", prompt=func_args["prompt"], history=history, current_user_id=user_id,
                        generator=generator, tts_session_id=tts_session_id, user_orders=user_orders,
                        user_feature=user_feature, personal_reply=personal_reply, memory_content=memory_content, mis_id=mis_id, model=AI_MODEL, model_type=model_type, model_config=MODEL_CONFIG, token=token)
                    elif func_name == "Edit":
                        function_calling(func_name, "call", user_input=func_args["user_input"], current_user_id=user_id,
                        generator=generator, mis_id=mis_id, history=history, model_type=model_type, token=token, memory_content=memory_content, user_orders=user_orders)
                    elif func_name == "Share":
                        logger.debug(f"MainAgent调用ShareAgent，token存在状态: {token is not None}")
                        function_calling(func_name, "call", intent=func_args["intent"], history=history, current_user_id=user_id,
                        generator=generator, tts_session_id=tts_session_id, user_orders=user_orders,
                        user_feature=user_feature, personal_reply=personal_reply, memory_content=memory_content, mis_id=mis_id, model=AI_MODEL, model_type=model_type, model_config=MODEL_CONFIG, token=token)
                    elif func_name == "Delete":
                        function_calling(func_name, "call", user_input=func_args["user_input"], current_user_id=user_id,
                        generator=generator, mis_id=mis_id, history=history, model_type=model_type, token=token, memory_content=memory_content, user_orders=user_orders)
                    else:
                        logger.error(f"用户{user_id} 获取用户意图时出错: 未找到函数调用, Want to find {func_name}, with arguments: {func_args}")
                        generator.send("小美累了！不想回复啦！")
                        generator.close()
            
            return None
        except Exception as e:
            logger.error(f"系统， 用户{user_id} 获取用户意图时出错: {str(e)}")
            return None

# 弃用
class IntentAgent(BaseTool):
    function_name = "Intent"
    function_description = "用于从用户聊天的信息中，获取用户的意图，并对意图进行总结或扩写，以决定后续的使用流程"
    parameters = [{
        "name": "messages",
        "description": "大模型与用户的聊天记录",
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "role": {
                    "type": "string",
                    "description": "消息的角色，如'user'或'assistant'或'system'",
                    "enum": ["user", "assistant", "system"]
                },
                "content": {
                    "type": "string",
                    "description": "消息的内容"
                }
            },
            "required": ["role", "content"]
        },
        "required":True
    }]
    
    @staticmethod
    def call(token: str, history: list[dict['content':str]], user_id: int, tts_session_id: None,
              generator: ThreadedGenerator, trace_id: str, user_feature: str = "", personal_reply: str = "",
              memory_content: str = "", mis_id: str = "")->list:
        """获取用户意图

        Args:
            messages: 消息列表
            user_id: 用户ID
            mis_id: 美团内部ID

        Returns:
            list: 意图列表
        """
        def extract_intent_from_str(intent:str)->list:
            try:
                text_dict = json.loads(intent)
                text_list = []
                text_list.extend(list(text_dict.keys()))
                for values in text_dict.values():
                    if isinstance(values, list):
                        text_list.extend(values)
                
                intent = text_list[:lion_config.INTENT_NUM]
                logger.info(f"模型提取的用户意图解析结果: {intent}")
                return intent
            except Exception as e:
                logger.info(f"模型提取的用户意图解析结果为空， 错误信息为: {str(e)}")
                return []

        # mini 微型对话函数，如此可以看出流式返回有多么臃肿
        def get_intent_from_ai(formatted_messages):
            query_input = {
                "model": lion_config.INTENT_MODEL,
                "messages": formatted_messages,
                "tools": [OrderAdivisorAgent.json_scheme, ChatAgent.json_scheme],
                "stream": False,
                "temperature": 0.0,
                "max_tokens": 4000,
            }
            response = send_to_ai(query_input)
            json_data = json.loads(response.text)
            logger.info(f"模型直接返回的的结果: {json_data}")
            return json_data["choices"][0]["message"]

        try:
            # 获取用户画像
            # es_user_portrait = ""
            # if mis_id:
            #     try:
            #         portrait_result = search_user_portrait(mis_id, str(user_id))
            #         if portrait_result:
            #             es_user_portrait = portrait_result.get("portrait", "")
            #             logger.info(f"成功获取用户画像用于意图解析，用户ID: {user_id}, MIS ID: {mis_id}")
            #     except Exception as e:
            #         logger.error(f"获取用户画像时发生错误: {str(e)}")
            if mis_id:
                try:    
                    es_user_portrait = get_portrait_by_id(mis_id)
                except Exception as e:
                    logger.error(f"获取用户画像时发生错误: {str(e)},当前agent为:Chat")
                    es_user_portrait = ""
            formatted_messages = [{
                'role': 'system',
                'content': lion_config.INTENT_PROMPT
            }]
            limit = lion_config.AI_ORDER_LIMIT
            user_orders = order.get_user_orders_v2(token, user_id, limit)
            # 如果没有消息，直接返回空意图
            if not history:
                ai_ret = get_intent_from_ai(formatted_messages)
            else:   
                # 获取最近的对话历史
                context_content = "以下是最近的对话历史（仅供参考）：\n\n"
                
                # 保留最后10条消息或更少
                recent_messages = history[-10:]
                
                # 构建对话历史
                last_user_msg = None
                last_assistant_msg = None
                dialog_pairs = []
                
                for msg in recent_messages:
                    if msg['role'] == 'user':
                        if last_user_msg and last_assistant_msg:
                            dialog_pairs.append((last_user_msg, last_assistant_msg))
                        last_user_msg = msg['content']
                        last_assistant_msg = None
                    elif msg['role'] == 'assistant':
                        if last_user_msg:
                            last_assistant_msg = msg['content']
                
                # 如果最后一组对话不完整，但有用户消息，也加入对话对
                if last_user_msg and last_assistant_msg:
                    dialog_pairs.append((last_user_msg, last_assistant_msg))
                    
                # 只保留最近的5对对话
                dialog_pairs = dialog_pairs[-5:]
                
                # 构建对话历史文本
                for user_msg, assistant_msg in dialog_pairs:
                    context_content += f"用户：{user_msg}\n助手：{assistant_msg}\n\n"
                    
                # 添加当前用户的最新消息（如果最后一条是用户消息）
                if recent_messages and recent_messages[-1]['role'] == 'user':
                    context_content += f"当前用户问题：{recent_messages[-1]['content']}"
                
                formatted_messages.append({
                    'role': 'user',
                    'content': context_content
                })
                
                logger.info(f"发送给意图模型的消息：{formatted_messages}")        
                ai_ret = get_intent_from_ai(formatted_messages)

            if ai_ret.get("tool_calls", None) != None:
                tool_call:list[dict] = ai_ret.get("tool_calls")
                tool_result = []
                for tool in tool_call:
                    func_name = tool["function"]["name"]
                    func_args = json.loads(tool["function"]["arguments"])
                    func_result = function_calling(function_name=func_name, method_name="call", **func_args)
                    tool_result.append({"function": func_name, "arguments": func_args, "result": func_result})
                return tool_result
            else:
                pass
        except Exception as e:
            logger.error(f"系统， 用户{user_id} 获取用户意图时出错: {str(e)}")
            return None

class LocationAgent(BaseTool):
    function_name = "location"
    function_description = "用于返回当前的位置信息"
    parameters = [{
        "name": "location",
        "description": "生成随便的内容即可",
        "type": "string",
        "required": True
    }]

    @staticmethod  # 添加缺失的 staticmethod 装饰器
    def call(location):
        """调用函数的入口"""
        return f"你调用了location函数，location是{location}"

class OrderAdivisorAgent(BaseTool):
    function_name = "Order"
    function_description = "用于接收多个用户意图词汇，并根据这些词汇为用户推荐订单。"
    parameters = [{
        "name": "intent",
        "description": "一个list，用于对用户内容的关键词进行提取和扩写，基于这些词进行RAG查询。",
        "type": "array",
        "items": {
            "type": "string"
        },
        "required": True
    }]

    @staticmethod  # 添加缺失的 staticmethod 装饰器
    def call(intent: list, history: list[dict['content':str]], current_user_id: int,   
            generator: ThreadedGenerator, tts_session_id: None, user_orders=None,
            user_feature: str = "", personal_reply: str = "", memory_content: str= "", \
            mis_id: str = "", model:str="gpt-4o-mini", model_type:str="gpt-4o-mini", model_config:dict={}, token:str|None=None,
            location_query: bool = False, order_count_query: bool = False, pre_tools:list=[]) -> None:

        logger.info(f"用户的意图信息为: {intent}")
        """处理更多数据请求

        Args:
            memory_content: 记忆内容
            intent: 用户意图
            history: 聊天历史记录
            current_user_id: 当前用户ID
            generator: 生成器实例
            user_orders: 用户订单数据
            tts_session_id: tts会话id
            user_feature: 用户画像
            personal_reply: 回复规则
            mis_id: 美团内部ID
        """
        try:
            # 初始化ES用户画像
            # es_user_portrait = ""

            # # 处理用户画像
            # if mis_id:
            #     try:
            #         # 使用传入的mis_id和current_user_id
            #         portrait_result = search_user_portrait(mis_id, str(current_user_id))
            #         if not portrait_result:
            #             # 如果没有找到画像，生成并上传
            #             logger.info(f"未找到用户画像，开始生成新画像，用户ID: {current_user_id}, MIS ID: {mis_id}")
            #             portrait = get_user_portrait(mis_id, user_orders, memory_content)  # 使用mis_id获取用户画像
            #             if portrait:
            #                 # 上传到ES
            #                 upload_result = upload_to_es(mis_id, str(current_user_id), portrait)
            #                 if upload_result:
            #                     logger.info(f"成功生成并上传用户画像，用户ID: {current_user_id}, MIS ID: {mis_id}")
            #                     es_user_portrait = portrait
            #         else:
            #             logger.info(f"成功找到现有用户画像，用户ID: {current_user_id}, MIS ID: {mis_id}")
            #             es_user_portrait = portrait_result.get("portrait", "")
            #     except Exception as e:
                    # logger.error(f"处理用户画像时发生错误: {str(e)}")

            if mis_id:
                try:    
                    es_user_portrait = get_portrait_by_id(mis_id)
                except Exception as e:
                    logger.error(f"获取用户画像时发生错误: {str(e)},当前agent为:Chat")
                    es_user_portrait = ""

            user_last_input = parse_user_response(history)
            logger.info(f"type Intent : {type(intent)}, :{intent}")
            
            # 告知用户正在开始RAG过程
            # generator.send("正在开始分析您的需求并查找相关信息...\n")
            
            # RAG
            from service.COT.agents import RAGAgent
            data_agent = RAGAgent(model=model, pre_tools=pre_tools)
            analysis_result, pre_tools = data_agent.call(query=user_last_input, intent=intent, user_orders=user_orders, user_id=current_user_id, generator=generator)
            # analysis_result = rag.search(intent if isinstance(intent, list) else [intent], current_user_id, user_orders=user_orders) # 召回结果
            # insight_result = get_insight_keywords_sql(intent, topk=model_config.get("weiwei.insight_topk")) # 洞察结果
            # 告知用户RAG过程结束
            # generator.send("信息收集完毕，正在组织回复...\n")

            user_last_input = history[-1]["content"]
            prompt_content = model_config.get('weiwei.chat_prompt_template').format(
                user_input=user_last_input,
                time_info=context.get_time_info(),
                weather_info=context.get_weather_prompt(),
                analysis=analysis_result,
                thought_chains=model_config.get("weiwei.consumer_thought_chains_main"),
                insight_result="无",
                memory_content=memory_content
            )

            logger.info(f"RAG召回构建的系统提示词为: {prompt_content}")

            # 构建系统提示词，包含用户画像和ES中的用户画像
            system_prompt = model_config.get("weiwei.system_prompt")
            if user_feature:
                system_prompt += f"\n用户画像：{user_feature}"
            if es_user_portrait:
                system_prompt += f"\nES用户画像：{es_user_portrait}"
            if personal_reply:
                system_prompt += f"\n回复规则：{personal_reply}"
            # 添加用户历史订单数据
            if user_orders:
                system_prompt += f"\n用户历史订单：{json.dumps(user_orders, ensure_ascii=False)}"
            # 添加记忆内容
            if memory_content:
                system_prompt += f"\n记忆内容：{memory_content}"


            history_tmp = [{
                "role": "system",
                "content": system_prompt
            }] + history[:-1] + [
                            {
                                "role": "user",
                                "content": prompt_content
                            }
                        ]
            
            # 添加分隔符，以区分RAG过程和最终回复
            #generator.send("\n---\n")
            
            send(history_tmp, tts_session_id, current_user_id, generator, model=model, model_type=model_type, token=token, pre_tools=pre_tools)
            
            # 在函数结束前上传记忆
            try:
                # 生成记忆内容
                memory_facts = begin_memory_service(mis_id=mis_id,history=history,user_orders=user_orders,token=token,model_type=model_type,success=True,function_name="Order")
            except Exception as e:
                logger.error(f"上传记忆时出错: {str(e)}")

            try:
                # 更新用户画像
                # from service.user_portrait import update_portrait_by_mis_id
                # mt_user_id = str(current_user_id)  # 确保是字符串类型
                # if memory_facts:
                #     success = update_portrait_by_mis_id(mis_id, mt_user_id, limit=2, memory_content=memory_facts[-1])
                #     if success:
                #         logger.info(f"ChatAgent: 成功更新用户画像，用户ID: {mt_user_id}")
                #     else:
                #         logger.error(f"ChatAgent: 更新用户画像失败，用户ID: {mt_user_id}")
                # else:
                #     logger.info(f"ChatAgent: 没有生成记忆内容，不更新用户画像")
                begin_portrait_service(mis_id=mis_id,history=history,function_name="Order")
            except Exception as e:
                logger.error(f"更新用户画像时出错: {str(e)}")
        except Exception as e:
            logger.error(f"用户{current_user_id} 处理推荐对话时出错: {str(e)}")
            generator.send("小美，不知道要说什么啦，换个问题好不好嘛。")
            generator.close()

class ShareAgent(BaseTool):
    function_name = "Share"
    function_description = "用于将聊天内容分享到社区广场，供其他用户参考。"
    parameters = [{
        "name": "intent",
        "description": "一个list，包含用户意图关键词",
        "type": "array",
        "items": {
            "type": "string"
        },
        "required": True
    }]

    @staticmethod
    def call(intent: list, history: list[dict['content':str]], current_user_id: int,   
            generator: ThreadedGenerator, tts_session_id: None, user_orders=None,
            user_feature: str = "", personal_reply: str = "", memory_content: str= "", \
            mis_id: str = "", model:str="gpt-4o-mini", model_type:str="gpt-4o-mini", model_config:dict={}, token:str|None=None) -> dict:
        """将用户对话分享到社区广场

        Args:
            intent: 用户意图关键词
            history: 聊天历史记录
            current_user_id: 当前用户ID
            generator: 生成器实例
            tts_session_id: tts会话id
            user_orders: 用户订单数据
            user_feature: 用户画像
            personal_reply: 回复规则
            memory_content: 记忆内容
            mis_id: 美团内部ID
            
        Returns:
            dict: 包含所有ES字段的字典
        """
        try:
                
            from datetime import datetime
            from service.request_aigc import send
            import time

            # 获取分享标题和内容
            title, content, shop_name, food_names, order_detail, money_detail, poi_id, food_ids, shop_url = generate_share_content(history, user_orders, intent, memory_content)
            # 获取食物图片
            food_img_urls = []
            for food_id in food_ids:
                food_img_url = generate_food_img_url(token, poi_id, food_id)
                food_img_urls.append(food_img_url)
            # 格式化当前时间
            formatted_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 生成唯一标识符
            share_identifier = f"{title}_{formatted_time}"
            
            # 准备要返回的ES字段字典
            es_fields = {
                "time": formatted_time,
                "title": title,
                "content": content,
                "mis_id_keyword": mis_id,
                "content": content,
                "share_identifier": share_identifier,
                "shop_name": shop_name,
                "order_detail": order_detail,
                "money_detail": money_detail,
                "food_img_urls": food_img_urls,
                "shop_url": shop_url
            }

            logger.info(f"准备上传到社区广场的ES字段: {es_fields}")
            
            # 上传到社区广场
            success, share_id = upload_to_plaza(
                formatted_time=formatted_time,
                title=title,
                content=content,
                mis_id=mis_id,
                user_content=content,
                share_identifier=share_identifier,
                shop_name=shop_name,
                order_detail=order_detail,
                money_detail=money_detail,
                food_img_urls=food_img_urls,
                shop_url=shop_url
            )
            
            # 构建提示消息
            if success:
                es_fields["id"] = share_id

                # 更新用户评论图谱
                update_user_comment_graph(mis_id, share_id, is_delete=False)
                # 更新店铺评论图谱
                
                # 构建响应消息
                if CURRENT_ENV == "test":
                    response = f"内容已成功分享到社区广场！\n[分享链接](https://xiaomeiai.cloud.test.sankuai.com/xiaomeivv/post/{share_id})\n分享标题：{title}\n分享内容：{content}"
                else:
                    response = f"内容已成功分享到社区广场！\n[分享链接](https://xiaomeiai.meituan.com/xiaomeivv/post/{share_id})\n分享标题：{title}\n分享内容：{content}" 
                    
                logger.info(f"已经给用户{mis_id}的评论图谱添加了分享内容{share_id}")
                
                # 如果有订单信息，添加到响应中
                if shop_name:
                    update_shop_comment_graph(shop_name, poi_id, share_id, is_delete=False)
                    response += f"\n分享的餐厅：{shop_name}"
                    logger.info(f"已经给店铺{poi_id}的评论图谱添加了分享内容{share_id}")

                    if order_detail:
                        response += f"\n订单内容：{', '.join(order_detail)}"

                response += "\n 以上是我给你写的分享内容，如果你不满意，请你直接告诉我你要修改这条分享，并告诉我具体怎么修改标题或者内容哦！"
            else:
                response = "抱歉，分享失败，请稍后再试。"
            
            # 构建系统提示词和用户提示词
            system_prompt = lion_config.lion_client.get_value_with_default("weiwei.output_prompt", DEFAULT_OUTPUT_PROMPT)
            
            # 构建消息历史
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": response}
            ]
            
            # 使用send函数让AI生成回复
            model = lion_config.lion_client.get_value_with_default("weiwei.output_model", "gpt-4o-mini")
            logger.info(f"ShareAgent: 使用模型: {model}")
            logger.info(f"发送信息: {messages}")
            send(messages, tts_session_id, current_user_id, generator, model=model, model_type=model, token=token)
            
            # 在函数结束前上传记忆
            try:
                # 生成记忆内容

                memory_facts = begin_memory_service(mis_id=mis_id,history=history,user_orders=user_orders,token=token,model_type=model_type,success=True,function_name="Share",share_id=share_id,title=title,content=content,shop_name=shop_name)
            except Exception as e:
                logger.error(f"上传记忆时出错: {str(e)}")
            
            try:
                # 更新用户画像
            #     from service.user_portrait import update_portrait_by_mis_id
            #     mt_user_id = str(current_user_id)  # 确保是字符串类型
            #     if memory_facts:
            #         success = update_portrait_by_mis_id(mis_id, mt_user_id, limit=2, memory_content=memory_facts[-1])
            #         if success:
            #             logger.info(f"ShareAgent: 成功更新用户画像，用户ID: {mt_user_id}")
            #         else:
            #             logger.error(f"ShareAgent: 更新用户画像失败，用户ID: {mt_user_id}")
            #     else:
            #         logger.info(f"ShareAgent: 没有生成记忆内容，不更新用户画像")
                begin_portrait_service(mis_id=mis_id,history=history,function_name="Share")
            except Exception as e:
                logger.error(f"更新用户画像时出错: {str(e)}")
                

            # 返回包含所有ES字段的字典
            return es_fields
            
        except Exception as e:
            logger.error(f"分享到社区广场时出错: {str(e)}")
            error_msg = f"分享内容时出现错误: {str(e)}"
            
            # 构建系统提示词和错误消息
            system_prompt = lion_config.lion_client.get_value_with_default("weiwei.output_prompt", DEFAULT_OUTPUT_PROMPT)
            
            # 构建消息历史
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": error_msg}
            ]
            
            # 使用send函数让AI生成错误回复
            model = lion_config.lion_client.get_value_with_default("weiwei.output_model", "gpt-4o-mini")
            send(messages, tts_session_id, current_user_id, generator, model=model, model_type=model, token=token)
            
            # 发生异常时返回空字典
            return {}

class EditAgent(BaseTool):
    function_name = "Edit"
    function_description = "用于修改已分享的内容，可以修改标题和内容。"
    parameters = [{
        "name": "user_input",
        "description": "用户输入的内容，可能包含要修改的分享ID或关键词",
        "type": "string",
        "required": True
    }]

    @staticmethod
    def call(user_input: str, current_user_id: int, generator: ThreadedGenerator, mis_id: str = "", 
             history: list[dict['content':str]] = [], model_type:str="gpt-4o-mini", token:str=None, memory_content: str= "", user_orders=None) -> dict:
        """修改已分享的内容

        Args:
            user_input: 用户输入
            current_user_id: 当前用户ID
            generator: 生成器实例
            mis_id: 美团内部ID
            history: 聊天历史记录
            model_type: 模型类型
            token: 认证令牌
            memory_content: 记忆内容
            user_orders: 用户历史订单
            
        Returns:
            dict: 包含所有处理的ES字段的字典
        """
        try:
            
            # 使用LLM分析历史记录和用户输入
            analysis_result = analyze_edit_request(user_input, history, memory_content)
            logger.info(f"LLM分析结果: {analysis_result}")
            
            # 提取分析结果
            edit_id = analysis_result.get('edit_id', '')
            edit_title = analysis_result.get('edit_title', '')
            edit_content = analysis_result.get('edit_content', '')
            search_keyword = analysis_result.get('search_keyword', '')
            
            # 初始化响应和返回字段
            response = ""
            es_fields = {
                "action": "unknown",
                "edit_id": edit_id,
                "edit_title": edit_title,
                "edit_content": edit_content,
                "search_keyword": search_keyword
            }
            
            # 1. 如果edit_id不为空，执行修改操作
            if edit_id:
                # 获取分享内容
                share = get_plaza_share_by_id(edit_id, index=PLAZA_INDEX)
                
                if share and share.get('mis_id_keyword') == mis_id:
                    # 准备更新数据
                    update_data = {}
                    
                    # 仅当有值时才更新相应字段
                    if edit_title:
                        update_data["title"] = edit_title
                    
                    if edit_content:
                        update_data["content"] = edit_content
                    
                    # 更新es_fields为原始share的内容，并根据edit_title和edit_content更新相应字段
                    es_fields = share.copy()
                    if edit_title:
                        es_fields["title"] = edit_title
                    if edit_content:
                        es_fields["content"] = edit_content
                    

                    # 如果没有任何要更新的内容，返回当前内容让用户确认要修改的内容
                    if not update_data:
                        current_title = share.get('title', '')
                        current_content = share.get('content', '')
                        
                        response = f"请提供要修改的新标题和内容，格式为：\n\n新标题\n\n新内容\n\n当前标题: {current_title}\n当前内容: {current_content}"
                    else:
                        # 执行更新
                        success = update_plaza_share_content(edit_id, update_data, mis_id)
                        
                        if success:
                            if CURRENT_ENV == "test":
                                response = f"已成功更新分享内容！[分享链接](https://xiaomeiai.cloud.test.sankuai.com/xiaomeivv/post/{edit_id})\n分享标题更改：{edit_title}\n分享内容更改：{edit_content}"
                            else:
                                response = f"已成功更新分享内容！[分享链接](https://xiaomeiai.meituan.com/xiaomeivv/post/{edit_id})\n分享标题更改：{edit_title}\n分享内容更改：{edit_content}"
                        else:
                            response = "抱歉，更新失败，请稍后再试。"
                            es_fields = {}  # 更新失败时返回空字典
                else:
                    if not share:
                        response = f"未找到ID为 {edit_id} 的分享内容，请检查ID是否正确。"
                    else:
                        response = "您没有权限修改这条分享内容。"
                    es_fields = {}  # 查询失败或无权限时返回空字典
            
            # 2. 如果edit_id为空但search_keyword不为空，执行搜索操作
            elif search_keyword:
                # 导入时间范围模块
                from service.time_range_str import get_time_range
                
                # 获取7天前到现在的时间范围
                time_range = get_time_range(7)  # 最近7天
                
                # 搜索包含关键词的分享，添加时间过滤
                search_results = search_plaza_contains_exact_phrase(search_keyword, time_range=time_range)
                
                if search_results:
                    # 过滤出属于当前用户的分享
                    user_shares = [share for share in search_results if share.get('mis_id_keyword') == mis_id]
                    
                    if user_shares:
                        response = "找到以下与\"{}\"相关的您近7天内分享的内容，请选择要修改的内容ID或输入\"搜索结果中的第几个\":".format(search_keyword)
                        response += "\n\n"
                        
                        search_results_preview = []
                        for i, share in enumerate(user_shares, 1):
                            share_id = share.get('id')
                            title = share.get('title')
                            share_time = share.get('time', '')
                            content_preview = share.get('content', '')[:50] + "..." if len(share.get('content', '')) > 50 else share.get('content', '')
                            
                            search_results_preview.append({
                                "index": i,
                                "id": share_id,
                                "title": title,
                                "time": share_time,
                                "content_preview": content_preview
                            })
                            
                            response += f"第{i}个: ID: {share_id}\n标题: {title}\n时间: {share_time}\n预览: {content_preview}\n\n"
                        
                        response += "请回复要修改的内容ID或\"搜索结果中的第几个\"，然后在下一条消息中提供新的标题和内容，格式为：\n新标题\n\n新内容"
                        es_fields = {"search_results": search_results_preview}
                    else:
                        response = "未找到您近7天内分享的包含关键词\"{}\"的内容。请尝试使用其他关键词搜索，或者搜索更早的内容。".format(search_keyword)
                        es_fields = {}
                else:
                    response = "未找到近7天内包含关键词\"{}\"的分享内容。请尝试使用其他关键词搜索，或者搜索更早的内容。".format(search_keyword)
                    es_fields = {}
            
            # 3. 如果edit_id和search_keyword都为空，提示用户提供搜索关键词
            else:
                response = "无法确定您想要修改的分享内容。请提供一个关键词或者分享id，以便我们帮您搜索并确定相关的分享内容。"
                es_fields = {}
            
            # 构建系统提示词和用户提示词
            system_prompt = lion_config.lion_client.get_value_with_default("weiwei.output_prompt", DEFAULT_OUTPUT_PROMPT)

            # 构建消息历史
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": response}
            ]
            
            # 使用send函数让AI生成回复
            model = lion_config.lion_client.get_value_with_default("weiwei.output_model", "gpt-4o-mini")
            send(messages, None, current_user_id, generator, model=model, model_type=model, token=token)
            
            # 在函数结束前上传记忆
            try:
                # 生成记忆内容
                memory_facts = begin_memory_service(mis_id=mis_id,history=history,user_orders=user_orders,token=token,model_type=model_type,success=True,function_name="Edit",edit_id=edit_id,edit_title=edit_title,edit_content=edit_content)

            except Exception as e:
                logger.error(f"上传记忆时出错: {str(e)}")

            try:
                # 更新用户画像
                # from service.user_portrait import update_portrait_by_mis_id
                # mt_user_id = str(current_user_id)  # 确保是字符串类型
                # if memory_facts:
                #     success = update_portrait_by_mis_id(mis_id, mt_user_id, limit=2, memory_content=memory_facts[-1])
                #     if success:
                #         logger.info(f"EditAgent: 成功更新用户画像，用户ID: {mt_user_id}")
                #     else:
                #         logger.error(f"EditAgent: 更新用户画像失败，用户ID: {mt_user_id}")
                # else:
                #     logger.info(f"EditAgent: 没有生成记忆内容，不更新用户画像")
                begin_portrait_service(mis_id=mis_id,history=history,function_name="Edit")
            except Exception as e:
                logger.error(f"更新用户画像时出错: {str(e)}")
            
            # 返回ES字段
            return es_fields
            
        except Exception as e:
            logger.error(f"修改分享内容时出错: {str(e)}")
            error_msg = f"修改内容时出现错误: {str(e)}"
            
            # 构建系统提示词和错误消息
            system_prompt = lion_config.lion_client.get_value_with_default("weiwei.output_prompt", DEFAULT_OUTPUT_PROMPT)
            
            # 构建消息历史
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": error_msg}
            ]
            
            # 使用send函数让AI生成错误回复
            model = lion_config.lion_client.get_value_with_default("weiwei.output_model", "gpt-4o-mini")
            send(messages, None, current_user_id, generator, model=model, model_type=model, token=token)
            
            # 返回包含错误信息的字典
            return {
                "action": "error",
                "error": str(e)
            }

class DeleteAgent(BaseTool):
    function_name = "Delete"
    function_description = "用于删除已分享的内容。"
    parameters = [{
        "name": "user_input",
        "description": "用户输入的内容，可能包含要删除的分享ID或关键词",
        "type": "string",
        "required": True
    }]

    @staticmethod
    def call(user_input: str, current_user_id: int, generator: ThreadedGenerator, mis_id: str = "", 
             history: list[dict['content':str]] = [], model_type:str="gpt-4o-mini", token:str=None, memory_content: str= "", user_orders=None) -> dict:
        """删除已分享的内容

        Args:
            user_input: 用户输入
            current_user_id: 当前用户ID
            generator: 生成器实例
            mis_id: 美团内部ID
            history: 聊天历史记录
            model_type: 模型类型
            token: 认证令牌
            memory_content: 记忆内容
            user_orders: 用户历史订单
            
        Returns:
            dict: 包含所有处理的ES字段的字典
        """
        try:
            
            # 使用LLM分析历史记录和用户输入
            analysis_result = analyze_delete_request(user_input, history, memory_content)
            logger.info(f"LLM分析结果: {analysis_result}")
            
            # 提取分析结果
            delete_id = analysis_result.get('delete_id', '')
            search_keyword = analysis_result.get('search_keyword', '')
            
            # 初始化响应和返回字段
            response = ""
            es_fields = {
                "action": "unknown",
                "delete_id": delete_id,
                "search_keyword": search_keyword
            }
            
            # 1. 如果delete_id不为空，执行删除操作
            if delete_id:
                # 获取分享内容
                share = get_plaza_share_by_id(delete_id, index=PLAZA_INDEX)
                
                if share and share.get('mis_id_keyword') == mis_id:
                    # 执行删除操作
                    success = delete_plaza_share(delete_id, mis_id)
                    
                    if success:
                        title = share.get('title', '')
                        content = share.get('content', '')
                        shop_name = share.get('shop_name', '')
                        poi_id = share.get('poi_id', '')
                        # 保存被删除内容的信息，用于后续可能的记忆
                        es_fields = share.copy()
                        es_fields["action"] = "deleted"
                        # 删除用户评论图谱
                        update_user_comment_graph(mis_id, delete_id, is_delete=True)
                        response = f"已成功删除分享内容！\n标题：{title}\n内容：{content}"
                        logger.info(f"已经给用户{mis_id}的评论图谱删除了分享内容{delete_id}")
                        if shop_name:
                            # 删除店铺评论图谱
                            update_shop_comment_graph(shop_name, poi_id, delete_id, is_delete=True)
                            response += f"\n涉及的餐厅：{shop_name}"
                            logger.info(f"已经给店铺{poi_id}的评论图谱删除了分享内容{delete_id}")
                    else:
                        response = "抱歉，删除失败，请稍后再试。"
                        es_fields = {}  # 删除失败时返回空字典
                else:
                    if not share:
                        response = f"未找到ID为 {delete_id} 的分享内容，请检查ID是否正确。"
                    else:
                        response = "您没有权限删除这条分享内容。"
                    es_fields = {}  # 查询失败或无权限时返回空字典
            
            # 2. 如果delete_id为空但search_keyword不为空，执行搜索操作
            elif search_keyword:
                # 导入时间范围模块
                from service.time_range_str import get_time_range
                
                # 获取7天前到现在的时间范围
                time_range = get_time_range(7)  # 最近7天
                
                # 搜索包含关键词的分享，添加时间过滤
                search_results = search_plaza_contains_exact_phrase(search_keyword, time_range=time_range)
                
                if search_results:
                    # 过滤出属于当前用户的分享
                    user_shares = [share for share in search_results if share.get('mis_id_keyword') == mis_id]
                    
                    if user_shares:
                        response = "找到以下与\"{}\"相关的您近7天内分享的内容，请选择要删除的内容ID或输入\"搜索结果中的第几个\":".format(search_keyword)
                        response += "\n\n"
                        
                        search_results_preview = []
                        for i, share in enumerate(user_shares, 1):
                            share_id = share.get('id')
                            title = share.get('title')
                            share_time = share.get('time', '')
                            content_preview = share.get('content', '')[:50] + "..." if len(share.get('content', '')) > 50 else share.get('content', '')
                            
                            search_results_preview.append({
                                "index": i,
                                "id": share_id,
                                "title": title,
                                "time": share_time,
                                "content_preview": content_preview
                            })
                            
                            response += f"第{i}个: ID: {share_id}\n标题: {title}\n时间: {share_time}\n预览: {content_preview}\n\n"
                        
                        response += "请回复要删除的内容ID或\"搜索结果中的第几个\"。删除操作不可恢复，请确认后再操作。"
                        es_fields = {"search_results": search_results_preview}
                    else:
                        response = "未找到您近7天内分享的包含关键词\"{}\"的内容。请尝试使用其他关键词搜索，或者搜索更早的内容。".format(search_keyword)
                        es_fields = {}
                else:
                    response = "未找到近7天内包含关键词\"{}\"的分享内容。请尝试使用其他关键词搜索，或者搜索更早的内容。".format(search_keyword)
                    es_fields = {}
            
            # 3. 如果delete_id和search_keyword都为空，提示用户提供搜索关键词
            else:
                response = "无法确定您想要删除的分享内容。请提供一个关键词或者分享ID，以便我们帮您搜索并确定要删除的分享内容。"
                es_fields = {}
            
            # 构建系统提示词和用户提示词
            system_prompt = lion_config.lion_client.get_value_with_default("weiwei.output_prompt", DEFAULT_OUTPUT_PROMPT)

            # 构建消息历史
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": response}
            ]
            
            # 使用send函数让AI生成回复
            model = lion_config.lion_client.get_value_with_default("weiwei.output_model", "gpt-4o-mini")
            send(messages, None, current_user_id, generator, model=model, model_type=model, token=token)
            
            # 在函数结束前上传记忆
            try:
                # 生成记忆内容
                memory_facts = begin_memory_service(mis_id=mis_id,history=history,user_orders=user_orders,token=token,model_type=model_type,success=True,function_name="Delete",delete_id=delete_id)
                    

            except Exception as e:
                logger.error(f"上传记忆时出错: {str(e)}")

            try:
                # 更新用户画像
                # from service.user_portrait import update_portrait_by_mis_id
                # mt_user_id = str(current_user_id)  # 确保是字符串类型
                # if memory_facts:
                #     success = update_portrait_by_mis_id(mis_id, mt_user_id, limit=2, memory_content=memory_facts[-1])
                #     if success:
                #         logger.info(f"DeleteAgent: 成功更新用户画像，用户ID: {mt_user_id}")
                #     else:
                #         logger.error(f"DeleteAgent: 更新用户画像失败，用户ID: {mt_user_id}")
                # else:
                #     logger.info(f"DeleteAgent: 没有生成记忆内容，不更新用户画像")
                begin_portrait_service(mis_id=mis_id,history=history,function_name="Delete")
            except Exception as e:
                logger.error(f"更新用户画像时出错: {str(e)}")
            
            # 返回ES字段
            return es_fields
            
        except Exception as e:
            logger.error(f"删除分享内容时出错: {str(e)}")
            error_msg = f"删除内容时出现错误: {str(e)}"
            
            # 构建系统提示词和错误消息
            system_prompt = lion_config.lion_client.get_value_with_default("weiwei.output_prompt", DEFAULT_OUTPUT_PROMPT)
            
            # 构建消息历史
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": error_msg}
            ]
            
            # 使用send函数让AI生成错误回复
            model = lion_config.lion_client.get_value_with_default("weiwei.output_model", "gpt-4o-mini")
            send(messages, None, current_user_id, generator, model=model, model_type=model, token=token)
            
            # 返回包含错误信息的字典
            return {
                "action": "error",
                "error": str(e)
            }

class BaseKnowledgeTool(BaseTool):
    function_name = "BaseKnowledge"
    function_description = "用于从美团的外卖商家数据库中，搜索相关的内容作为基础信息，该信息作为外卖推荐的基础存在。"
    parameters = [{
        "name": "keywords",
        "description": "一个list类型，包含需要查询的关键词，工具将根据关键词来从数据库中查询，得到相关的外卖信息。",
        "type": "array",
        "items": {
            "type": "string"
        },
        "required": True
    }]

    @staticmethod
    def call(keywords:List[str], collate_fn=None)->List[Dict[str, Any]]:
        """
        根据给定的关键词，从美团的外卖商家数据库中，搜索相关的内容作为基础信息，该信息作为外卖推荐的基础存在。

        Args:
            keywords: 关键词列表
        """
        version = lion_config.lion_client.get_value_with_default("weiwei.base_knowledge_version", "v1")
        if version == "v1":
            es_result = ingredient.match_ingredient_list(keywords, limit=lion_config.ES_MATCH_NUM) # 满足一个即可，总共召回N条
        elif version == "v2":
            es_result = ingredient.match_ingredient_listV2(keywords, limit=lion_config.ES_MATCH_NUM) # 满足一个即可，总共召回N条
        else:
            logger.info(f"不支持的版本: {version}")

        if collate_fn is not None:
            es_result = collate_fn(es_result)

        return es_result

def BaseKnowledge(keywords:List[str], collate_fn=None)->List[Dict[str, Any]]:
    """
    根据给定的关键词，从美团的外卖商家数据库中，搜索相关的内容作为基础信息，该信息作为外卖推荐的基础存在。
    """
    return BaseKnowledgeTool.call(keywords, collate_fn)

class VexDBTool(BaseTool):
    function_name = "VexDB"
    function_description = "用于根据输入的关键词信息，利用语意向量计算相似度的方式，匹配相近的订单信息。"
    parameters = [{
        "name": "intent",
        "description": "一个字符串类型，包含需要查询的关键词，工具将根据关键词来从向量数据库中查询，得到相关的外卖信息。",
        "type": "string",
        "required": True
    }]

    @staticmethod
    def call(intent:str|list[str], num:int=lion_config.VEX_SEARCH_NUM)->List[Dict[str, Any]]:
        """
        根据给定的关键词，从美团的外卖商家数据库中，搜索相关的内容作为基础信息，该信息作为外卖推荐的基础存在。

        Args:
            query: 查询语句
        """
        query = intent
        try:
            results:list[dict] = vex.search(query, num)
            visual_result = ""
            for result in results:
                visual_result += f"{result['value']} similarity: {result['score']}\n"
            merchants_dict, ignore_info = fill_shop_dict(results, False) # 按照商店的名称处理召回信息
            logger.info(f"vex召回解析结果: {merchants_dict}")
            logger.info(f"Vex匹配的洞察信息: {ignore_info}")
        except Exception as e:
            logger.info(f"处理vex召回时出错: {str(e)}")
            merchants_dict = {}
            ignore_info = []
        return merchants_dict, ignore_info

def VexDB(query:str, num:int=lion_config.VEX_SEARCH_NUM)->List[Dict[str, Any]]:
    """
    用于根据输入的关键词信息，利用语意向量计算相似度的方式，匹配相近的订单信息。
    """
    return VexDBTool.call(query, num)

class SQLTool(BaseTool):
    function_name = "SQL"
    function_description = "用于从美团的SQL数据库中，根据相关的食品关键词，召回该食物的专家信息。"
    parameters = [{
        "name": "keywords",
        "description": "一个list类型，包含需要查询的关键词，工具将根据关键词来从数据库中查询，得到相关的外卖信息。",
        "type": "array",
        "items": {
            "type": "string"
        },
        "required": True
    }]

    @staticmethod
    def call(keywords:List[str], topk:int=lion_config.INSIGHT_TOPK)->List[Dict[str, Any]]:
        """
        根据给定的关键词，从美团的外卖商家数据库中，搜索相关的内容作为基础信息，该信息作为外卖推荐的基础存在。
        """
        insight_result = get_insight_keywords_sql(keywords, topk=topk)
        return insight_result

def SQL(keywords:List[str], topk:int=lion_config.INSIGHT_TOPK)->List[Dict[str, Any]]:
    """
    用于从美团的SQL数据库中，根据相关的食品关键词，召回该食物的专家信息。
    """
    return SQLTool.call(keywords, topk)

if __name__ == '__main__':
    # 为了兼容测试，创建一个模块级的 json_scheme 变量
    # json_scheme = DemoAgent.json_scheme if hasattr(DemoAgent, "json_scheme") else {}
    # print(json_scheme)
    # agent_ids = Registry.list_registered()
    # for id in agent_ids:
    #     agent_class = Registry.get_class(id)
    #     if agent_class is not None:
    #         print(agent_class)
    # query = generete_function_calling_quert("test")
    # print(query)
    # print(IntentAgent.json_scheme)
    # print(function_calling("Intent", "call", messages=[{"role":"user", "content":"helloerere"}]))

    # 只查询
    BaseKnowledge(["小龙虾", "鸡尾酒"])

    # 使用collate_fn进行处理
    def collate_fn(es_result):
        ret = []
        for data in es_result:
            ret.append({
                "name": data["name"],
                "price": data["price"],
                "description": data["description"]
            })
        return ret

    BaseKnowledge(["小龙虾", "鸡尾酒"], collate_fn=collate_fn)

    VexDB("世界上最帅的男人是谁？是孙海岳吗？")

    SQL(["小龙虾", "鸡尾酒"], topk=10)