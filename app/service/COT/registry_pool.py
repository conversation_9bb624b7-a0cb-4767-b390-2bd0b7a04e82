from utils.logger import logger
# 全局对象池
class Registry:
    """全局对象注册表，用于管理和访问各种功能类"""
    _classes = {}  # 存储注册的类
    _instances = {}  # 存储类的实例（懒加载）
    
    @classmethod
    def register(cls, name=None):
        """
        注册类的装饰器
        
        Args:
            name: 注册名称，默认使用类名
            
        Returns:
            装饰器函数
        """
        def decorator(registered_class):
            # 使用提供的名称或类名作为注册名
            key = name or registered_class.__name__.lower()
            # 将类添加到注册表
            cls._classes[key] = registered_class
            return registered_class
        return decorator
    
    @classmethod
    def get_class(cls, name):
        """
        获取已注册的类
        
        Args:
            name: 类的注册名
            
        Returns:
            已注册的类，如果不存在则返回None
        """
        return cls._classes.get(name, None)
    
    @classmethod
    def get_instance(cls, name, *args, **kwargs):
        """
        获取或创建类的实例（单例模式）
        
        Args:
            name: 类的注册名
            *args, **kwargs: 传递给类构造函数的参数
            
        Returns:
            类的实例，如果类不存在则返回None
        """
        if name not in cls._instances:
            registered_class = cls.get_class(name)
            if registered_class:
                cls._instances[name] = registered_class(*args, **kwargs)
        return cls._instances.get(name)
    
    @classmethod
    def list_registered(cls):
        """
        列出所有已注册的类名
        
        Returns:
            已注册类名的列表
        """
        return list(cls._classes.keys())

    @classmethod
    def list_class(cls):
        return list(cls._classes.values())
    
    @classmethod
    def call_method(cls, class_name, method_name, *args, **kwargs):
        """
        调用已注册类的方法
        
        Args:
            class_name: 类的注册名
            method_name: 方法名
            *args, **kwargs: 传递给方法的参数
            
        Returns:
            方法的返回值，如果类或方法不存在则返回None
        """
        instance = cls.get_instance(class_name)
        if instance and hasattr(instance, method_name):
            
            method = getattr(instance, method_name)
            logger.info(f"Method : {method}")
            return method(*args, **kwargs)
        return None

# 全局对象池
class LocalRegistry:
    """Agent对象注册表，用于管理和访问各种功能类"""
    def __init__(self):
        self._classes = {}  # 存储注册的类
        self._instances = {}  # 存储类的实例（懒加载）
    
    def register(self, name=None):
        """
        注册类的装饰器
        
        Args:
            name: 注册名称，默认使用类名
            
        Returns:
            装饰器函数
        """
        def decorator(registered_class):
            # 使用提供的名称或类名作为注册名
            key = name or registered_class.__name__.lower()
            # 将类添加到注册表
            self._classes[key] = registered_class
            return registered_class
        return decorator
    
    def get_class(self, name):
        """
        获取已注册的类
        
        Args:
            name: 类的注册名
            
        Returns:
            已注册的类，如果不存在则返回None
        """
        return self._classes.get(name, None)
    
    def get_instance(self, name, *args, **kwargs):
        """
        获取或创建类的实例（单例模式）
        
        Args:
            name: 类的注册名
            *args, **kwargs: 传递给类构造函数的参数
            
        Returns:
            类的实例，如果类不存在则返回None
        """
        if name not in self._instances:
            registered_class = self.get_class(name)
            if registered_class:
                self._instances[name] = registered_class(*args, **kwargs)
        return self._instances.get(name)
    
    def list_registered(self):
        """
        列出所有已注册的类名
        
        Returns:
            已注册类名的列表
        """
        return list(self._classes.keys())

    def list_class(self):
        return list(self._classes.values())
    
    def call_method(self, class_name, method_name, *args, **kwargs):
        """
        调用已注册类的方法
        
        Args:
            class_name: 类的注册名
            method_name: 方法名
            *args, **kwargs: 传递给方法的参数
            
        Returns:
            方法的返回值，如果类或方法不存在则返回None
        """
        instance = self.get_instance(class_name)
        if instance and hasattr(instance, method_name):
            
            method = getattr(instance, method_name)
            logger.info(f"Method : {method}")
            return method(*args, **kwargs)
        return None


class AgentRegistry:
    """全局对象注册表，用于管理和访问各种功能类"""
    _classes = {}  # 存储注册的类
    _instances = {}  # 存储类的实例（懒加载）
    
    @classmethod
    def register(cls, name=None):
        """
        注册类的装饰器
        
        Args:
            name: 注册名称，默认使用类名
            
        Returns:
            装饰器函数
        """
        def decorator(registered_class):
            # 使用提供的名称或类名作为注册名
            key = name or registered_class.__name__.lower()
            # 将类添加到注册表
            cls._classes[key] = registered_class
            return registered_class
        return decorator
    
    @classmethod
    def get_class(cls, name):
        """
        获取已注册的类
        
        Args:
            name: 类的注册名
            
        Returns:
            已注册的类，如果不存在则返回None
        """
        return cls._classes.get(name, None)
    
    @classmethod
    def get_instance(cls, name, *args, **kwargs):
        """
        获取或创建类的实例（单例模式）
        
        Args:
            name: 类的注册名
            *args, **kwargs: 传递给类构造函数的参数
            
        Returns:
            类的实例，如果类不存在则返回None
        """
        if name not in cls._instances:
            registered_class = cls.get_class(name)
            if registered_class:
                cls._instances[name] = registered_class(*args, **kwargs)
        return cls._instances.get(name)
    
    @classmethod
    def list_registered(cls):
        """
        列出所有已注册的类名
        
        Returns:
            已注册类名的列表
        """
        return list(cls._classes.keys())

    @classmethod
    def list_class(cls):
        return list(cls._classes.values())
    
    @classmethod
    def call_method(cls, class_name, method_name, *args, **kwargs):
        """
        调用已注册类的方法
        
        Args:
            class_name: 类的注册名
            method_name: 方法名
            *args, **kwargs: 传递给方法的参数
            
        Returns:
            方法的返回值，如果类或方法不存在则返回None
        """
        instance = cls.get_instance(class_name)
        if instance and hasattr(instance, method_name):
            
            method = getattr(instance, method_name)
            logger.info(f"Method : {method}")
            return method(*args, **kwargs)
        return None 
    