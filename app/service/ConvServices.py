from utils.logger import logger
from es import es_client
import re
from web import response
from configs import lion_config
from configs.local_config import DEFAULT_RATE_AI_RESPONSE_PROMPT
import random
from uuid import uuid4
import requests
import json
import time
from my_mysql.entity import ai_batch_response, batch_job_stop_status, prompt_templates
from service.ai_client import send_to_ai
from configs.config import CURRENT_ENV
from datetime import datetime, timed<PERSON><PERSON>


def upload_conv_his(conversation_id, new_message, api_name, user_id=None):
    index_name = "compare_history"

    query = {
        "query": {
            "term": {
                "_id": conversation_id
            }
        }
    }
        
    logger.info(f"查询对话ID: {conversation_id}")
    search_result = es_client.search(index_name, query)
    history = search_result['hits']['hits']
    logger.info(f"查询对话ID: {conversation_id}，查询结果: {history}")
    if history:
        history = history[0]['_source']
    else:
        history = {}

    if history.get(api_name, None):
        if not isinstance(history[api_name], list):
            history[api_name] = [history[api_name], new_message]
        else:   
            history[api_name].append(new_message)
    else:
        history[api_name] = [new_message]

    history["user_id"] = user_id #强制确保user_id不变

    doc = {
        "doc": history,
        "doc_as_upsert": True
    }
            

    es_response = es_client.upsert_data(index_name, conversation_id, doc)
    if es_response:
        logger.info(f"上传对话历史到ES成功，对话ID: {conversation_id}，当前使用接口api: {api_name}，当前对话历史文档: {history}")
        return True
    else:
        logger.error(f"上传对话历史到ES失败，对话ID: {conversation_id}")
        return False



def get_conv_by_id(conversation_id):
    query = {
        "query": {
            "term": {
                "_id": conversation_id
            }
        }
    }
    logger.info(f"查询对话ID对应的全部历史记录: {conversation_id}")
    index_name = "compare_history"
    search_result = es_client.search(index_name, query)
    history = search_result['hits']['hits']
    if history:
        history = history[0]['_source']
    else:
        history = {}
        return history
    
    api_names = ['agentic', 'dianping', 'rawchat', 'centralagent']
    content_types = ['reasoning_content', 'main_content', 'poi_content']
    for api_name in api_names:
        if history.get(api_name, None):
            for msg in history[api_name]:
                for content_type in content_types:
                    if content_type in msg.keys():
                        msg[f"{content_type}_length"] = len(msg[content_type])
 
    return history


def get_conv_ids_by_user(user_id):
    index_name = "compare_history"
    # term 查询 user_id 字段
    query = {
        "query": {
            "term": {
                "user_id": user_id
            }
        },
        "_source": False,
        "size": 10000  # 可根据实际情况调整
    }
    
    search_result = es_client.search(index_name, query)
    hits = search_result.get('hits', {}).get('hits', [])

    # 正则表达式匹配合规的conversation_id
    pattern = re.compile(rf'^[a-zA-Z0-9_.\-@+]{{1,64}}_\d{{4}}-\d{{2}}-\d{{2}}-\d{{2}}-\d{{2}}-\d{{2}}$')
    conversation_ids = [hit['_id'] for hit in hits if '_id' in hit and pattern.match(hit['_id'])]
    return conversation_ids

def get_conv_name_by_id(conversation_id):
    index_name = "compare_history"
    query = {
        "query": {
            "term": {
                "_id": conversation_id
            }
        }
    }
    search_result = es_client.search(index_name, query)
    history = search_result['hits']['hits']
    if history:
        history = history[0]['_source']
        agentic = history.get("agentic", [])
        if agentic:
            agentic_name = agentic[0].get("user_message", "新对话")
            if len(agentic_name) > 10:
                agentic_name = agentic_name[:10] + "..."
            
            return agentic_name 
        dianping = history.get("dianping", [])
        if dianping:
            dianping_name = dianping[0].get("user_message", "新对话")
            if len(dianping_name) > 10:
                dianping_name = dianping_name[:10] + "..."
            return dianping_name  
        
        rawchat = history.get("rawchat", [])
        if rawchat:
            rawchat_name = rawchat[0].get("user_message", "新对话")
            if len(rawchat_name) > 10:
                rawchat_name = rawchat_name[:10] + "..."
            return rawchat_name
        centralagent = history.get("centralagent", [])
        if centralagent:
            centralagent_name = centralagent[0].get("user_message", "新对话")
            if len(centralagent_name) > 10:
                centralagent_name = centralagent_name[:10] + "..."
            return centralagent_name
    return "新对话"

def like_or_dislike_a_msg(conversation_id, message_id, api_name, like_or_dislike):
    index_name = "compare_history"
    # 1. 查找文档
    query = {
        "query": {
            "term": {
                "_id": conversation_id
            }
        }
    }
    search_result = es_client.search(index_name, query)
    hits = search_result.get('hits', {}).get('hits', [])
    if not hits:
        return response.fail("未找到对应的对话文档")
    doc = hits[0]['_source']

    # 2. 查找api_name字段
    if api_name not in doc:
        return response.fail(f"文档中不存在api_name字段: {api_name}")
    messages = doc[api_name]
    if not isinstance(messages, list):
        return response.fail(f"api_name字段内容不是列表")

    # 3. 查找message_id并修改likes
    found = False
    for msg in messages:
        if msg.get("message_id") == message_id:
            msg["likes"] = like_or_dislike
            found = True
            break
    if not found:
        return response.fail("未找到对应的message_id")

    # 4. 更新该api_name字段
    update_body = {
        "doc": {
            api_name: messages
        }
    }
    update_result = es_client.update_doc(index_name, conversation_id, update_body)

    if not update_result:
        return response.fail("ES更新失败")
    return response.success({"message": "点赞/点踩成功"})

def upload_used_ids(user_id):
    try:
        mis_id_index = lion_config.lion_client.get_value_with_default("mis_id_index", "id_used_weiwei_compare")
        query = {
            "query": {
                "term": {
                    "_id": 1
                }
            }
        }
        search_result = es_client.search(mis_id_index, query)
        if search_result:
            history = search_result['hits']['hits']
            if history:
                history = history[0]['_source']
                if user_id not in history["mis_id"]:
                    history["mis_id"].append(user_id)
                    id_response = es_client.upsert_data(mis_id_index, 1, {"doc": history, "doc_as_upsert": True})
                    if id_response:
                        logger.info(f"用户ID: {user_id} 添加到使用者名单成功")
                    else:
                        logger.error(f"用户ID: {user_id} 添加到使用者名单失败")
                else:
                    logger.info(f"用户ID: {user_id} 已存在")
            else:
                history = {"mis_id": [user_id]}
                id_response = es_client.upsert_data(mis_id_index, 1, {"doc": history, "doc_as_upsert": True})
                if id_response:
                    logger.info(f"用户ID: {user_id} 添加到使用者名单成功")
                else:
                    logger.error(f"用户ID: {user_id} 添加到使用者名单失败")

        return
    except Exception as e:
        logger.error(f"上传mis_id到ES失败，用户ID: {user_id}")
        return
    
def delete_conv(conversation_id):
    """
    删除一个对话
    
    Args:
        conversation_id (str): 对话ID
        
    Returns:
        bool: 操作成功返回True，失败返回False
    """
    try:
        if not conversation_id:
            logger.error("删除对话缺少必要参数")
            return False
        
        index_name = "compare_history"
        delete_result = es_client.delete_doc(index_name, conversation_id)
        
        if not delete_result:
            logger.error(f"删除对话失败: {conversation_id}")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"删除对话错误: {e}")
        return False 
    
def handle_request_json(input_request_json:dict, batch_job_id:str, prompt:str, task_index:int):
    """
    处理请求的json，根据api_name修改其格式和属性使之合规, 返回一个list，list中每个元素是一个dict，dict中包含api_name的输入参数，顺序为agentic，centralagent，dianping

    输入：
    input_request_json: 请求的json
    batch_job_id: 批次ID
    prompt: 提示词
    task_index: 任务索引

    输出：
    一个list，list中每个元素是一个dict，dict中包含api_name的输入参数，顺序为agentic，centralagent，dianping
    """
    try:
        return [
            {
                "searchSource":int(lion_config.lion_client.get_value_with_default("agentic_search_source", 0)),
                "searchMode":1,
                "query":prompt,
                "userInfo":input_request_json['agentic_user_info'],
                "cityId":input_request_json['agentic_city_id'],
                "lat":input_request_json['lat'],
                "lng":input_request_json['lng'],
                "sessionId":batch_job_id,
                "messageList": None,
                "deviceInfo": None,
                "globalId":1,
                # "conversation_id":f"{batch_job_id}_agentic_{task_index}",
                # "mis_id":input_request_json['mis_id'],
            }, #Input for agentic
            {
                "message":{
                    "role":"user",
                    "content":[
                        {
                        "type":"text",
                        "text":prompt
                        }
                    ]
                },
                "user_info":input_request_json['central_user_info'],
                "active_service_info":input_request_json['active_service_info'],
                "mis_id":input_request_json['mis_id'],
                "conversation_id":f"{batch_job_id}_centralagent_{task_index}",
                "retry":True,
                "business_data":{},
                "debug_info":{}
            }, #Input for centralagent
            {
                "keyword":prompt,
                "cityId":input_request_json['dianping_city_id'],
                "locateCityId":input_request_json['locate_city_id'],
                "lat":input_request_json['lat'],
                "lng":input_request_json['lng'],
                "userId":input_request_json['dianping_user_id'],
                "dpid":input_request_json['dpid'],
                "session_id":batch_job_id
            } #Input for dianping   
        ]
    except Exception as e:
        logger.error(f"处理请求的json错误: {e}")
        return False

def rate_ai_response(user_query, ai_response)->tuple[str, int]:
    """
        评分AI回答
        
        Args:
            user_query: 用户输入
            ai_response: AI回答
            
        Returns:
            ai_comment: 评分AI对回答的评价
            ai_score: 评分AI对回答的评分,0-100
    """
    try:
        model = lion_config.lion_client.get_value_with_default("rate_ai_response_model", "gpt-4.1")
        system_prompt = lion_config.lion_client.get_value_with_default("rate_ai_response_prompt", DEFAULT_RATE_AI_RESPONSE_PROMPT)
        query = {
            "model": model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"用户输入: {user_query}\nAI回答: {ai_response}"}
            ]
        }   
        response = send_to_ai(query)
        logger.info(f"评分AI回答响应: {response.text}")
        response_text = json.loads(response.text)["choices"][0]["message"]["content"]
        response_json = json.loads(response_text)
        ai_comment = response_json["ai_comment"]
        ai_score = response_json["ai_score"]
        return ai_comment, ai_score
    except Exception as e:
        logger.error(f"评分AI回答错误: {e}")
        return False


def post_agentic_batch(request_json, batch_job_id=None, sub_task_id=None):
    """
    发送请求给agentic并由ai评分
    
    Args:
        request_json: 请求的json
        batch_job_id: 批次ID
    
    输出：
        聊天是否正确上传到sql
    """
    try:
        if not batch_job_id:
            logger.error("缺少batch_job_id")
            return False
        if not sub_task_id:
            logger.error("缺少sub_task_id")
            return False
        external_api_url = "https://xmww.meituan.com/deepsearch/agenticStreamSearch"
        headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream"  # 明确接受SSE格式
        }
        last_message = request_json['query']
        if not last_message:
            logger.error("传入空消息！")
            return False
        
        logger.info(f"开始使用prompt:{request_json['query']}请求agentic")

        think_content = []
        main_content = []
        poi_content = []
        
        with requests.post(
            external_api_url,
            json=request_json,
            headers=headers,
            stream=True  # 启用流式传输
        ) as r:
            
            if r.status_code != 200:
                logger.error(f"外部API调用失败: {r.status_code}")
                return False
            for line in r.iter_lines():

                if line:
                    decoded_line = line.decode('utf-8')
                    cut_line = decoded_line.replace("data:", "")
                    # logger.info(f"agentic流式传输内容: {cut_line}")
                    decoded_json = json.loads(cut_line)

                    if decoded_json["contentList"][0]["data"]["type"] in ["thinking_title", "thinking"]:
                        think_content.append(decoded_json["contentList"][0]["data"].get("content", ""))
                    elif decoded_json["contentList"][0]["data"]["type"] == "main":
                        main_content.append(decoded_json["contentList"][0]["data"].get("content", ""))
                    elif decoded_json["contentList"][0]["data"]["type"] in ["poi_description", "poi_info"]:
                        poi_content.append(decoded_json["contentList"][0]["data"].get("content", ""))
                
            final_think_content = "".join(think_content).strip()    
            final_main_content = "".join(main_content).strip()
            final_poi_content = "".join(poi_content).strip()

            final_answer_content = f"思考：{final_think_content}\n主要内容：{final_main_content}\n店铺信息：{final_poi_content}"

            ai_comment, ai_score = rate_ai_response(request_json["query"], final_answer_content)
            logger.info(f"最终回答：{final_answer_content}，AI评论: {ai_comment}, 评分: {ai_score}")

            api_name = "agentic"
            user_query = last_message

            sql_result = ai_batch_response.insert_ai_batch_response(
                BatchJobID=batch_job_id,
                SubTaskID=sub_task_id,
                api_name=api_name,
                query=user_query,
                ai_response=final_answer_content,
                ai_rating=ai_comment,
                ai_score=ai_score
            )
            logger.info(f"写入ai_batch_response表结果: {sql_result}")
            if sql_result:
                return True  # 返回插入结果
            else:
                return False
    except Exception as e:
        logger.error(f"post_agentic错误: {e}")
        return False

def post_centralagent_batch(request_json, batch_job_id=None, sub_task_id=None):
    """
    发送请求给centralagent并由ai评分
    
    Args:
        request_json: 请求的json
        batch_job_id: 批次ID
        sub_task_id: 子任务ID

    输出：
        聊天是否正确上传到sql
    """
    try:

        if not batch_job_id:
            return False
        if not sub_task_id:
            return False
        external_api_url = "http://i-beam.sankuai.com/ca/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream"  # 明确接受SSE格式
        }
        answer_content = []
        last_message = request_json.get("message",{}).get("content",[])
        if last_message:
            last_message = last_message[-1].get("text","")
        else:
            logger.error("传入空消息！")
            return False
        
        message_id = str(uuid4())
        request_json["message_id"] = message_id
        request_json["child_message_id"] = message_id+str(random.randint(100000, 999999))
        

        logger.info(f"开始使用prompt:{request_json['message']['content'][-1]['text']}请求centralagent")

        with requests.post(
            external_api_url,
            json=request_json,
            headers=headers,
            stream=True  # 启用流式传输
        ) as r:
            logger.info(f"central agent开始请求，状态码: {r.status_code}")
            # 检查状态码
            if r.status_code != 200:
                logger.error(f"外部API调用失败: {r.status_code}")
                return False
            
            # 逐行传输响应
            for line in r.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    cut_line = decoded_line.replace("data:", "")
                 
                    if cut_line == "[DONE]":
                        break

                    cut_json = json.loads(cut_line)
                    if cut_json.get("choices",[{}]) == None:
                        continue
                    if cut_json.get("choices",[{}])[0].get("delta",{}).get("type","") == "ANSWER":
                        answer_content.append(cut_json.get("choices",[])[0].get("delta",{}).get("content",""))    
                    

            final_answer_content = "".join(answer_content).strip()
            ai_comment, ai_score = rate_ai_response(last_message, final_answer_content)
            logger.info(f"最终回答：{final_answer_content}，AI评论: {ai_comment}, 评分: {ai_score}")

            # 新增：写入SQL
            api_name = "centralagent" 
            user_query = last_message
            # ai_score 已有

            if batch_job_id and sub_task_id:
                sql_result = ai_batch_response.insert_ai_batch_response(
                    BatchJobID=batch_job_id,
                    SubTaskID=sub_task_id,
                    api_name=api_name,
                    query=user_query,
                    ai_response=final_answer_content,
                    ai_rating=ai_comment,
                    ai_score=ai_score
                )
                logger.info(f"写入ai_batch_response表结果: {sql_result}")
                if sql_result:
                    return True  # 返回插入结果
                else:
                    return False
            else:
                logger.error(f"batch_job_id 或 sub_task_id 缺失，无法写入ai_batch_response表")
                return False

    except Exception as e:
        logger.error(f"post_centralagent错误: {e}")
        return False


def post_dianping_batch(request_json, batch_job_id=None, sub_task_id=None):
    """
    发送请求给dianping并由ai评分
    
    Args:
        request_json: 请求的json
        batch_job_id: 批次ID
        sub_task_id: 子任务ID

    输出：
        聊天是否正确上传到sql
    """
    try:
        if not batch_job_id:
            return False
        if not sub_task_id:
            return False
        external_api_url = "https://dpllm.sankuai.com/api/sse/aisearch/evaluate"
        headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream"  # 明确接受SSE格式
        }
        params = {
            'keyword': request_json.get('keyword'),
            'cityId': request_json.get('cityId'),
            'locateCityId': request_json.get('locateCityId'),
            'userId': request_json.get('userId'),
            'dpid': request_json.get('dpid'),
            'lat': request_json.get('lat'),
            'lng': request_json.get('lng'),
            'sessionId': request_json.get('session_id')
        }
        last_message = request_json['keyword']
        if not last_message:
            logger.error("传入空消息！")
            return False
        
        think_content = []
        main_content = []
        thinking_mode = False
        main_mode = False

        logger.info(f"开始使用prompt:{request_json['keyword']}请求dianping，get params: {params}")

        with requests.get(
            external_api_url,
            params=params,
            headers=headers,
            stream=True  # 启用流式传输
        ) as r:
            if r.status_code != 200:
                logger.error(f"外部API调用失败: {r.status_code}")
                return False
            for line in r.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    decoded_line = decoded_line.replace("data:", "")
                    # logger.info(f"dianping流式传输内容: {decoded_line}")
                    if decoded_line == "深度思考：":
                        thinking_mode = True
                        main_mode = False
                        continue
                    elif decoded_line == "FollowUp:":
                        main_mode = False
                        continue
                    if thinking_mode:
                        if decoded_line.startswith('<span style="color:grey">') and decoded_line.endswith('</span>'):
                            # 去掉前后标签
                            content = decoded_line.replace('<span style="color:grey">', '').replace('</span>', '')
                            think_content.append(content)
                        else:
                            # 不再是深度思考内容，切换到main_mode
                            thinking_mode = False
                            main_mode = True
                            if decoded_line:  # 避免空行
                                main_content.append(decoded_line)
                    elif main_mode and decoded_line:
                        main_content.append(decoded_line)

            final_think_content = "".join(think_content).strip()    
            final_main_content = "".join(main_content).strip()

            final_answer_content = f"思考：{final_think_content}\n主要内容：{final_main_content}"   
            ai_comment, ai_score = rate_ai_response(last_message, final_answer_content)
            logger.info(f"最终回答：{final_answer_content}，AI评论: {ai_comment}, 评分: {ai_score}")

            api_name = "dianping"
            user_query = request_json["keyword"]
            
            if batch_job_id and sub_task_id:
                sql_result = ai_batch_response.insert_ai_batch_response(
                    BatchJobID=batch_job_id,
                    SubTaskID=sub_task_id,
                    api_name=api_name,
                    query=user_query,
                    ai_response=final_answer_content,
                    ai_rating=ai_comment,
                    ai_score=ai_score
                )
                logger.info(f"写入ai_batch_response表结果: {sql_result}")
                if sql_result:
                    return True  # 返回插入结果
                else:
                    return False
            else:
                logger.error(f"batch_job_id 或 sub_task_id 缺失，无法写入ai_batch_response表")
                return False    

    except Exception as e:
        logger.error(f"post_dianping错误: {e}")
        return False
    
def get_recent_batch_job_ids(limit=1000, mis_id=""):
    """
    获取最近的批处理任务ID列表，按创建时间从近到远排序
    
    Args:
        limit: 返回的最大记录数，默认1000条
        mis_id: 创建者mis_id，默认""
    Returns:
        批处理任务ID列表
    """
    try:
        batch_job_id_index = lion_config.lion_client.get_value_with_default("batch_job_id_index", "batch_job_status")
        
        # 构建查询，按created_time降序排序
        if not mis_id:
            logger.info(f"未指定mis_id，查询所有批处理任务")
            query = {
                "query": {
                    "match_all": {}  # 匹配所有文档
                },
                "sort": [
                    {"created_time": {"order": "desc"}}  # 按创建时间降序
                ],
                "size": limit  # 限制返回条数
            }
            
            # 执行搜索
            result = es_client.search(batch_job_id_index, query)
            
            if not result or "hits" not in result or "hits" not in result["hits"]:
                logger.warning(f"ES返回为空，未找到任何批处理任务")
                return []
            
            # 提取batch_job_id列表
            hits = result["hits"]["hits"]
            batch_job_ids = [hit["_id"] for hit in hits]
            
            logger.info(f"获取到{len(batch_job_ids)}个最近批处理任务ID")
            return batch_job_ids
        
        else:
            logger.info(f"指定mis_id，查询指定创建者批处理任务")
            query = {
                "query": {
                    "term": {"creator_mis_id": mis_id}
                },
                "sort": [
                    {"doc.created_time": {"order": "desc"}}  # 按创建时间降序
                ],
                "size": limit  # 限制返回条数
            }
            result = es_client.search(batch_job_id_index, query)
            if not result or "hits" not in result or "hits" not in result["hits"]:
                logger.warning(f"ES返回为空，未找到任何批处理任务")
                return []
            hits = result["hits"]["hits"]
            batch_job_ids = [hit["_id"] for hit in hits]
            logger.info(f"获取到{len(batch_job_ids)}个最近批处理任务ID")
            return batch_job_ids

    except Exception as e:
        logger.error(f"获取最近批处理任务ID失败: {e}")
        return []
    
def get_recent_batch_job_info(limit=1000, mis_id=""):
    """
    获取最近批处理任务的详细信息
    
    Args:
        limit: 返回的最大记录数，默认1000条
        mis_id: 创建者mis_id，默认""
    Returns:
        批处理任务ID列表
    """
    try:
        batch_job_id_index = lion_config.lion_client.get_value_with_default("batch_job_id_index", "batch_job_status")
        if not mis_id:
            query = {
                "query": {
                    "match_all": {}  # 匹配所有文档
                },
                "sort": [
                    {"created_time": {"order": "desc"}}  # 按创建时间降序
                ],
                "size": limit  # 限制返回条数
            }
            result = es_client.search(batch_job_id_index, query)
            if not result or "hits" not in result or "hits" not in result["hits"]:
                logger.warning(f"ES返回为空，未找到任何批处理任务")
                return []
            hits = result["hits"]["hits"]

            batch_job_info = [{
                "batch_job_id": hit["_id"],
                "created_time": hit["_source"]["created_time"],
                "creator_mis_id": hit["_source"]["creator_mis_id"],
                "completed_tasks": hit["_source"]["completed_tasks"],
                "total_tasks": hit["_source"]["total_tasks"],
                "status": hit["_source"]["status"],
                "job_name": hit["_source"]["job_name"],
                "repeat_mode": hit["_source"].get("repeat_mode", "no_repeat")
            } for hit in hits]
            
            logger.info(f"获取到{len(batch_job_info)}个最近批处理任务")
            return batch_job_info
        
        else:
            query = {
                "query": {
                    "term": {"creator_mis_id": mis_id}
                },  
                "sort": [
                    {"created_time": {"order": "desc"}}  # 按创建时间降序
                ],
                "size": limit  # 限制返回条数
            }
            result = es_client.search(batch_job_id_index, query)
            if not result or "hits" not in result or "hits" not in result["hits"]:
                logger.warning(f"ES返回为空，未找到任何批处理任务")
                return []
            hits = result["hits"]["hits"]
            batch_job_info = [{
                "batch_job_id": hit["_id"],
                "created_time": hit["_source"]["created_time"],
                "creator_mis_id": hit["_source"]["creator_mis_id"],
                "completed_tasks": hit["_source"]["completed_tasks"],
                "total_tasks": hit["_source"]["total_tasks"],
                "status": hit["_source"]["status"],  
                "job_name":hit["_source"]["job_name"],
                "repeat_mode": hit["_source"].get("repeat_mode", "no_repeat")
            } for hit in hits]  
            logger.info(f"获取到{len(batch_job_info)}个最近批处理任务")
            return batch_job_info

    except Exception as e:
        logger.error(f"获取最近批处理任务ID失败: {e}")
        return []
        
def initialize_batch_job(batch_job_id, prompt_list, start_time=None):
    """
    初始化批处理任务
    """
    try:
        num_of_prompts = len(prompt_list)
        batch_job_id_index = lion_config.lion_client.get_value_with_default("batch_job_id_index", "batch_job_status")
        query = {
            "query": {
                "term": {
                    "_id": batch_job_id
                }
            }
        }   
        result = es_client.search(batch_job_id_index, query)
        if not result or "hits" not in result or "hits" not in result["hits"]:
            logger.warning(f"ES返回为空，未找到任何批处理任务")
            return "未在ES中找到批处理任务记录"
        hits = result["hits"]["hits"]
        if len(hits) == 0:
            logger.warning(f"ES返回为空，未找到任何批处理任务")
            return "未在ES中找到批处理任务记录"
        
        if hits[0]["_source"]["status"] != "未启动":
            logger.warning(f"批处理任务{batch_job_id}已启动，无法初始化")
            return "该任务已启动或完成，无法重新开始"
        batch_job_info = hits[0]["_source"]
        batch_job_info["status"] = "running"
        batch_job_info["total_tasks"] = num_of_prompts
        batch_job_info["detail_status"] = {"agentic":["Waiting"]*num_of_prompts,"centralagent":["Waiting"]*num_of_prompts,"dianping":["Waiting"]*num_of_prompts}
        batch_job_info["prompt_list"] = prompt_list
        if start_time:
            batch_job_info["actual_start_time"] = start_time
        else:
            batch_job_info["actual_start_time"] = "立即开始"
        logger.info(f"初始化批处理任务: {batch_job_info}")
        doc = {
            "doc": batch_job_info,
            "doc_as_upsert": True
        }
        res = es_client.upsert_data(batch_job_id_index, batch_job_id, doc)
        logger.info(f"初始化批处理任务ES结果: {res}")
        if not res:
            return "初始化ES失败"
        return 
        
    except Exception as e:
        logger.error(f"初始化批处理任务失败: {e}")
        return f"初始化时出现错误{e}"
    
def update_batch_job_status(batch_job_id, current_task_index, current_api_name, status, total_tasks):
    """
    更新批处理任务状态
    """
    try:
        batch_job_id_index = lion_config.lion_client.get_value_with_default("batch_job_id_index", "batch_job_status")
        query = {
            "query": {
                "term": {
                    "_id": batch_job_id
                }
            }
        }   
        result = es_client.search(batch_job_id_index, query)
        if not result or "hits" not in result or "hits" not in result["hits"]:
            logger.warning(f"ES返回为空，未找到任何批处理任务")
            return "未在ES中找到批处理任务记录"
        hits = result["hits"]["hits"]
        if len(hits) == 0:
            logger.warning(f"ES返回为空，未找到任何批处理任务") 
            return "未在ES中找到批处理任务记录"
        batch_job_info = hits[0]["_source"]
        if status == True:
            batch_job_info["detail_status"][current_api_name][current_task_index] = "Success"
        else:
            batch_job_info["detail_status"][current_api_name][current_task_index] = "Failed"

        if current_api_name == "dianping":
            batch_job_info["completed_tasks"] += 1

        if batch_job_info["completed_tasks"] == total_tasks:
            batch_job_info["status"] = "completed"
        else:
            batch_job_info["status"] = "running"
        
        doc = {
            "doc": batch_job_info,
            "doc_as_upsert": True
        }
        res = es_client.upsert_data(batch_job_id_index, batch_job_id, doc)
        logger.info(f"更新批处理任务状态ES结果: {res}")
        if not res:
            return "更新ES失败"
        return "更新成功"
    except Exception as e:
        logger.error(f"更新批处理任务状态失败: {e}")
        return f"更新时出现错误{e}" 
    
def get_batch_job_detail(batch_job_id):
    """
    获取批处理任务的详细信息
    """
    try:
        batch_job_id_index = lion_config.lion_client.get_value_with_default("batch_job_id_index", "batch_job_status")
        query = {
            "query": {
                "term": {
                    "_id": batch_job_id
                }
            }
        }
        result = es_client.search(batch_job_id_index, query)
        if not result or "hits" not in result or "hits" not in result["hits"]:
            logger.warning(f"ES返回为空，未找到任何批处理任务")
            return "未在ES中找到批处理任务记录"
        hits = result["hits"]["hits"]
        if len(hits) == 0:
            logger.warning(f"ES返回为空，未找到任何批处理任务")
            return "未在ES中找到批处理任务记录"
        batch_job_info = hits[0]["_source"]
        return batch_job_info
    except Exception as e:
        logger.error(f"获取批处理任务详细信息失败: {e}")
        return f"获取批处理任务详细信息失败{e}"
    
def get_batch_job_result(batch_job_id, sub_task_id, api_name):
    """
    获取批处理任务结果
    """
    
    if not sub_task_id and not api_name:
        logger.info(f"查询batch_job_id为{batch_job_id}的批量请求AI接口结果")
        result = ai_batch_response.query_by_batch_or_subtask(batch_job_id=batch_job_id)
    elif not sub_task_id and api_name:
        logger.info(f"查询batch_job_id为{batch_job_id}，api_name为{api_name}的批量请求AI接口结果")
        result = ai_batch_response.query_by_batch_or_subtask(batch_job_id=batch_job_id, api_name=api_name)
    elif sub_task_id and not api_name:
        logger.info(f"查询batch_job_id为{batch_job_id}，sub_task_id为{sub_task_id}的批量请求AI接口结果")    
        result = ai_batch_response.query_by_batch_or_subtask(batch_job_id=batch_job_id, sub_task_id=sub_task_id)
    else:
        logger.info(f"查询batch_job_id为{batch_job_id}，sub_task_id为{sub_task_id}，api_name为{api_name}的批量请求AI接口结果")
        result = ai_batch_response.query_by_batch_or_subtask(batch_job_id=batch_job_id, sub_task_id=sub_task_id, api_name=api_name)

    # 将元组列表转换为字典列表
    formatted_result = []
    if result:
        for item in result:
            if len(item) >= 7 and len(item) < 9:  # 确保元组有足够的元素
                # 确保文本字段正确处理，特别是可能包含emoji的字段
                formatted_item = {
                    "batch_job_id": item[0],
                    "sub_task_id": item[1],
                    "model_type": item[2],
                    "query": item[3],
                    "response": item[4],
                    "evaluation": item[5],
                    "score": item[6]
                }
                formatted_result.append(formatted_item)
            elif len(item) >= 9:
                formatted_item = {
                    "batch_job_id": item[0],
                    "sub_task_id": item[1],
                    "model_type": item[2],
                    "query": item[3],
                    "response": item[4],
                    "evaluation": item[5],
                    "score": item[6],
                    "user_comment": item[7],
                    "user_score": item[8]
                }
                formatted_result.append(formatted_item) 
            else:
                logger.warning(f"结果项格式不符合预期: {item}")

    logger.info(f"查询结果数量: {len(formatted_result) if formatted_result else 0}")
    if not formatted_result:
        return {}
    
    # 将最终结果作为JSON返回
    return formatted_result

def check_batch_job_need_stop(batch_job_id):
    """
    检查批处理任务是否需要停止
    """
    batch_job_id_index = lion_config.lion_client.get_value_with_default("batch_job_id_index", "batch_job_status")
    if batch_job_stop_status.is_job_need_stop(batch_job_id):
        logger.info(f"批处理任务{batch_job_id}需要停止")
        res = batch_job_stop_status.delete_stop_id(batch_job_id)
        if res:
            query = {
                "query": {
                    "term": {
                        "_id": batch_job_id
                    }
                }
            }
            result = es_client.search(batch_job_id_index, query)
            if not result or "hits" not in result or "hits" not in result["hits"]:
                logger.warning(f"ES返回为空，未找到任何批处理任务")
                return "未在ES中找到批处理任务记录"
            hits = result["hits"]["hits"]
            if len(hits) == 0:
                logger.warning(f"ES返回为空，未找到任何批处理任务")
                return "未在ES中找到批处理任务记录"
            batch_job_info = hits[0]["_source"]
            batch_job_info["status"] = "canceled"   
            batch_job_info["detail_status"]["agentic"] = ['canceled' if status == "Waiting" else status for status in batch_job_info["detail_status"]["agentic"]]
            batch_job_info["detail_status"]["centralagent"] = ['canceled' if status == "Waiting" else status for status in batch_job_info["detail_status"]["centralagent"]]
            batch_job_info["detail_status"]["dianping"] = ['canceled' if status == "Waiting" else status for status in batch_job_info["detail_status"]["dianping"]]
            doc = {
                "doc": batch_job_info,
                "doc_as_upsert": True
            }
            res = es_client.upsert_data(batch_job_id_index, batch_job_id, doc)
            if not res:
                logger.error(f"更新批处理任务状态失败")
                return f"更新批处理任务状态失败"
            return True
        else:
            return False
    else:
        return False

def set_prompt_templates(mis_id, prompt_name, prompt_list):
    """
    设置prompt模板

    Args:
        mis_id: 创建者mis_id
        prompt_name: 模版名称
        prompt_list: 模版列表
    """
    # 检查是否存在同名的prompt模板，如果有，使用update函数更新，否则使用insert上传一个新模版
    res = prompt_templates.query_by_mis_id(mis_id, prompt_name)
    if res:
        # 更新模版
        up_res = prompt_templates.update_prompt_template(mis_id, prompt_name, prompt_list)
        if not up_res:
            return f"更新模版{prompt_name}失败"
        return f"更新模版{prompt_name}成功"
    else:
        # 上传新模版
        up_res = prompt_templates.insert_prompt_template(mis_id, prompt_name, prompt_list)
        if not up_res:
            return f"上传新模版{prompt_name}失败"
        return f"上传新模版{prompt_name}成功"

    

def get_prompt_templates(mis_id, prompt_name):
    """
    获取prompt模板
    """
    res = prompt_templates.query_by_mis_id(mis_id, prompt_name)
    list_res = [{
        "mis_id": item[0],
        "prompt_name": item[1],
        "prompt_list": item[2].split("；；；")
    } for item in res]
    logger.info(f"获取模版{prompt_name}结果: {list_res}")
    if list_res:
        return list_res
    else:
        return f"获取模版{prompt_name}失败"

def delete_prompt_templates(mis_id, prompt_name):
    """
    删除prompt模板
    """
    res = prompt_templates.delete_prompt_template(mis_id, prompt_name)
    if res:
        return f"删除模版{prompt_name}成功"
    else:
        return f"删除模版{prompt_name}失败"


def ask_the_ai_by_batch_separate_thread(generator, prompt_list, batch_job_id, request_json, num_of_prompts, start_time=None, repeat_mode="no_repeat", job_name=""):
    from datetime import datetime, timedelta
    import requests
    import time
    try:
        # 如果设置了开始时间，等待直到达到指定时间
        if start_time:
            scheduled_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            current_time = datetime.now()
            
            if scheduled_time > current_time:
                import time
                wait_seconds = (scheduled_time - current_time).total_seconds()
                if wait_seconds > 0:
                    logger.info(f"批处理任务 {batch_job_id} 计划在 {start_time} 开始执行，当前时间 {current_time.strftime('%Y-%m-%d %H:%M:%S')}，等待 {wait_seconds:.2f} 秒")
                    
                    # 更新任务状态为等待中
                    try:
                        batch_job_id_index = lion_config.lion_client.get_value_with_default("batch_job_id_index", "batch_job_status")
                        query = {"query": {"term": {"_id": batch_job_id}}}
                        result = es_client.search(batch_job_id_index, query)
                        if result and "hits" in result and "hits" in result["hits"] and len(result["hits"]["hits"]) > 0:
                            batch_job_info = result["hits"]["hits"][0]["_source"]
                            logger.info(f"更新任务状态为等待中，batch_job_info: {batch_job_info}")
                            batch_job_info["status"] = "waiting"
                            doc = {"doc": batch_job_info, "doc_as_upsert": True}
                            es_client.upsert_data(batch_job_id_index, batch_job_id, doc)
                    except Exception as e:
                        logger.error(f"更新批处理任务状态失败: {e}")
                    
                    # 每10秒检查一次是否需要停止任务
                    check_interval = 10  # 检查间隔（秒）
                    while wait_seconds > 0:
                        sleep_time = min(wait_seconds, check_interval)
                        time.sleep(sleep_time)
                        wait_seconds -= sleep_time
                        
                        # 检查是否需要停止任务
                        if check_batch_job_need_stop(batch_job_id):
                            logger.info(f"在等待执行期间收到停止信号，停止任务 {batch_job_id}")
                            generator.rawsend(json.dumps({"error": f"任务已取消: {batch_job_id}"}, ensure_ascii=False)+'\n')
                            return
                    
                    logger.info(f"等待结束，开始执行批处理任务 {batch_job_id}")
            
        
        sub_task_count = 0
        logger.info(f"prompt_list: {prompt_list}, I am here to run those prompts")

        for prompt in prompt_list:
            # 处理request_json使得它能够适应三个接口的格式
            logger.info(f"开始处理第{sub_task_count+1}轮批量处理，当前prompt为：{prompt}")
            subtask_request_json = handle_request_json(request_json, batch_job_id, prompt, sub_task_count)

            if not subtask_request_json:
                logger.info(f"第{sub_task_count+1}轮批量处理，当前prompt为：{prompt}，没有请求数据，请求失败")
                continue
            agentic_request_json = subtask_request_json[0]
            central_request_json = subtask_request_json[1]
            dianping_request_json = subtask_request_json[2]
            logger.info(f"agentic请求数据: {agentic_request_json}")
            logger.info(f"central请求数据: {central_request_json}")
            logger.info(f"dianping请求数据: {dianping_request_json}")

            if check_batch_job_need_stop(batch_job_id):
                logger.info(f"即将停止任务{batch_job_id}")
                generator.rawsend(json.dumps({"error": f"即将停止任务{batch_job_id}"})+'\n')
                return  

            agentic_result = post_agentic_batch(agentic_request_json, batch_job_id, f"{batch_job_id}_agentic_{sub_task_count}")
            data = {"result": agentic_result,"sub_task_id": f"{batch_job_id}_agentic_{sub_task_count}", "api_name": "agentic"}
            generator.rawsend(json.dumps(data, ensure_ascii=False)+'\n') 
            agentic_res = update_batch_job_status(batch_job_id, sub_task_count, "agentic", agentic_result, num_of_prompts)

            logger.info(f"更新批处理任务状态: {agentic_res}")   

            if check_batch_job_need_stop(batch_job_id):
                logger.info(f"即将停止任务{batch_job_id}")
                generator.rawsend(json.dumps({"error": f"即将停止任务{batch_job_id}"}, ensure_ascii=False)+'\n')
                return    

            central_result = post_centralagent_batch(central_request_json, batch_job_id, f"{batch_job_id}_centralagent_{sub_task_count}")
            data = {"result": central_result,"sub_task_id": f"{batch_job_id}_centralagent_{sub_task_count}", "api_name": "centralagent"}
            generator.rawsend(json.dumps(data, ensure_ascii=False)+'\n') 
            central_res = update_batch_job_status(batch_job_id, sub_task_count, "centralagent", central_result, num_of_prompts)
            logger.info(f"更新批处理任务状态: {central_res}")

            if check_batch_job_need_stop(batch_job_id):
                logger.info(f"即将停止任务{batch_job_id}")
                generator.rawsend(json.dumps({"error": f"即将停止任务{batch_job_id}"}, ensure_ascii=False)+'\n')
                return  

            dianping_result = post_dianping_batch(dianping_request_json, batch_job_id, f"{batch_job_id}_dianping_{sub_task_count}")
            data = {"result": dianping_result,"sub_task_id": f"{batch_job_id}_dianping_{sub_task_count}", "api_name": "dianping"}
            generator.rawsend(json.dumps(data, ensure_ascii=False)+'\n')  
            dianping_res = update_batch_job_status(batch_job_id, sub_task_count, "dianping", dianping_result, num_of_prompts)
            logger.info(f"更新批处理任务状态: {dianping_res}")

            sub_task_count += 1
            if sub_task_count == num_of_prompts:
                logger.info(f"完成本轮批量处理，当前已经执行{sub_task_count}轮，共{num_of_prompts}轮")
            else:
                logger.info(f"进入下一轮批量处理，当前已经执行{sub_task_count}轮，共{num_of_prompts}轮")

        generator.close()

        try:
            # 每天重复
            logger.info(f"任务{batch_job_id}完成，开始创建{'每天' if repeat_mode == 'every_day' else '每周' if repeat_mode == 'every_week' else repeat_mode}重复任务")

            if repeat_mode == "every_day":
                # 将start_time字符串转换为datetime对象，然后加上1天
                if start_time:
                    base_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                else:
                    base_time = datetime.now()
                next_start_time = base_time + timedelta(days=1)
            elif repeat_mode == "every_week":
                # 将start_time字符串转换为datetime对象，然后加上7天
                if start_time:
                    base_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                else:
                    base_time = datetime.now()
                next_start_time = base_time + timedelta(days=7)
            else:
                return

            next_start_time = next_start_time.strftime("%Y-%m-%d %H:%M:%S")
            create_job_data = {
                "mis_id": request_json.get("mis_id"),
                "job_name": f"{job_name if job_name else '未命名重复任务'}",
                "repeat_mode": repeat_mode
            }
            
            # 创建新的重复任务
            create_job_url = "https://xiaomeiai.cloud.test.sankuai.com/weiwei/compare/create_batch_job_id" if CURRENT_ENV == "test" else "https://xiaomeiai.meituan.com/weiwei/compare/create_batch_job_id"

            headers = {"Content-Type": "application/json"}
            create_response = requests.post(create_job_url, json=create_job_data, headers=headers).json()
            logger.info(f"创建新的{repeat_mode}重复任务响应: {create_response}")
            if create_response.get("status") == 0:
                logger.info(f"创建新的{repeat_mode}重复任务成功，新任务ID: {create_response.get('data').get('batch_job_id')}")
                new_batch_job_id = create_response.get("data").get("batch_job_id")
            else:
                logger.error(f"创建新的{repeat_mode}重复任务失败: {create_response.get('message')}")
                return
            
            time.sleep(3)

            new_task_data = request_json.copy()
            new_task_data["batch_job_id"] = new_batch_job_id
            new_task_data["prompt_list"] = prompt_list
            new_task_data["start_time"] = next_start_time
            new_task_data["repeat_mode"] = repeat_mode  # 保持重复模式传递下去

            
            batch_url = "https://xiaomeiai.cloud.test.sankuai.com/weiwei/compare/ask_the_ai_by_batch" if CURRENT_ENV == "test" else "https://xiaomeiai.meituan.com/weiwei/compare/ask_the_ai_by_batch"
            
            headers = {"Content-Type": "application/json"}
            create_response = requests.post(batch_url, json=new_task_data, headers=headers).json()
            logger.info(f"启动新的{repeat_mode}重复任务响应: {create_response}")
            
            
        except Exception as e:
            logger.error(f"创建新的每日重复任务失败: {e}")
            return


        

    except Exception as e:
        logger.error(f"生成失败: {e}")
        generator.rawsend(json.dumps({"error": f"生成失败: {e}"}, ensure_ascii=False)+'\n')


if __name__ == "__main__":
    print(get_recent_batch_job_info())