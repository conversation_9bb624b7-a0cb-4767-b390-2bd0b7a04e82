from utils.logger import logger
from es import es_client
import re
from web import response
from configs import lion_config
import requests

def ave_res_time(user_id, api_name):
    total_time_cost = 0
    count = 0
    index_name = "compare_history"
    query = {
        "query": {
            "term": {
                "user_id": user_id
            }
        },
        "size": 10000
    }
    search_result = es_client.search(index_name, query)
    hits = search_result.get('hits', {}).get('hits', [])
    if not hits:
        return response.fail("未找到对应的对话文档")
    
    for hit in hits:
        history = hit['_source']
        if api_name in history:
            messages = history[api_name]
            for msg in messages:
                if msg.get("total_time_cost", None):
                    total_time_cost += msg["total_time_cost"]
                    count += 1
    if count == 0:
        return response.fail("未找到对应的对话文档")
    average_time_cost = total_time_cost / count
    return response.success({"average_time_cost": average_time_cost, "count": count})

def admin_ave_res_time(api_name):
    total_time_cost = 0
    total_count = 0
    used_count = 0  
    mis_id_index = lion_config.lion_client.get_value_with_default("mis_id_index", "id_used_weiwei_compare")
    query = {
        "query": {
            "term": {
                "_id": 1
            }
        }
    }
    search_result = es_client.search(mis_id_index, query)
    if not search_result:
        return response.fail("暂无有记录的使用者id")
    else:
        history = search_result['hits']['hits']
        logger.info(f"查询使用者id成功，使用者id数: {len(history[0]['_source']['mis_id'])},使用者id: {history[0]['_source']['mis_id']}")
        if not history:
            return response.fail("查询使用者id失败")
        else:
            history = history[0]['_source']
            mis_id = history["mis_id"]
            for user_id in mis_id:
                if not user_id:
                    continue
                user_response = requests.post("http://localhost:8080/weiwei/compare/average_response_time", json={"user_id": user_id, "api_name": api_name})
                logger.info(f"查询用户ID: {user_id} 的平均响应时间，返回结果: {user_response.json()}")
                if user_response.status_code == 200 and user_response.json()['message'] == "成功":
                    user_average_response_time = user_response.json()["data"]['average_time_cost']
                    user_count = user_response.json()["data"]['count']
                    total_time_cost += user_average_response_time * user_count
                    total_count += user_count
                    used_count += 1
                else:
                    logger.error(f"查询用户ID: {user_id} 的平均响应时间失败")

            if total_count == 0:
                return response.fail("暂无可用的对话")
            else:
                average_response_time = total_time_cost / total_count
                logger.info(f"平均响应时间为: {average_response_time}")
                logger.info(f"总对话数为: {total_count}")
                logger.info(f"使用者数为: {used_count}")
                return response.success({"average_response_time": average_response_time, "total_count": total_count, "used_count": used_count})
            
def ave_res_length(user_id, api_name):
    total_length = 0
    count = 0
    index_name = "compare_history"
    query = {
        "query": {
            "term": {
                "user_id": user_id
            }
        },
        "size": 10000
    }
    search_result = es_client.search(index_name, query)
    hits = search_result.get('hits', {}).get('hits', [])
    if not hits:
        return response.fail("未找到对应的对话文档")
    
    for hit in hits:    
        history = hit['_source']
        if api_name in history:
            messages = history[api_name]
            for msg in messages:
                main_content = msg.get("main_content", "")
                reasoning_content = msg.get("reasoning_content", "")
                poi_content = msg.get("poi_content", "")

                total_length += len(main_content) + len(reasoning_content) + len(poi_content)

                if main_content or reasoning_content or poi_content:
                    count += 1   
                    
    if count == 0:
        return response.fail("未找到对应的对话文档")
    average_length = total_length / count
    return response.success({"average_length": average_length, "count": count})

def admin_ave_res_length(api_name):
    total_length = 0
    total_count = 0
    used_count = 0
    
    
    mis_id_index = lion_config.lion_client.get_value_with_default("mis_id_index", "id_used_weiwei_compare")
    query = {
        "query": {
            "term": {
                "_id": 1
            }
        }
    }
    search_result = es_client.search(mis_id_index, query)
    if not search_result:
        return response.fail("暂无有记录的使用者id")
    else:
        history = search_result['hits']['hits']
        if not history:
            return response.fail("查询使用者id失败")    
        else:
            history = history[0]['_source']
            mis_id = history["mis_id"]
            for user_id in mis_id:
                if not user_id:
                    continue    
                user_response = requests.post("http://localhost:8080/weiwei/compare/average_response_length", json={"user_id": user_id, "api_name": api_name})
                logger.info(f"查询用户ID: {user_id} 的平均响应长度，返回结果: {user_response.json()}")
                if user_response.status_code == 200 and user_response.json()['message'] == "成功":
                    user_average_length = user_response.json()["data"]['average_length']
                    user_count = user_response.json()["data"]['count']
                    total_length += user_average_length * user_count
                    total_count += user_count
                    used_count += 1 
                else:
                    logger.error(f"查询用户ID: {user_id} 的平均响应长度失败")   
                    
            if total_count == 0:
                return response.fail("暂无可用的对话")
            else:
                average_length = total_length / total_count
                logger.info(f"平均响应长度为: {average_length}")
                logger.info(f"总对话数为: {total_count}")
                logger.info(f"使用者数为: {used_count}")
                return response.success({"average_length": average_length, "total_count": total_count, "used_count": used_count})
            
####Unchecked func starts here
            
def total_likes(user_id, api_name):
        total_likes = 0
        total_dislikes = 0
        index_name = "compare_history"
        query = {
            "query": {
                "term": {
                    "user_id": user_id
                }
            },
            "size": 10000
        }       
        search_result = es_client.search(index_name, query)
        hits = search_result.get('hits', {}).get('hits', [])
        if not hits:
            return response.fail("未找到对应的对话文档")
        logger.info(f"查询用户ID: {user_id} 的对话文档，返回结果: {hits}，总长度: {len(hits)}")
        for hit in hits:
            history = hit['_source']
            logger.info(f"查询用户ID: {user_id} 的对话文档，docid:{hit['_id']}，返回结果: {history}")
            if api_name in history:
                messages = history[api_name]
                for msg in messages:
                    likes = msg.get("likes", "")
                    if likes == "like":
                        total_likes += 1
                    elif likes == "dislike":
                        total_dislikes += 1
                    else:
                        continue
        return response.success({"total_likes": total_likes, "total_dislikes": total_dislikes})

def total_msg_sent_by_api(api_name):
    total_convs = 0
    total_messages = 0
    mis_id_index = lion_config.lion_client.get_value_with_default("mis_id_index", "id_used_weiwei_compare")
    index_name = "compare_history"
    query = {
        "query": {
            "term": {
                "_id": 1
            }
        }
    }
    search_result = es_client.search(mis_id_index, query)
    if not search_result:
        return response.fail("暂无有记录的使用者id")
    else:
        history = search_result['hits']['hits']
        if not history:
            return response.fail("查询使用者id失败")    
        else:
            history = history[0]['_source']
            mis_id = history["mis_id"]
            for user_id in mis_id:
                if not user_id:
                    continue    
                query = {
                    "query": {
                        "term": {
                            "user_id": user_id
                        }
                    }
                }
                user_search_result = es_client.search(index_name, query)
                hits = user_search_result.get('hits', {}).get('hits', [])
                if not hits:
                    logger.info(f"查询用户ID: {user_id} 的对话文档失败")
                else:
                    for hit in hits:
                        history = hit['_source']
                        if api_name in history:
                            total_convs += 1
                            total_messages += len(history[api_name])


        return response.success({"total_convs": total_convs, "total_messages": total_messages})
    
def admin_total_likes(api_name):
    total_likes = 0
    total_dislikes = 0
    total_users = 0 
    mis_id_index = lion_config.lion_client.get_value_with_default("mis_id_index", "id_used_weiwei_compare")
    query = {
        "query": {
            "term": {
                "_id": 1
            }
        }
    }
    search_result = es_client.search(mis_id_index, query)
    if not search_result:
        return response.fail("暂无有记录的使用者id")
    else:
        history = search_result['hits']['hits']
        if not history:
            return response.fail("查询使用者id失败")
        else:
            history = history[0]['_source']
            mis_id = history["mis_id"]
            for user_id in mis_id:
                if not user_id:
                    continue    
                user_response = requests.post("http://localhost:8080/weiwei/compare/total_likes_and_dislikes", json={"user_id": user_id, "api_name": api_name})
                logger.info(f"查询用户ID: {user_id} 的点赞和点踩，返回结果: {user_response.json()}")
                if user_response.status_code == 200 and user_response.json()['message'] == "成功":

                    user_total_likes = user_response.json()["data"]["total_likes"]
                    user_total_dislikes = user_response.json()["data"]["total_dislikes"]
                    total_likes += user_total_likes 
                    total_dislikes += user_total_dislikes
                    total_users += 1
                else:
                    logger.error(f"查询用户ID: {user_id} 的点赞和点踩失败")
                    
            
            return response.success({"total_likes": total_likes, "total_dislikes": total_dislikes, "total_users": total_users})
        
def total_msgs_sent(user_id, conversation_id):
    total_messages = 0
    index_name = "compare_history"
    query = {
        "query": {
            "term": {
                "_id": conversation_id
            }
        }
    }
    search_result = es_client.search(index_name, query)
    hits = search_result.get('hits', {}).get('hits', [])
    if not hits:
        return response.fail("未找到对应的对话文档")
    
    history = hits[0]['_source']
        
    if "agentic" in history:
        messages = history["agentic"]
        total_messages = len(messages)
        return response.success({"total_messages": total_messages})
    elif "dianping" in history:
        messages = history["dianping"]
        total_messages = len(messages)
        return response.success({"total_messages": total_messages})
    elif "rawchat" in history:
        messages = history["rawchat"]
        total_messages = len(messages)
        return response.success({"total_messages": total_messages})
    elif "centralagent" in history:
        messages = history["centralagent"]
        total_messages = len(messages)
        return response.success({"total_messages": total_messages}) 
    else:
        return response.success({"total_messages": 0})
    
def ave_res_time_by_date_and_api(date_str, api_name):
    index_name = "compare_history"
    # 构造ES查询
    query = {
        "query": {
            "wildcard": {
                f"{api_name}.message_id": f"*{date_str}*"
            }
        },
        "size": 10000  # 可根据实际数据量调整
    }
    search_result = es_client.search(index_name, query)
    hits = search_result.get('hits', {}).get('hits', [])
    total_time_cost = 0
    count = 0
    for hit in hits:
        history = hit['_source']
        if api_name in history:
            for msg in history[api_name]:
                if "message_id" in msg and date_str in msg["message_id"]:
                    if "total_time_cost" in msg:
                        total_time_cost += msg["total_time_cost"]
                        count += 1
    if count == 0:
        return response.fail("未找到对应的消息")
    average_time_cost = total_time_cost / count
    return response.success({"average_time_cost": average_time_cost, "count": count})

def ave_res_length_by_date_and_api(date_str, api_name):
    index_name = "compare_history"
    # 构造ES查询
    query = {
        "query": {
            "wildcard": {
                f"{api_name}.message_id": f"*{date_str}*"
            }
        },
        "size": 1000  # 可根据实际数据量调整
    }   
    search_result = es_client.search(index_name, query)
    hits = search_result.get('hits', {}).get('hits', [])
    total_length = 0
    count = 0
    for hit in hits:
        history = hit['_source']
        if api_name in history:
            for msg in history[api_name]:
                if "message_id" in msg and date_str in msg["message_id"]:
                    main = msg.get("main_content", "")
                    reasoning = msg.get("reasoning_content", "")
                    point = msg.get("poi_content", "")
                    total_length += len(main) + len(reasoning) + len(point)
                    count += 1  
    if count == 0:
        return response.fail("未找到对应的消息")
    average_length = total_length / count
    return response.success({"average_length": average_length, "count": count})

def total_msgs_sent_by_date_and_api(date_str, api_name):
    index_name = "compare_history"
    # 构造ES查询
    query = {
        "query": {
            "wildcard": {
                f"{api_name}.message_id": f"*{date_str}*"
            }
        },  
        "size": 10000  # 可根据实际数据量调整
    }
    search_result = es_client.search(index_name, query)
    hits = search_result.get('hits', {}).get('hits', [])
    total_convs = 0
    total_messages = 0
    for hit in hits:
        history = hit['_source']
        if api_name in history:
            total_convs += 1
            for msg in history[api_name]:   
                if "message_id" in msg and date_str in msg["message_id"]:
                    total_messages += 1
    return response.success({"total_convs": total_convs, "total_messages": total_messages})

def total_likes_by_date_and_api(date_str, api_name):
    index_name = "compare_history"
    # 构造ES查询
    query = {
        "query": {
            "wildcard": {
                f"{api_name}.message_id": f"*{date_str}*"
            }
        },
        "size": 1000  # 可根据实际数据量调整
    }   
    search_result = es_client.search(index_name, query)
    hits = search_result.get('hits', {}).get('hits', [])
    seen_users = []
    total_likes = 0
    total_dislikes = 0
    total_users = 0
    for hit in hits:
        history = hit['_source']
        if api_name in history:
            for msg in history[api_name]:
                if "message_id" in msg and date_str in msg["message_id"]:
                    likes = msg.get("likes", "")
                    if likes == "like":
                        total_likes += 1        
                        current_user = msg.get("user_id", "")
                        if current_user not in seen_users and current_user:
                            seen_users.append(current_user)
                            total_users += 1
                    elif likes == "dislike":
                        total_dislikes += 1
                        current_user = msg.get("user_id", "")
                        if current_user not in seen_users and current_user:
                            seen_users.append(current_user)
                            total_users += 1    
    return response.success({"total_likes": total_likes, "total_dislikes": total_dislikes, "total_users": total_users})

