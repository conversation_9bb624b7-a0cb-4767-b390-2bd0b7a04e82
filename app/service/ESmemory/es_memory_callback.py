import json
from loguru import logger
from vex.vex_thrift import embedding
from service.ESmemory.es_memory_client import client
from utils.time_decay import calculate_exponential_decay
from configs import lion_config

try:
    half_life_days = int(lion_config.lion_client.get_value_with_default("weiwei.half_life_days", 30))
except Exception as e:
    logger.error(f"获取half_life_days配置失败，使用默认值: {str(e)}")
    half_life_days = 30

# 确保type_weight是字典类型
try:
    type_weight = lion_config.lion_client.get_value_with_default("weiwei.type_weight", {"long": 1.2, "short": 1})
    if isinstance(type_weight, str):
        type_weight = json.loads(type_weight)
except Exception as e:
    logger.error(f"获取或解析type_weight配置失败，使用默认值: {str(e)}")
    type_weight = {"long": 1.2, "short": 1}

def generate_search_body(user_input, size, k, user_id, memory_type):
    """
    生成搜索体
    Args:
        user_input: 用户输入
        size: 返回结果数量
        k: 候选数量
        user_id: 用户id
        memory_type: 记忆类型
    """
    try:
        return {
            "size": size,
            "_source": {
                "excludes": ["memory_vector"]
            },
            "query": {
                "knn": {
                    "field": "memory_vector",
                    "query_vector": embedding(user_input),
                    "num_candidates": k,
                    "filter": [
                    {"term": {"user_id": user_id}},
                    {"term": {"memory_type": memory_type}}
                    ]
                }
            }
        }
    except Exception as e:
        logger.error(f"生成搜索体失败: {str(e)}")
        # 返回一个简单的空查询，避免程序崩溃
        return {
            "size": 0,
            "query": {
                "match_none": {}
            }
        }

def search_memory(index_name:str, user_input:str, size:int, k:int, user_id:str, memory_type:str):
    """
    搜索文档，一个示例搜索体格式：
    search_body = {
        "size": 5,
        "_source": {
            "excludes": ["memory_vector"]
        },
        "query": {
            "knn": {
                "field": "memory_vector",
                "query_vector": embedding("用户喜欢吃烤鸡"),
                "num_candidates": 5,
                "filter": [
                    {"term": {"user_id": "liulingfeng05"}},
                    {"term": {"memory_type": "long"}}
                ]
            }
        }
    }
    """
    try:
        response = client.search(index=index_name, body=generate_search_body(user_input, size, k, user_id, memory_type))
        return response
    except Exception as e:
        logger.error(f"搜索{index_name}出错: {str(e)}")
        # 返回一个空的搜索结果，避免程序崩溃
        return {"hits": {"hits": [], "total": {"value": 0}}}

def callback_memory(index_name:str, user_input:str, user_id:str, long_count:int, short_count:int):
    """
    回调记忆,根据用户输入分别返回对应要求数量的长短期记忆
    """
    try:
        # 防止数量参数异常
        long_count = max(0, min(20, long_count))  # 限制在0-20之间
        short_count = max(0, min(20, short_count))
        
        # 获取长期记忆
        try:
            long_memory = search_memory(index_name, user_input, long_count, long_count, user_id, "long")["hits"]["hits"]
        except Exception as e:
            logger.error(f"获取长期记忆失败: {str(e)}")
            long_memory = []
            
        # 获取短期记忆
        try:
            short_memory = search_memory(index_name, user_input, short_count, short_count, user_id, "short")["hits"]["hits"]
        except Exception as e:
            logger.error(f"获取短期记忆失败: {str(e)}")
            short_memory = []
            
        # 合并记忆列表
        memory_list = long_memory + short_memory
        logger.info(f"长短期记忆数量: {len(memory_list)}")
        logger.info(f"长短期记忆原始内容: {memory_list}")
        
        # 处理记忆项，计算相似度
        memories = []
        for hit in memory_list:
            try:
                memory_type = hit["_source"].get("memory_type", "short")
                # 使用get方法安全地获取值，提供默认值
                memory_weight = type_weight.get(memory_type, 1.0)
                if not isinstance(memory_weight, (int, float)):
                    memory_weight = float(memory_weight)
                
                time_stamp = hit["_source"].get("time_stamp", "2025-01-01-00-00")
                decay_factor = calculate_exponential_decay(time_stamp, half_life_days)
                
                memories.append({
                    "memory_content": hit["_source"].get("memory_content", ""),
                    "memory_type": memory_type,
                    "similarity": hit.get("_score", 0) * decay_factor * memory_weight
                })
            except Exception as e:
                logger.error(f"处理记忆项时出错: {str(e)}")
                continue
        
        # 排序记忆
        memories = sorted(memories, key=lambda x: x.get("similarity", 0), reverse=True)
        logger.info(f"长短期记忆排序后最终结果: {memories}")
        return memories
    except Exception as e:
        logger.error(f"回调记忆过程中发生错误: {str(e)}")
        # 返回空列表，避免程序崩溃
        return []


