import base64
import requests
from opensearchpy import OpenSearch
from configs.config import APP_KEY, CURRENT_ENV
from configs import lion_config
from utils.logger import logger


def generate_token(appkey, accesskey):
    """生成Basic Auth的TOKEN"""
    try:
        s = f"{appkey}:{accesskey}"
        return base64.b64encode(s.encode()).decode()
    except Exception as e:
        logger.error(f"生成令牌失败: {str(e)}")
        return None


def call_openapi(appkey, accesskey, cluster_name):
    """调用OpenAPI获取集群节点信息"""
    try:
        token = generate_token(appkey, accesskey)
        if not token:
            logger.error("无法生成令牌，使用默认节点配置")
            return [{"host": "localhost", "port": 9200, "scheme": "http"}]
            
        headers = {
            "Authorization": f"Basic {token}"
        }
        url = f"{openapi_url}/clusters/{cluster_name}/nodes"
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # 如果请求失败则抛出异常

        data = response.json()
        if data["code"] != 0:
            logger.error(f"获取节点信息失败: {data['message']}")
            return [{"host": "localhost", "port": 9200, "scheme": "http"}]

        # 提取节点的HTTP地址
        nodes = [{"host": node['ip'], "port": node['httpPort'], "scheme": "http"} for node in data["data"]]
        return nodes
    except Exception as e:
        logger.error(f"调用OpenAPI获取节点信息失败: {str(e)}")
        # 返回默认节点，避免程序崩溃
        return [{"host": "localhost", "port": 9200, "scheme": "http"}]


# 配置参数
cluster_name = "nlp_xiaomeies_default" if CURRENT_ENV == "test" else "nlp_xiaomei_default"
openapi_url = "http://openapi.eagle.test.sankuai.com/openapi" if CURRENT_ENV == "test" else "http://eagleweb.sankuai.com/openapi/"

# cluster_name = "nlp_xiaomei_default"
# openapi_url = "http://eagleweb.sankuai.com/openapi/"
# 获取节点列表
try:
    nodes = call_openapi(APP_KEY, lion_config.ES_ACCESS_KEY, cluster_name)
    # python3.10和Elasticsearch的7.10.0版本不兼容，因此使用OpenSearch
    # 这里使用ElasticSearch8.12,但是为了保持一致性继续使用OpenSearch
    client = OpenSearch(
        hosts=nodes,
        http_auth=(APP_KEY, lion_config.ES_ACCESS_KEY),
        use_ssl=False,
        verify_certs=False,
        sniff_on_start=True,
        sniff_on_connection_fail=True,
        sniffer_timeout=60
    )
except Exception as e:
    logger.error(f"初始化ES客户端失败: {str(e)}")
    # 创建一个无效的客户端对象
    # 后续代码中需要捕获这个客户端可能引发的异常
    # 创建一个空的类，模拟客户端接口但不执行实际操作
    class DummyClient:
        def __getattr__(self, name):
            def dummy_method(*args, **kwargs):
                logger.error(f"ES客户端未正确初始化，无法执行{name}操作")
                if name in ['search', 'count']:
                    return {"hits": {"hits": [], "total": {"value": 0}}, "count": 0}
                return None
            return dummy_method
    
    client = DummyClient()