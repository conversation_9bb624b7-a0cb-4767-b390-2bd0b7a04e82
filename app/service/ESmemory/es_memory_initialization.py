from configs import lion_config
from configs.local_config import DEFAULT_MEMORY_INITIALIZATION_PROMPT
from service.ESmemory.es_memory_upsert import upsert_memory
from service.ESmemory.es_memory_update import  search_clean_memory_by_id
import json
from utils.logger import logger
from service.ai_client import send_to_ai

def initialize_memory(index_name:str, mis_id:str, user_orders:list[dict]):
    """
    由用户历史订单生成初始化的长期记忆
    """
    existed_long_memories = search_clean_memory_by_id(index_name, mis_id, "long")
    if existed_long_memories:
        logger.info(f"用户{mis_id}已存在长期记忆，跳过初始化，当前长期记忆条数: {len(existed_long_memories)}")
        return
    else:
        logger.info(f"用户{mis_id}不存在长期记忆，开始初始化")
        # 生成初始化长期记忆
        initialization_prompt = lion_config.lion_client.get_value_with_default(
            "weiwei.memory_initialization_prompt", 
            DEFAULT_MEMORY_INITIALIZATION_PROMPT
        )

        # 格式化用户订单
        order_history = json.dumps(user_orders, ensure_ascii=False) if user_orders else "无历史订单"

        # 生成初始化长期记忆        # 调用模型生成记忆内容
        query_input = {
            "model": lion_config.lion_client.get_value_with_default("weiwei.memory_generation_model", "gpt-4.1"),
            "messages": [{"role": "system", "content": initialization_prompt}, {"role": "user", "content": f"用户历史订单：{order_history}"}],
            "stream": False,
            "temperature": 0.0,
            "max_tokens": 1000
        }

        response = send_to_ai(query_input)
        response_data = json.loads(response.text)
        logger.info(f"初始化长期记忆的原始记录: {response_data}")
        memory_response_text = response_data["choices"][0]["message"]["content"].strip()
        memory_responses = json.loads(memory_response_text)
        logger.info(f"初始化长期记忆的原始记录: {memory_responses}")
        

        if isinstance(memory_responses, dict):
            memory_responses = [memory_responses]

        if not memory_responses:
            logger.info(f"用户{mis_id}没有生成任何长期记忆，跳过初始化")
            return

        
        for memory_response in memory_responses:
            try:
                # 解析JSON响应
                memory_sentence = memory_response.get("memory_content", "")
                memory_topic = memory_response.get("topic", "")

                response = upsert_memory(index_name, mis_id, memory_sentence, "long", memory_topic)
                logger.info(f"初始化上传长期记忆: {memory_sentence}, 主题: {memory_topic}, 结果: {response}")
            except Exception as e:
                logger.error(f"生成初始化长期记忆失败: {str(e)}，此次生成的原始记录是：{memory_response}")
                continue
