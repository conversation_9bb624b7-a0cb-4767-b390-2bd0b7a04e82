from configs import lion_config
from configs.local_config import DEFAULT_MEMORY_GENERATION_PROMPT
from service.ESmemory.es_memory_client import client
from service.ESmemory.es_memory_upsert import upsert_memory
from service.ESmemory.es_memory_update import delete_outdated_memories, update_long_memory, has_time_elapsed, search_update_memory, integrate_memories, update_memory
import json
import uuid
from utils.logger import logger
from service import context
from service.ai_client import send_to_ai



def get_last_updated_time(index_name:str, user_id:str):
    try:
        search_body = {
            "size": 1,
            "query": {
                "bool": {
                    "filter": [{"term": {"mis_id": user_id}}]
                }
            },
            "sort": [
                {"latest_date": {"order": "desc"}}
            ]
        }
        response = client.search(index=index_name, body=search_body)
        logger.info(f"获取{index_name}中用户{user_id}的最新更新时间: {response}")
        return response["hits"]["hits"][0]["_source"]["latest_date"]
    except Exception as e:
        logger.error(f"获取{index_name}中用户{user_id}的最新更新时间出错: {str(e)}")
        return "2000-01-01-00-00"

def begin_memory_service(mis_id: str, history: list[dict['content':str]], user_orders: list[dict], token: str, model_type: str, success: bool, function_name: str, **kwargs) -> list[str]:
    """
    记忆服务逻辑，首先生成记忆并上传，然后删除过时的短期记忆，然后执行记忆总结更新逻辑：短期记忆汇总为长期记忆。
    """
    update_date_index = lion_config.lion_client.get_value_with_default("weiwei.update_date_index", "memory_last_modified_date")
    update_wait_time = int(lion_config.lion_client.get_value_with_default("weiwei.update_wait_time", 15))
    es_index_name = lion_config.lion_client.get_value_with_default("weiwei.memory_index", "memory_es_test")
    memory_facts = []
    
    # 新的记忆生成方法
    if mis_id and success:
        # 获取记忆生成提示词
        memory_generation_prompt = lion_config.lion_client.get_value_with_default(
            "weiwei.memory_generation_prompt", 
            DEFAULT_MEMORY_GENERATION_PROMPT
        )
                    
                    # 获取最近五条消息
        recent_messages = history[-5:] if len(history) >= 5 else history
                    
        # 格式化对话历史
        dialogue_history = ""
        for msg in recent_messages:
            role = msg.get('role', '')
            content = msg.get('content', '')
            dialogue_history += f"{role}: {content}\n"
                    
        # 格式化用户订单
        order_history = json.dumps(user_orders, ensure_ascii=False) if user_orders else "无历史订单"
                    
        
                    
        # 获取当前时间和天气
        current_time = context.get_time_info()
        current_weather = context.get_weather_prompt()
                    
        # 构建输入提示

        input_prompt = f"\n\n当前用户对话状态：{function_name}"


        if function_name == "Share":
            # 添加分享信息上下文
            share_id = kwargs.get("share_id", "")
            title = kwargs.get("title", "")
            content = kwargs.get("content", "")
            shop_name = kwargs.get("shop_name", "")

            share_context = f"用户分享了内容到社区广场，分享id：{share_id}，分享标题：{title}，分享内容：{content}，分享店家：{shop_name}"
            input_prompt +=f"\n\n分享信息：{share_context}"
        elif function_name == "Edit":
            # 添加编辑信息上下文
            edit_id = kwargs.get("edit_id", "")
            edit_title = kwargs.get("edit_title", "")
            edit_content = kwargs.get("edit_content", "")
            edit_context = f"用户编辑了内容到社区广场，编辑id：{edit_id}，编辑标题：{edit_title}，编辑内容：{edit_content}"
            input_prompt +=f"\n\n编辑信息：{edit_context}"
        elif function_name == "Delete":
            # 添加删除信息上下文
            delete_id = kwargs.get("delete_id", "")
            delete_context = f"用户删除了内容到社区广场，删除id：{delete_id}"
            input_prompt +=f"\n\n删除信息：{delete_context}"

        input_prompt +=f"\n\n最近对话历史：{dialogue_history}"
        input_prompt +=f"\n\n用户外卖历史订单：{order_history}"
        input_prompt +=f"\n\n当前时间：{current_time}"
        input_prompt +=f"\n\n当前天气：{current_weather}"
        input_prompt +=f"\n\n请生成一句信息充分的记忆内容扩句："
                    
        # 调用模型生成记忆内容
        query_input = {
            "model": lion_config.lion_client.get_value_with_default("weiwei.memory_generation_model", "gpt-4o-mini"),
            "messages": [{"role": "user", "content": input_prompt},{"role": "system", "content": memory_generation_prompt}],
            "stream": False,
            "temperature": 0.0,
            "max_tokens": 200
        }
                    
        response = send_to_ai(query_input)
        response_data = json.loads(response.text)
        memory_response_text = response_data["choices"][0]["message"]["content"].strip()
        memory_responses = json.loads(memory_response_text)

        if isinstance(memory_responses, dict):
            memory_responses = [memory_responses]

        
        logger.info(f"列表形式的memory_responses: {memory_responses}")
        for memory_response in memory_responses:
            try:
                # 解析JSON响应
                memory_sentence = memory_response.get("memory_content", "")
                is_sufficient_context = memory_response.get("is_sufficient_context", False)
                # 获取memory_type，如果没有默认为"short"
                memory_type = memory_response.get("memory_type", "short")
                topic = memory_response.get("topic", memory_sentence)
                logger.info(f"生成记忆内容：{memory_sentence}, 上下文充足: {is_sufficient_context}, 记忆类型: {memory_type}, 记忆主题: {topic}")
                
                # 只有当上下文充足时才添加时间与天气信息
                if is_sufficient_context and mis_id:
                    # 添加时间与天气信息概略
                    # time_info = context.get_time_summary()
                    # weather_info = context.get_weather_summary()

                    # memory_sentence += f"\n\n当前时间：{time_info}"
                    # if weather_info is not None:
                    #     memory_sentence += f"\n\n当前天气：{weather_info}"
                    
                    # 将生成的记忆内容添加到memory_facts
                    # memory_facts.append(memory_sentence)
                    
                    # 使用ES上传记忆
                    logger.info(f"上传记忆到ES: user_id={mis_id}, memory_type={memory_type}, topic={topic}")

                    #TODO:如果是长期记忆，调用update里面的函数，看看会不会有topic相同的记忆，如果有，整合之后上传，如果没有，直接上传
                    if memory_type == "long":
                        # 搜索相关的长期记忆    
                        integrate_candidate_num = int(lion_config.lion_client.get_value_with_default("weiwei.integrate_candidate_num", 10))
                        search_result = search_update_memory(es_index_name, topic, integrate_candidate_num, integrate_candidate_num, mis_id, "long")
                        logger.info(f"搜索到的长期记忆: {search_result}，长度为{len(search_result)}, 类型为{type(search_result)}")
                        logger.info(f"即将整合的长期记忆内容: {memory_sentence}")
                        # 判断是否需要整合入之前的记忆
                        integration_results = integrate_memories(memory_sentence, search_result, mis_id)
                        logger.info(f"整合完毕欧耶")
                        contain_new_memory = True
                        for result in integration_results:
                            if result["doc_id"]:
                                try:    
                                    # 如果有doc_id，说明需要更新现有记忆
                                    contain_new_memory = False
                                    update_response = update_memory(es_index_name, result["memory_content"], result["doc_id"])
                                    if update_response.get("result") == "updated":
                                        logger.info(f"成功为用户 {mis_id} 更新整合长期记忆 {result['memory_content']}，新长期记忆已经与之前的记忆融合。")
                                    else:
                                        logger.error(f"用户 {mis_id} 的整合长期记忆更新失败: {update_response}")
                                except Exception as e:
                                    logger.error(f"更新整合长期记忆时出错: {e}")

                        # 如果contain_new_memory仍然为True，说明没有整合入之前的记忆，直接上传
                        if contain_new_memory:
                            logger.info(f"用户 {mis_id} 没有整合入之前的记忆，直接上传新长期记忆 {memory_sentence}")
                            update_response = upsert_memory(es_index_name, mis_id, memory_sentence, memory_type, topic)
                    else:
                        update_response = upsert_memory(es_index_name, mis_id, memory_sentence, memory_type, topic)
            
                    logger.info(f"ES上传记忆结果: {update_response}")
                else:
                    logger.info("背景信息不足，跳过记忆上传")
                    
            except Exception as e:
                logger.error(f"解析记忆时出错: {str(e)}")

        # 删除过期记忆  
        delete_outdated_memories(es_index_name, mis_id)

        #总结短期记忆#到es7上去获取mis_id对应的长期记忆最近修改时间，时间差长于给定值的才新增上传。

        last_updated_time = get_last_updated_time(update_date_index, mis_id)
        logger.info(f"用户{mis_id}的长期记忆最近更新时间: {last_updated_time}, 更新间隔: {update_wait_time}天，是否需要更新: {has_time_elapsed(last_updated_time, update_wait_time)}")
        if has_time_elapsed(last_updated_time, update_wait_time):
            logger.info(f"用户{mis_id}的长期记忆最近更新时间大于{update_wait_time}天，最近更新于{last_updated_time}，开始更新长期记忆")
            update_long_memory(es_index_name, mis_id)
        else:
            logger.info(f"用户{mis_id}的长期记忆最近更新时间小于{update_wait_time}天，最近更新于{last_updated_time}，跳过长期记忆更新")
    # if memory_facts and mis_id:
    #     logger.info(f"为用户 {mis_id} 和代理 {function_name} 上传记忆: {memory_facts}")
    #     logger.debug(f"Agent传递给memory_service的token状态: {token is not None}")
    #     memory_service = MemoryService()
    #     assistant_message_id = f"{function_name}_{uuid.uuid4()}"
    #     memory_service.update_memory(
    #         assistant_message_id=assistant_message_id,
    #         agent_id=lion_config.lion_client.get_value_with_default("weiwei.agent_id", DEFAULT_AGENT_ID),
    #         new_retrieved_facts=memory_facts,
    #         user_id=mis_id,
    #         model=model_type,
    #         token=token
    #     )
    
    return memory_sentence

if __name__ == "__main__":
    print(get_last_updated_time("memory_last_modified_time", "liulingfeng05"))