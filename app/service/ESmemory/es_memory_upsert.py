from utils.logger import logger
from vex.vex_thrift import embedding
from datetime import datetime
from uuid import uuid4
from service.ESmemory.es_memory_client import client


def generate_doc(user_id, memory_content, memory_type, topic):
    """
    生成文档
    Args:
        user_id: 用户id
        memory_content: 记忆内容
        memory_type: 记忆类型
    """
    try:
        # 设置时间戳
        if memory_type == "long":
            time_stamp = "9999-12-31-23-59"
        else:
            time_stamp = datetime.now().strftime("%Y-%m-%d-%H-%M")
        
        # 生成向量嵌入
        try:
            memory_vector = embedding(memory_content)
            topic_vector = embedding(topic)
        except Exception as e:
            logger.error(f"生成向量嵌入失败: {str(e)}")
            # 使用空向量作为替代
            memory_vector = [0.0] * 768  # 向量维度为768
            topic_vector = [0.0] * 768  # 向量维度为768
        
        return {
            "doc": {
                "memory_content": memory_content,
                "memory_type": memory_type,
                "user_id": user_id,
                "topic": topic,
                "memory_vector": memory_vector,
                "topic_vector": topic_vector,
                "time_stamp": time_stamp
            },
            "doc_as_upsert": True
        }
    except Exception as e:
        logger.error(f"生成文档对象失败: {str(e)}")
        # 返回一个基本的文档对象，避免程序崩溃
        return {
            "doc": {
                "memory_content": memory_content or "",
                "memory_type": memory_type or "short",
                "user_id": user_id or "unknown",
                "topic": topic or "",
                "time_stamp": datetime.now().strftime("%Y-%m-%d-%H-%M")
            },
            "doc_as_upsert": True
        }


def upsert_memory(index_name:str, user_id:str, memory_content:str, memory_type:str, topic:str, doc_id:str = ""):
    """
    上传文档到ES，一个示例doc格式：
    {
        "doc": {
            "memory_content": "用户喜欢吃烤鸡",
            "memory_type": "long",
            "topic": "烤鸡",
            "time_stamp": "2025-04-18-15-40",
            "user_id": "liulingfeng05",
            "memory_vector": a word embedding vector given by app/vex/vex_thrift.py embedding function of memory_content,
            "topic_vector": a word embedding vector given by app/vex/vex_thrift.py embedding function of topic
        },
        "doc_as_upsert": True  # 如果文档不存在则插入，存在则更新
    }
    """
    if not memory_content:
        logger.warning("记忆内容为空，跳过上传")
        return {"result": "skipped", "reason": "empty_content"}
        
    try:
        # 生成唯一的文档ID
        if not doc_id:  
            doc_id = f"{user_id}_{memory_type}_{uuid4()}"
        
        # 生成文档对象
        doc = generate_doc(user_id, memory_content, memory_type, topic)
        
        # 执行更新操作
        response = client.update(index=index_name, id=doc_id, body=doc)
        logger.info(f"插入{index_name}成功: {doc_id}")
        return response
    except Exception as e:
        logger.error(f"插入{index_name}出错: {str(e)}")
        # 返回错误信息，避免程序崩溃
        return {"result": "error", "reason": str(e)}



if __name__ == "__main__":
    index_name = "memory_es_test"
    
    # upsert_memory(index_name, "liulingfeng05", "用户最近讨厌玩王者荣耀", "short", "披萨")
    # upsert_memory(index_name, "liulingfeng05", "用户不想玩王者荣耀", "short", "披萨")
    # upsert_memory(index_name, "liulingfeng05", "用户想卸载王者荣耀", "short", "放假")
    # upsert_memory(index_name, "liulingfeng05", "用户恨王者荣耀", "short", "睡觉")
    # upsert_memory(index_name, "liulingfeng05", "用户讨厌王者荣耀", "short", "感冒")

    # 长期记忆
    upsert_memory(index_name, "liulingfeng05", "用户从2025年3月开始搬出了宿舍，现在是在睡大街", "long", "住宿舍")
    # upsert_memory(index_name, "liulingfeng05", "用户在2025年4月开始睡大街", "long", "睡大街")
    # upsert_memory(index_name, "liulingfeng05", "用户每顿都要吃香菜", "long", "香菜")
    # upsert_memory(index_name, "liulingfeng05", "用户每日香菜摄入量为1kg", "long", "香菜")
    # upsert_memory(index_name, "liulingfeng05", "用户爱上吃香菜了", "long", "香菜")
 
    # search_result = search_memory(index_name, "用户在4月18日，星期五，想放假，天气雨", 5, 10, "liulingfeng05", "short")
    # print(search_result['hits']['hits'])

    # delete_memories_by_user_id(index_name, "liulingfeng05")


