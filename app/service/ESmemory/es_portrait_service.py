#TODO: 画像服务
"""
每次对话调用记忆服务后，都要调用画像的服务，类似记忆的工作流程。大体内容是，首先根据用户输入生成json{portrait_content, should_upload}，如果should_upload==True,根据user_id召回已有的portrait，
并把当前的portrait_content和已有的portrait content进行整合，把当前portrait蕴含的个人信息或禁忌记录进画像，合成一个新的字符串，然后更新上传，使用原doc_id，注意如果根据user id召回的内容为空那么说明用户还没有画像，
则要让新生成的portrait_content符合预先规定的格式，然后上传新用户画像。还有一个长期记忆与画像的冲突检测机制，如果长期记忆与画像开始发生冲突，则以长期记忆为准，更新画像内容。
"""

from service.ESmemory.es_memory_client import client
from service.ESmemory.es_memory_service import get_last_updated_time
from utils.logger import logger
from uuid import uuid4
from configs import lion_config
from configs.local_config import DEFAULT_PORTRAIT_GENERATION_PROMPT, DEFAULT_PORTRAIT_CONFLICT_DETECTION_PROMPT, DEFAULT_PORTRAIT_INTEGRATION_PROMPT
import json
from service.ESmemory.es_memory_update import has_time_elapsed, search_update_memory
from service.ai_client import send_to_ai
from service.ESmemory.es_memory_update import delete_memories_by_user_id
from service import context
from datetime import datetime

def generate_portrait_search_body_by_id(user_id):
    """
    生成搜索体，给定user id对应最多一千条文档，全部是短期记忆
    Args:
        user_id: 用户id
    """
    try:
        
        return {
            "size": 1,
            "query": {
                "term": {
                    "user_id": user_id
                }
            }
        }
        
    except Exception as e:
        logger.error(f"生成搜索体失败: {str(e)}")
        # 返回一个简单的空查询
        return {
            "size": 0,
            "query": {
                "match_none": {}
            }
        }
    
def update_portrait_time(index_name:str, user_id:str):
    """
    更新画像的最近更新时间
    """
    try:
        # 获取当前时间并格式化为YYYY-MM-DD-HH-MM
        current_time = datetime.now().strftime("%Y-%m-%d-%H-%M")
        response = client.update(index=index_name, id=user_id, body={
            "doc": {
            "latest_date": current_time,
            "mis_id": user_id
            },
            "doc_as_upsert": True
        })
        logger.info(f"更新画像最近更新时间的返回值：{response}")
    except Exception as e:
        logger.error(f"更新画像最近更新时间失败: {str(e)}")
    
def search_portrait_by_id(index_name:str, user_id:str):
    """
    给定user id（也就是mis id）搜索对应用户的画像
    """
        
    try:
        # 生成搜索体
        search_body = generate_portrait_search_body_by_id(user_id)
        
        # 执行搜索操作
        response = client.search(index=index_name, body=search_body)
        return response
    except Exception as e:
        logger.error(f"搜索{index_name}出错: {str(e)}")
        # 返回空的搜索结果，避免程序崩溃
        return {"hits": {"hits": [], "total": {"value": 0}}}
    
def get_portrait_by_id(user_id:str):
    """
    给定user id（也就是mis id）获取并格式化对应用户的画像
    """
    try:
        portrait_index_name = lion_config.lion_client.get_value_with_default("weiwei.portrait_index", "es_user_portrait")
        response = search_portrait_by_id(portrait_index_name, user_id)["hits"]["hits"]
        if response:
            portrait_dict = response[0]["_source"]
            forbidden_food = "用户不吃以下食物："
            personal_information = "用户个人信息："
            for key, value in portrait_dict.items():
                if key == "forbidden_food" :
                    if isinstance(value, list):
                        forbidden_food += ",".join(value)
                        forbidden_food += "\n"
                    else:
                        forbidden_food += f"{value}\n"
                elif key == "personal_information":
                    if isinstance(value, list):
                        personal_information += ",".join(value)
                        personal_information += "\n"
                    else:
                        personal_information += f"{value}\n"
                else:continue
            return forbidden_food + personal_information
        else:
            return ""
    except Exception as e:
        logger.error(f"处理画像失败: {str(e)}")
        return ""
    
def upsert_portrait(index_name:str, user_id:str, portrait_content:dict, doc_id:str):
    """
    上传画像
    """
    try:
        if not doc_id:
            doc_id = user_id+"_"+str(uuid4())

        portrait_content["user_id"] = user_id
        update_portrait_body = {
            "doc": portrait_content,
            "doc_as_upsert": True
        }
        client.update(index=index_name, id=doc_id, body=update_portrait_body)
        logger.info(f"上传画像成功，内容: {portrait_content},id: {doc_id}")
        return {"doc": portrait_content, "doc_id": doc_id}
    except Exception as e:
        logger.error(f"上传画像失败: {str(e)}")
        return {"doc": {}, "doc_id": ""}
    
def integrate_portrait(portrait_content:dict, portrait_content_from_db:dict) -> dict:
    """
    将新的画像内容按格式整合到旧画像内容中，返回一个整合后的portrait_content
    Args:
        portrait_content: 新的画像内容，dict形式，每个key对应一个字符串或list
        portrait_content_from_db: 旧画像内容，dict形式
    """
    # 记录日志查看输入内容
    logger.info(f"新的画像内容：{portrait_content},旧的画像内容：{portrait_content_from_db}")

    # 从配置获取画像整合提示词
    integration_prompt = lion_config.lion_client.get_value_with_default(
        "weiwei.portrait_integration_prompt", 
        DEFAULT_PORTRAIT_INTEGRATION_PROMPT
    )
    
    # 准备发送给AI的数据
    messages = [
        {
            "role": "system",
            "content": integration_prompt + "\n注意：返回的JSON必须使用双引号而不是单引号。"
        },
        {
            "role": "user",
            "content": f"新的画像内容：{portrait_content},旧的画像内容：{portrait_content_from_db}"
        }
    ]
    
    # 调用AI服务
    data = {
        "model": lion_config.lion_client.get_value_with_default("weiwei.portrait_integration_model", "gpt-4o-mini"),
        "messages": messages,
        "temperature": 0.1,
        "max_tokens": 1000,
    }
    
    try:
        response = send_to_ai(data)
        response_text = response.text
        response_json = json.loads(response_text)
        
        if "choices" in response_json and len(response_json["choices"]) > 0:
            ai_response_text = response_json["choices"][0]["message"]["content"].strip()
            
            # 尝试解析AI返回的JSON
            try:
                # 首先尝试直接解析
                try:
                    merged_portrait = json.loads(ai_response_text)
                except json.JSONDecodeError:
                    # 如果失败，尝试将单引号转换为双引号并重新解析
                    import ast
                    # 使用ast.literal_eval安全地解析Python字典字符串
                    merged_portrait = ast.literal_eval(ai_response_text)
                
                # 确保返回的是字典类型
                if not isinstance(merged_portrait, dict):
                    logger.error(f"AI返回的不是字典类型: {merged_portrait}")
                    return portrait_content_from_db
                
                # 确保包含所有必需的字段
                required_fields = {"forbidden_food", "personal_information", "order_preference"}
                for field in required_fields:
                    if field not in merged_portrait:
                        merged_portrait[field] = portrait_content_from_db.get(field, [])
                
                # 保留旧画像中的user_id
                if "user_id" in portrait_content_from_db:
                    merged_portrait["user_id"] = portrait_content_from_db["user_id"]
                
                logger.info(f"整合后的画像内容：{merged_portrait}")
                return merged_portrait
                
            except (json.JSONDecodeError, ValueError, SyntaxError) as e:
                logger.error(f"无法解析AI响应为JSON或字典: {ai_response_text}, 错误: {str(e)}")
                return portrait_content_from_db
        else:
            logger.error(f"AI响应格式错误: {response_json}")
            return portrait_content_from_db
    except Exception as e:
        logger.error(f"画像整合过程中发生错误: {e}")
        return portrait_content_from_db
        
def conflict_detection(portrait_content:str, type:str, mis_id:str) -> bool:
    """
    检测画像与长期记忆是否冲突
    Args:
        portrait_content: 画像内容中的某一条信息
        type: 画像内容的类型，可能是 "forbidden_food" 或 "personal_information"
        long_memory_contents: 长期记忆列表
    Returns:
        dict: 包含是否存在冲突和更新内容的字典
    """
    memory_index_name = lion_config.lion_client.get_value_with_default("weiwei.memory_index", "memory_es_test")
    
    # 从配置获取冲突检测提示词
    conflict_detection_prompt = lion_config.lion_client.get_value_with_default(
        "weiwei.portrait_conflict_detection_prompt", 
        DEFAULT_PORTRAIT_CONFLICT_DETECTION_PROMPT
    )
    
    # 使用portrait_content作为topic搜索相关记忆
    # 确定memory_type为"long"，确保我们只检查长期记忆
    relevant_memories = search_update_memory(
        index_name=memory_index_name,
        topic=portrait_content,
        size=5,  # 获取最相关的5条记忆
        k=10,    # 候选数量
        user_id=mis_id,
        memory_type="long"
    )
    
    # 如果没有相关记忆，则不存在冲突
    if not relevant_memories or len(relevant_memories) == 0:
        logger.info(f"没有找到与画像内容相关的长期记忆，跳过冲突检测: {portrait_content}")
        return {"conflict_detected": False, "content": ""}
    
    logger.info(f"找到与画像内容相关的长期记忆: {relevant_memories}")
    
    # 准备记忆内容字符串
    memory_contents_str = ""
    for memory in relevant_memories:
        if isinstance(memory, dict) and 'memory_content' in memory:
            memory_contents_str += f"- {memory['memory_content']}\n"
    
    # 如果没有有效的记忆内容，则不存在冲突
    if not memory_contents_str:
        logger.info(f"没有有效的记忆内容，跳过冲突检测: {portrait_content}")
        return {"conflict_detected": False, "content": ""}
    
    # 类型说明
    type_description = ""
    if type == "forbidden_food":
        type_description = "用户不吃的食物"
    elif type == "personal_information":
        type_description = "用户的个人信息"
    else:
        type_description = f"用户的{type}"
    
    # 准备发送给AI的数据
    messages = [
        {
            "role": "system",
            "content": conflict_detection_prompt
        },
        {
            "role": "user",
            "content": f"""
我需要检测用户的画像内容与长期记忆是否存在冲突。

画像内容类型: {type_description}
画像内容: {portrait_content}

相关的长期记忆:
{memory_contents_str}

请分析这些长期记忆是否与画像内容存在明显冲突。

"""
        }
    ]
    
    # 调用AI服务
    data = {
        "model": lion_config.lion_client.get_value_with_default("weiwei.portrait_conflict_model", "gpt-4o-mini"),
        "messages": messages,
        "temperature": 0.1,
        "max_tokens": 200,
    }
    
    try:
        response = send_to_ai(data)
        response_text = response.text
        response_json = json.loads(response_text)
        
        if "choices" in response_json and len(response_json["choices"]) > 0:
            ai_response_text = response_json["choices"][0]["message"]["content"].strip()
            
            # 尝试解析AI返回的JSON
            try:
                ai_response = json.loads(ai_response_text)
                has_conflict = ai_response.get("conflict_detected", False)
                new_content = ai_response.get("new_content", "")
                
                if has_conflict:
                    logger.info(f"检测到画像内容与长期记忆存在冲突: {ai_response}")
                else:
                    logger.info(f"画像内容与长期记忆不存在冲突: {ai_response}")
                
                return ai_response
            except json.JSONDecodeError:
                logger.error(f"无法解析AI响应为JSON: {ai_response_text}")
                return {"conflict_detected": False, "new_content": ""}
        else:
            logger.error(f"AI响应格式错误: {response_json}")
            return {"conflict_detected": False, "new_content": ""}
    except Exception as e:
        logger.error(f"冲突检测过程中发生错误: {e}")
        return {"conflict_detected": False, "new_content": ""}
            

def begin_portrait_service(mis_id: str, history: list[dict], function_name: str, **kwargs) -> str:
    portrait_index_name = lion_config.lion_client.get_value_with_default("weiwei.portrait_index", "es_user_portrait")
    portrait_update_time_index = lion_config.lion_client.get_value_with_default("weiwei.portrait_update_time_index", "portrait_last_checked_time")
    # 新的画像生成方法
    try:
        if mis_id:
            # 获取画像生成提示词

            portrait_update_time_range = float(lion_config.lion_client.get_value_with_default("weiwei.portrait_update_time_range", 30))
            portrait_generation_prompt = lion_config.lion_client.get_value_with_default(
                "weiwei.portrait_generation_prompt", 
                DEFAULT_PORTRAIT_GENERATION_PROMPT
            )
                        
                        # 获取最近五条消息
            recent_messages = history[-10:] if len(history) >= 10 else history
                        
            # 格式化对话历史
            dialogue_history = ""
            for msg in recent_messages:
                role = msg.get('role', '')
                content = msg.get('content', '')
                dialogue_history += f"{role}: {content}\n"

            current_time = context.get_time_summary()
            current_weather = context.get_weather_summary()

            input_prompt = f"\n\n当前用户对话状态：{function_name}"


            # if function_name == "Share":
            #     # 添加分享信息上下文
            #     share_id = kwargs.get("share_id", "")
            #     title = kwargs.get("title", "")
            #     content = kwargs.get("content", "")
            #     shop_name = kwargs.get("shop_name", "")

            #     share_context = f"用户分享了内容到社区广场，分享id：{share_id}，分享标题：{title}，分享内容：{content}，分享店家：{shop_name}"
            #     input_prompt +=f"\n\n分享信息：{share_context}"
            # elif function_name == "Edit":
            #     # 添加编辑信息上下文
            #     edit_id = kwargs.get("edit_id", "")
            #     edit_title = kwargs.get("edit_title", "")
            #     edit_content = kwargs.get("edit_content", "")
            #     edit_context = f"用户编辑了内容到社区广场，编辑id：{edit_id}，编辑标题：{edit_title}，编辑内容：{edit_content}"
            #     input_prompt +=f"\n\n编辑信息：{edit_context}"
            # elif function_name == "Delete":
            #     # 添加删除信息上下文
            #     delete_id = kwargs.get("delete_id", "")
            #     delete_context = f"用户删除了内容到社区广场，删除id：{delete_id}"
            #     input_prompt +=f"\n\n删除信息：{delete_context}"

            input_prompt +=f"\n\n最近对话历史：{dialogue_history}"
            # input_prompt +=f"\n\n用户外卖历史订单：{order_history}"
            input_prompt +=f"\n\n当前时间：{current_time}"
            input_prompt +=f"\n\n当前天气：{current_weather}"
            input_prompt +=f"\n\n如果以上信息包含画像内容的某个或者某些方面，请用简洁的语言，陈述句表达总结该内容："
                        
            # 调用模型生成记忆内容
            query_input = {
                "model": lion_config.lion_client.get_value_with_default("weiwei.portrait_model", "gpt-4o-mini"),
                "messages": [{"role": "user", "content": input_prompt},{"role": "system", "content": portrait_generation_prompt}],
                "stream": False,
                "temperature": 0.0,
                "max_tokens": 200
            }
                        
            response = send_to_ai(query_input)
            response_data = json.loads(response.text)
            portrait_response_text = response_data["choices"][0]["message"]["content"].strip()
            portrait_responses = json.loads(portrait_response_text)
            logger.info(f"portrait_responses:{portrait_responses}")

            old_portrait = search_portrait_by_id(portrait_index_name, mis_id)["hits"]["hits"]
            if old_portrait:
                logger.info(f"用户: {mis_id} 存在老画像，开始整合新画像与老画像,老画像内容：{old_portrait[0]['_source']}")
                # 如果存在老画像，则将新画像与老画像整合后上传
                old_portrait_content = old_portrait[0]["_source"]
                old_id = old_portrait[0]["_id"]
                new_portrait_content = integrate_portrait(portrait_responses, old_portrait_content)
                upsert_portrait(portrait_index_name, mis_id, new_portrait_content, old_id)
                logger.info(f"为用户: {mis_id} 更新画像成功，文档id: {old_id}，新内容：{new_portrait_content}") 

            else:
                # 否则直接上传新画像
                response = upsert_portrait(portrait_index_name, mis_id, portrait_responses, "")
                logger.info(f"为用户: {mis_id} 初次上传画像成功，文档id: {response['doc_id']}, 新内容：{portrait_responses}")


            
            #如果时间足够长，则进行画像与长期记忆的冲突检测
            logger.info(f"画像更新时间：{get_last_updated_time(portrait_update_time_index, mis_id)},更新时间间隔{portrait_update_time_range}，是否需要更新画像：{has_time_elapsed(get_last_updated_time(portrait_update_time_index, mis_id), portrait_update_time_range)}")
            if has_time_elapsed(get_last_updated_time(portrait_update_time_index, mis_id), portrait_update_time_range):
                logger.info(f"开始画像与长期记忆的冲突检测")
                portrait = search_portrait_by_id(portrait_index_name, mis_id)["hits"]["hits"]
                if not portrait:
                    logger.info(f"用户: {mis_id} 没有画像，跳过冲突检测")
                    return
                
                #按照每个属性每个元素分别做矛盾检测，记录矛盾检测是否返回True，最后删除所有返回True的元素，然后重新上传，并重写最近更新时间
                portrait_content = portrait[0]["_source"]
                
                # 记录需要删除的内容
                to_remove = {}
                # 记录需要添加的内容
                to_add = {}
                
                # 遍历画像内容的每个属性
                for type_key, content_values in portrait_content.items():
                    # 跳过用户ID等非画像内容字段
                    if type_key == "user_id" or type_key == "time_stamp":
                        continue
                        
                    # 如果内容是字符串，转为列表处理
                    if isinstance(content_values, str):
                        if content_values:  # 非空字符串
                            content_list = [content_values]
                        else:
                            continue  # 跳过空字符串
                    elif isinstance(content_values, list):
                        content_list = content_values
                    else:
                        logger.warning(f"未知的内容类型: {type(content_values)} for {type_key}")
                        continue
                    
                    # 对列表中的每个元素进行冲突检测
                    conflicts = []
                    for content_item in content_list:
                        # 调用冲突检测并获取结果
                        conflict_result = conflict_detection(content_item, type_key, mis_id)
                        
                        # 检查是否检测到冲突
                        if conflict_result.get("conflict_detected", False):
                            logger.info(f"检测到冲突: type={type_key}, content={content_item}")
                            conflicts.append(content_item)
                            
                            # 如果有新内容，记录下来准备添加
                            new_content = conflict_result.get("new_content", "")
                            if new_content:
                                if type_key not in to_add:
                                    to_add[type_key] = []
                                if new_content not in to_add[type_key]:  # 避免重复添加
                                    to_add[type_key].append(new_content)
                    
                    # 如果有冲突项，记录下来
                    if conflicts:
                        to_remove[type_key] = conflicts
                
                # 如果有冲突或需要添加新内容，更新画像
                if to_remove or to_add:
                    logger.info(f"发现冲突项或需要添加的内容，准备更新画像: 冲突项={to_remove}, 新内容={to_add}")
                    
                    # 从画像中删除冲突项
                    for type_key, conflict_items in to_remove.items():
                        if isinstance(portrait_content[type_key], str):
                            if portrait_content[type_key] in conflict_items:
                                portrait_content[type_key] = ""
                        elif isinstance(portrait_content[type_key], list):
                            portrait_content[type_key] = [item for item in portrait_content[type_key] if item not in conflict_items]
                    
                    # 添加新内容
                    for type_key, new_items in to_add.items():
                        if type_key not in portrait_content or not portrait_content[type_key]:
                            # 如果之前没有这个键或值为空，直接设置为新列表
                            portrait_content[type_key] = new_items
                        else:
                            # 已有内容，需要合并
                            if isinstance(portrait_content[type_key], str):
                                # 之前是字符串，转为列表
                                current_content = [portrait_content[type_key]]
                            else:
                                current_content = portrait_content[type_key]
                            
                            # 添加新内容到列表中
                            for item in new_items:
                                if item not in current_content:
                                    current_content.append(item)
                            
                            # 更新回portrait_content
                            if len(current_content) == 1:
                                portrait_content[type_key] = current_content[0]  # 单个值用字符串
                            else:
                                portrait_content[type_key] = current_content  # 多个值用列表
                    
                    # 更新画像
                    old_id = portrait[0]["_id"]
                    upsert_portrait(portrait_index_name, mis_id, portrait_content, old_id)
                    logger.info(f"更新用户画像成功: user_id={mis_id}, doc_id={old_id}, 更新后的内容: {portrait_content}")
                    update_portrait_time(portrait_update_time_index, mis_id)
                else:
                    logger.info(f"没有检测到画像与长期记忆的冲突: user_id={mis_id}")
                    update_portrait_time(portrait_update_time_index, mis_id)
    
    except Exception as e:
        logger.error(f"画像服务发生错误: {str(e)}")
        return 

if __name__ == "__main__":
    portrait_index_name = lion_config.lion_client.get_value_with_default("weiwei.portrait_index", "es_user_portrait")
    print(search_portrait_by_id(portrait_index_name, "liulingfeng05"))