from configs import lion_config
from utils.logger import logger

def check_is_admin(mis_id: str) -> tuple:
    """
    检查用户是否为管理员
    
    Args:
        mis_id: 用户的MIS ID
        
    Returns:
        tuple: (是否为管理员, 管理员列表)
    """
    try:
        if not mis_id:
            return False, []
        
        admin_list = []        
        # 从lion_config中获取管理员列表字符串
        original_admin_str = lion_config.ADMIN_LIST
        logger.info(f"从Lion获取的管理员列表原始字符串: {original_admin_str}")
        
        # 处理字符串格式，使用空格和换行符分割
        if original_admin_str:
            for line in original_admin_str.split('\n'):
                for item in line.split():
                    if item.strip():  # 过滤掉空字符串
                        admin_list.append(item.strip())
            
            logger.info(f"从Lion解析到的管理员列表: {admin_list}")
        else:
            # 如果获取失败或为空，使用空列表
            logger.info(f"未从Lion获取到管理员列表，使用空列表")
        
        # 检查用户是否在管理员列表中
        is_admin = mis_id in admin_list
        logger.info(f"用户 {mis_id} 是否为管理员: {is_admin}")
        
        return is_admin, admin_list
        
    except Exception as e:
        logger.error(f"检查管理员权限失败: {str(e)}")
        return False, [] 


def check_is_white_list(mis_id: str) -> tuple:
    """
    检查用户是否在白名单中
    
    Args:
        mis_id: 用户的MIS ID
        
    Returns:
        tuple: (是否在白名单中, 白名单列表)
    """
    try:
        if not mis_id:
            return False, []
        
        white_list = []        
        # 从lion_config中获取白名单列表字符串
        original_white_list_str = lion_config.WHITE_LIST
        logger.info(f"从Lion获取的白名单原始字符串: {original_white_list_str}")
        
        # 处理字符串格式，使用空格和换行符分割
        if original_white_list_str:
            for line in original_white_list_str.split('\n'):
                for item in line.split():
                    if item.strip():  # 过滤掉空字符串
                        white_list.append(item.strip())
            
            logger.info(f"从Lion解析到的白名单列表: {white_list}")
        else:
            # 如果获取失败或为空，使用空列表
            logger.info(f"未从Lion获取到白名单列表，使用空列表")
        
        # 检查用户是否在白名单中
        is_white_list = mis_id in white_list
        logger.info(f"用户 {mis_id} 是否在白名单中: {is_white_list}")
        
        return is_white_list, white_list
        
    except Exception as e:
        logger.error(f"检查白名单失败: {str(e)}")
        return False, []