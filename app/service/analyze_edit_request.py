from service.ai_client import send_to_ai
import json
from utils.logger import logger
from configs import lion_config
from configs.local_config import DEFAULT_ANALYZE_EDIT_REQUEST_PROMPT

def analyze_edit_request(user_input: str, history: list[dict['content':str]], memory_content: str|None=None) -> dict:
    """
    使用LLM分析用户输入和历史记录，确定编辑请求的详细信息
    
    Args:
        user_input: 用户输入
        history: 聊天历史记录
        
    Returns:
        dict: 包含edit_id, edit_title, edit_content, search_keyword的字典
    """
    try:
        # 提取用户最近的消息
        recent_messages = history[-5:] if len(history) >= 5 else history
        
        # 构建系统提示词
        system_prompt = lion_config.lion_client.get_value_with_default("weiwei.analyze_edit_request_prompt", DEFAULT_ANALYZE_EDIT_REQUEST_PROMPT)
        
        # 构建用户消息
        user_message = f"历史对话：\n"
        for msg in recent_messages:
            role = "用户" if msg["role"] == "user" else "助手"
            user_message += f"{role}: {msg['content']}\n"
        
        user_message += f"\n当前用户输入: {user_input}\n"
        if memory_content:
            user_message += f"\n记忆内容：{memory_content}"
        

        user_message += "\n请根据以上信息分析用户想要编辑的内容，并以JSON格式返回结果。"
        
        # 调用LLM进行分析
        query = {
            "model": "gpt-4o-mini",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ],
            "temperature": 0.0,
            "max_tokens": 1000
        }
        
        response = send_to_ai(query)
        response_text = json.loads(response.text)["choices"][0]["message"]["content"]
        
        # 提取JSON部分
        import re
        json_pattern = r'({[\s\S]*})'
        match = re.search(json_pattern, response_text)
        
        if match:
            result = json.loads(match.group(1))
        else:
            # 如果无法提取JSON，返回空结果
            result = {
                "edit_id": "",
                "edit_title": "",
                "edit_content": "",
                "search_keyword": ""
            }
        
        # 确保结果包含所有必要的字段
        for key in ["edit_id", "edit_title", "edit_content", "search_keyword"]:
            if key not in result:
                result[key] = ""
        
        return result
    
    except Exception as e:
        logger.error(f"分析编辑请求时出错: {str(e)}")
        # 返回默认空结果
        return {
            "edit_id": "",
            "edit_title": "",
            "edit_content": "",
            "search_keyword": user_input  # 出错时，至少使用用户输入作为搜索关键词
        }
