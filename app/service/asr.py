import hmac
import base64
import hashlib
from email.utils import formatdate
from utils.logger import logger
from utils.squirrel import RedisClient, build_category_key
import requests
import json

app_key = 'kJ08SdZlGBtezAelpdN8fVms+XGOgEzT82RV/uSKoG8='
secret_key = '70882a9640804b60bcb2bb8d39205cc6'
http_verb = 'POST'  # 按实际情况选择
uri = '/asr/v1/long_stream_recognize'  # 按实际情况传值

def generate_signature(verb, uri, date, secret_key):
    string_to_sign = f"{verb} {uri}\n{date}"
    return hmac_sha1(secret_key, string_to_sign)


def hmac_sha1(key, data):
    signature = hmac.new(key.encode(), data.encode(), hashlib.sha1)
    return base64.b64encode(signature.digest()).decode()


def call_asr_api(session_id, audio_param, audio_data):

    redis_client = RedisClient().get_instance().get_client()
    squirrel_key = build_category_key('session_asr_content', "s{0}", session_id)
    session_data = redis_client.get(squirrel_key)
    if session_data is None:
        session_data = ""

    # 设置请求的URL
    url = 'https://aispeech.sankuai.com/asr/v1/long_stream_recognize'  # 根据实际情况选择长或短流识别

    # 生成Asr-Params
    asr_params = {
        'audio_format': audio_param['format'],
        'sample_rate': audio_param['sampleRate'],
        'channel_num':  1,
        'text_normalization': 8,
        'index': audio_param['index']  # 假设这是第一包，实际使用中需要根据实际音频包序调整
    }
    asr_params_encoded = base64.b64encode(json.dumps(asr_params).encode()).decode()
    date = formatdate(timeval=None, localtime=False, usegmt=True)

    # 生成请求头
    headers = {
        'Content-Type': 'application/octet-stream',
        'Asr-Params': asr_params_encoded,
        'Authorization': f"AIAUTH-V1 {app_key}:{generate_signature(http_verb, uri, date, secret_key)}",
        'Date': date,
        'SessionID': session_id
    }
    logger.info(f"call_asr_api session_id={session_id} asr_params={json.dumps(asr_params)}")
    # 发送请求
    response = requests.post(url, headers=headers, data=audio_data)

    # 处理响应
    if response.status_code == 200:
        logger.info(f"call_asr_api success session_id={session_id} response={response.text}")
        response_content = response.content.decode('utf-8')
        response_json = json.loads(response_content)
        if response_json.get('data').get('status') == 3:
            redis_client.set(squirrel_key, session_data + response_json.get('data').get('text'))
        response_json['data']['full_text'] = session_data + response_json.get('data').get('text')
        return response_json
    else:
        logger.info(f"call_asr_api failed session_id={session_id} code={response.status_code} response={response.text}")
        return {
            'errcode': 500,
            'errmsg': f"call_asr_api failed session_id={session_id} code={response.status_code} response={response.text}"
        }
