import hmac
import base64
import hashlib
from email.utils import formatdate
from utils.logger import logger
from utils.squirrel import RedisClient, build_category_key
import requests
import json
app_key = 'kJ08SdZlGBtezAelpdN8fVms+XGOgEzT82RV/uSKoG8='
secret_key = '70882a9640804b60bcb2bb8d39205cc6'
http_verb = 'POST'  # 按实际情况选择
uri = '/asr/v1/long_stream_recognize'  # 按实际情况传值

# ASR音频时长限制配置
ASR_MIN_DURATION = 0.1  # 最小时长100毫秒（ASR服务要求）
ASR_MAX_DURATION = 60.0  # 最大时长60秒
ASR_MAX_SIZE = 10 * 1024 * 1024  # 最大音频数据大小10MB


def generate_signature(verb, uri, date, secret_key):
    string_to_sign = f"{verb} {uri}\n{date}"
    return hmac_sha1(secret_key, string_to_sign)


def hmac_sha1(key, data):
    signature = hmac.new(key.encode(), data.encode(), hashlib.sha1)
    return base64.b64encode(signature.digest()).decode()


def calculate_audio_duration(audio_data, sample_rate, channels=1,
                             sample_width=2):
    """
    计算音频数据的时长（秒）
    :param audio_data: 音频数据（字节格式）
    :param sample_rate: 采样率
    :param channels: 声道数，默认1
    :param sample_width: 采样宽度（字节数），默认2（16位）
    :return: 音频时长（秒）
    """
    if not audio_data:
        return 0

    # 计算样本数
    bytes_per_sample = channels * sample_width
    total_samples = len(audio_data) // bytes_per_sample

    # 计算时长
    duration = total_samples / sample_rate
    return duration


def split_audio_data(audio_data, sample_rate, channels=1, sample_width=2,
                     max_duration=ASR_MAX_DURATION):
    """
    将长音频数据分割成多个片段
    :param audio_data: 音频数据
    :param sample_rate: 采样率
    :param channels: 声道数
    :param sample_width: 采样宽度
    :param max_duration: 每个片段的最大时长
    :return: 音频片段列表
    """
    if not audio_data:
        return []

    # 计算每个片段的字节数
    bytes_per_second = sample_rate * channels * sample_width
    max_bytes_per_chunk = int(bytes_per_second * max_duration * 0.9)  # 留10%余量

    chunks = []
    offset = 0
    chunk_index = 1

    while offset < len(audio_data):
        # 计算当前片段的结束位置
        end_offset = min(offset + max_bytes_per_chunk, len(audio_data))

        # 确保在样本边界上分割
        bytes_per_sample = channels * sample_width
        end_offset = (end_offset // bytes_per_sample) * bytes_per_sample

        chunk_data = audio_data[offset:end_offset]
        if chunk_data:
            chunks.append({
                'data': chunk_data,
                'index': chunk_index,
                'offset': offset,
                'size': len(chunk_data),
                'duration': calculate_audio_duration(chunk_data, sample_rate,
                                                   channels, sample_width)
            })
            chunk_index += 1

        offset = end_offset

    return chunks


def validate_audio_data(audio_data, audio_param):
    """
    验证音频数据是否符合要求
    :param audio_data: 音频数据
    :param audio_param: 音频参数
    :return: (is_valid, error_message, should_split)
    """
    if not audio_data:
        return False, "音频数据为空", False

    # 获取音频参数
    sample_rate = audio_param.get('sampleRate', 16000)
    index = audio_param.get('index', 1)

    # 计算音频时长
    duration = calculate_audio_duration(audio_data, sample_rate)

    # 对于实时语音流，考虑ASR服务的实际限制
    # 实时流的index通常比较大，表示连续的音频片段
    is_realtime_stream = index > 100  # 假设index > 100表示实时流

    # 根据ASR服务实际测试结果，统一使用较严格的限制
    # 避免发送过短音频导致ASR服务返回400005
    min_duration = ASR_MIN_DURATION  # 统一使用100ms限制

    # 检查时长限制
    if duration < min_duration:
        stream_type = "实时流" if is_realtime_stream else "音频文件"
        return False, (f"{stream_type}音频时长过短: {duration:.3f}秒，"
                       f"ASR服务要求最小{min_duration:.3f}秒，"
                      f"建议前端缓冲更多音频数据后发送"), False

    # 检查音频数据大小
    if len(audio_data) > ASR_MAX_SIZE:
        return False, (f"音频数据过大: {len(audio_data)}字节，"
                       f"最大允许{ASR_MAX_SIZE}字节"), False

    # 如果时长超过限制，建议分片处理
    if duration > ASR_MAX_DURATION:
        return True, (f"音频时长较长: {duration:.2f}秒，"
                     f"将自动分片处理"), True

    return True, None, False


def call_single_asr_api(session_id, audio_param, audio_data, chunk_info=None):
    """
    调用单个ASR API请求
    :param session_id: 会话ID
    :param audio_param: 音频参数
    :param audio_data: 音频数据
    :param chunk_info: 分片信息（可选）
    :return: ASR响应结果
    """
    # 根据音频时长动态选择接口
    sample_rate = audio_param.get('sampleRate', 16000)
    duration = calculate_audio_duration(audio_data, sample_rate)

    # 短音频使用短流识别，长音频使用长流识别
    if duration < 1.0:  # 小于1秒使用短流识别
        url = 'https://aispeech.sankuai.com/asr/v1/short_stream_recognize'
        uri_path = '/asr/v1/short_stream_recognize'
    else:
        url = 'https://aispeech.sankuai.com/asr/v1/long_stream_recognize'
        uri_path = '/asr/v1/long_stream_recognize'

    # 生成Asr-Params
    asr_params = {
        'audio_format': audio_param['format'],
        'sample_rate': audio_param['sampleRate'],
        'channel_num':  1,
        'text_normalization': 8,
        'index': chunk_info['index'] if chunk_info else audio_param['index']
    }
    asr_params_encoded = base64.b64encode(
        json.dumps(asr_params).encode()).decode()
    date = formatdate(timeval=None, localtime=False, usegmt=True)

    # 生成请求头
    auth_signature = generate_signature(http_verb, uri_path, date, secret_key)
    headers = {
        'Content-Type': 'application/octet-stream',
        'Asr-Params': asr_params_encoded,
        'Authorization': f"AIAUTH-V1 {app_key}:{auth_signature}",
        'Date': date,
        'SessionID': session_id
    }

    chunk_desc = f"chunk_{chunk_info['index']}" if chunk_info else "single"
    api_type = "short_stream" if duration < 1.0 else "long_stream"
    logger.info(f"call_asr_api {chunk_desc} session_id={session_id} "
                f"duration={duration:.2f}s api_type={api_type} "
                f"asr_params={json.dumps(asr_params)}")

    # 发送请求
    try:
        response = requests.post(url, headers=headers, data=audio_data,
                                 timeout=30)
    except requests.exceptions.Timeout:
        logger.error(f"call_asr_api timeout session_id={session_id} {chunk_desc}")
        return {
            'errcode': 500,
            'errmsg': f'ASR服务请求超时 ({chunk_desc})'
        }
    except requests.exceptions.RequestException as e:
        logger.error(f"call_asr_api request error session_id={session_id} "
                     f"{chunk_desc} error={str(e)}")
        return {
            'errcode': 500,
            'errmsg': f'ASR服务请求失败 ({chunk_desc}): {str(e)}'
        }

    # 处理响应
    if response.status_code == 200:
        logger.info(f"call_asr_api success session_id={session_id} "
                    f"{chunk_desc} response={response.text}")
        try:
            response_content = response.content.decode('utf-8')
            response_json = json.loads(response_content)

            # 检查响应中的错误码
            if response_json.get('errcode') == 400005:
                logger.warning(f"call_asr_api ASR service returned 400005 "
                               f"session_id={session_id} {chunk_desc}")
                return {
                    'errcode': 400005,
                    'errmsg': f'音频时长超出ASR服务范围 ({chunk_desc})',
                    'data': None
                }

            return response_json
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            logger.error(f"call_asr_api response decode error "
                         f"session_id={session_id} {chunk_desc} error={str(e)}")
            return {
                'errcode': 500,
                'errmsg': f'ASR服务响应解析失败 ({chunk_desc}): {str(e)}'
            }
    else:
        logger.info(f"call_asr_api failed session_id={session_id} "
                    f"{chunk_desc} code={response.status_code} response={response.text}")
        return {
            'errcode': 500,
            'errmsg': (f"call_asr_api failed ({chunk_desc}) "
                       f"session_id={session_id} "
                       f"code={response.status_code} "
                       f"response={response.text}")
        }


def call_asr_api(session_id, audio_param, audio_data):

    redis_client = RedisClient().get_instance().get_client()
    squirrel_key = build_category_key('session_asr_content', "s{0}",
                                      session_id)
    session_data = redis_client.get(squirrel_key)
    if session_data is None:
        session_data = ""

    # 验证音频数据
    is_valid, error_msg, should_split = validate_audio_data(audio_data, audio_param)
    if not is_valid:
        logger.warning(f"call_asr_api validation failed "
                       f"session_id={session_id} error={error_msg}")
        return {
            'errcode': 400005,
            'errmsg': error_msg,
            'data': None
        }

    # 记录音频信息
    sample_rate = audio_param.get('sampleRate', 16000)
    duration = calculate_audio_duration(audio_data, sample_rate)
    logger.info(f"call_asr_api session_id={session_id} "
                f"audio_size={len(audio_data)} duration={duration:.2f}s "
                f"should_split={should_split}")

    # 如果需要分片处理
    if should_split:
        return process_audio_chunks(session_id, audio_param, audio_data,
                                  redis_client, squirrel_key, session_data)
    else:
        # 单个音频处理
        result = call_single_asr_api(session_id, audio_param, audio_data)
        if result.get('errcode') == 0:
            # 正常处理响应
            data = result.get('data')
            if data and data.get('status') == 3:
                text = data.get('text', '')
                redis_client.set(squirrel_key, session_data + text)

            if result.get('data'):
                full_text = session_data + data.get('text', '')
                result['data']['full_text'] = full_text

        return result


def process_audio_chunks(session_id, audio_param, audio_data,
                        redis_client, squirrel_key, session_data):
    """
    处理音频分片
    :param session_id: 会话ID
    :param audio_param: 音频参数
    :param audio_data: 音频数据
    :param redis_client: Redis客户端
    :param squirrel_key: Redis键
    :param session_data: 会话数据
    :return: 合并后的ASR结果
    """
    sample_rate = audio_param.get('sampleRate', 16000)

    # 分割音频数据
    chunks = split_audio_data(audio_data, sample_rate)

    if not chunks:
        return {
            'errcode': 400005,
            'errmsg': '音频分片失败',
            'data': None
        }

    logger.info(f"call_asr_api session_id={session_id} "
                f"split into {len(chunks)} chunks")

    # 处理每个分片
    all_texts = []
    final_status = 0

    for chunk in chunks:
        chunk_result = call_single_asr_api(session_id, audio_param,
                                         chunk['data'], chunk)

        if chunk_result.get('errcode') != 0:
            # 如果某个分片失败，返回错误
            logger.error(f"call_asr_api chunk_{chunk['index']} failed "
                        f"session_id={session_id} error={chunk_result.get('errmsg')}")
            return chunk_result

        # 提取文本
        data = chunk_result.get('data', {})
        text = data.get('text', '')
        if text:
            all_texts.append(text)

        # 记录最终状态
        if data.get('status') == 3:
            final_status = 3

    # 合并所有文本
    combined_text = ''.join(all_texts)

    # 更新Redis缓存
    if final_status == 3:
        redis_client.set(squirrel_key, session_data + combined_text)

    # 返回合并结果
    return {
        'errcode': 0,
        'errmsg': 'success',
        'data': {
            'text': combined_text,
            'status': final_status,
            'full_text': session_data + combined_text,
            'chunks_count': len(chunks),
            'total_duration': sum(chunk['duration'] for chunk in chunks)
        }
    }
