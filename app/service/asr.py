import hmac
import base64
import hashlib
from email.utils import formatdate
from utils.logger import logger
from utils.squirrel import RedisClient, build_category_key
import requests
import json
app_key = 'kJ08SdZlGBtezAelpdN8fVms+XGOgEzT82RV/uSKoG8='
secret_key = '70882a9640804b60bcb2bb8d39205cc6'
http_verb = 'POST'  # 按实际情况选择
uri = '/asr/v1/long_stream_recognize'  # 按实际情况传值

# ASR音频时长限制配置
ASR_MIN_DURATION = 0.1  # 最小时长100毫秒
ASR_MAX_DURATION = 60.0  # 最大时长60秒
ASR_MAX_SIZE = 10 * 1024 * 1024  # 最大音频数据大小10MB


def generate_signature(verb, uri, date, secret_key):
    string_to_sign = f"{verb} {uri}\n{date}"
    return hmac_sha1(secret_key, string_to_sign)


def hmac_sha1(key, data):
    signature = hmac.new(key.encode(), data.encode(), hashlib.sha1)
    return base64.b64encode(signature.digest()).decode()


def calculate_audio_duration(audio_data, sample_rate, channels=1,
                             sample_width=2):
    """
    计算音频数据的时长（秒）
    :param audio_data: 音频数据（字节格式）
    :param sample_rate: 采样率
    :param channels: 声道数，默认1
    :param sample_width: 采样宽度（字节数），默认2（16位）
    :return: 音频时长（秒）
    """
    if not audio_data:
        return 0

    # 计算样本数
    bytes_per_sample = channels * sample_width
    total_samples = len(audio_data) // bytes_per_sample

    # 计算时长
    duration = total_samples / sample_rate
    return duration


def validate_audio_data(audio_data, audio_param):
    """
    验证音频数据是否符合要求
    :param audio_data: 音频数据
    :param audio_param: 音频参数
    :return: (is_valid, error_message)
    """
    if not audio_data:
        return False, "音频数据为空"

    # 获取音频参数
    sample_rate = audio_param.get('sampleRate', 16000)

    # 计算音频时长
    duration = calculate_audio_duration(audio_data, sample_rate)

    # 检查时长限制
    if duration < ASR_MIN_DURATION:
        return False, (f"音频时长过短: {duration:.2f}秒，"
                       f"最小需要{ASR_MIN_DURATION}秒")

    if duration > ASR_MAX_DURATION:
        return False, (f"音频时长过长: {duration:.2f}秒，"
                       f"最大允许{ASR_MAX_DURATION}秒")

    # 检查音频数据大小
    if len(audio_data) > ASR_MAX_SIZE:
        return False, (f"音频数据过大: {len(audio_data)}字节，"
                       f"最大允许{ASR_MAX_SIZE}字节")

    return True, None


def call_asr_api(session_id, audio_param, audio_data):

    redis_client = RedisClient().get_instance().get_client()
    squirrel_key = build_category_key('session_asr_content', "s{0}",
                                      session_id)
    session_data = redis_client.get(squirrel_key)
    if session_data is None:
        session_data = ""

    # 验证音频数据
    is_valid, error_msg = validate_audio_data(audio_data, audio_param)
    if not is_valid:
        logger.warning(f"call_asr_api validation failed "
                       f"session_id={session_id} error={error_msg}")
        return {
            'errcode': 400005,
            'errmsg': error_msg,
            'data': None
        }

    # 记录音频信息
    sample_rate = audio_param.get('sampleRate', 16000)
    duration = calculate_audio_duration(audio_data, sample_rate)
    logger.info(f"call_asr_api session_id={session_id} "
                f"audio_size={len(audio_data)} duration={duration:.2f}s")

    # 设置请求的URL
    url = 'https://aispeech.sankuai.com/asr/v1/long_stream_recognize'

    # 生成Asr-Params
    asr_params = {
        'audio_format': audio_param['format'],
        'sample_rate': audio_param['sampleRate'],
        'channel_num':  1,
        'text_normalization': 8,
        'index': audio_param['index']
    }
    asr_params_encoded = base64.b64encode(
        json.dumps(asr_params).encode()).decode()
    date = formatdate(timeval=None, localtime=False, usegmt=True)

    # 生成请求头
    auth_signature = generate_signature(http_verb, uri, date, secret_key)
    headers = {
        'Content-Type': 'application/octet-stream',
        'Asr-Params': asr_params_encoded,
        'Authorization': f"AIAUTH-V1 {app_key}:{auth_signature}",
        'Date': date,
        'SessionID': session_id
    }
    logger.info(f"call_asr_api session_id={session_id} "
                f"asr_params={json.dumps(asr_params)}")

    # 发送请求
    try:
        response = requests.post(url, headers=headers, data=audio_data,
                                 timeout=30)
    except requests.exceptions.Timeout:
        logger.error(f"call_asr_api timeout session_id={session_id}")
        return {
            'errcode': 500,
            'errmsg': 'ASR服务请求超时'
        }
    except requests.exceptions.RequestException as e:
        logger.error(f"call_asr_api request error session_id={session_id} "
                     f"error={str(e)}")
        return {
            'errcode': 500,
            'errmsg': f'ASR服务请求失败: {str(e)}'
        }

    # 处理响应
    if response.status_code == 200:
        logger.info(f"call_asr_api success session_id={session_id} "
                    f"response={response.text}")
        try:
            response_content = response.content.decode('utf-8')
            response_json = json.loads(response_content)

            # 检查响应中的错误码
            if response_json.get('errcode') == 400005:
                logger.warning(f"call_asr_api ASR service returned 400005 "
                               f"session_id={session_id}")
                return {
                    'errcode': 400005,
                    'errmsg': '音频时长超出ASR服务范围，请检查音频数据',
                    'data': None
                }

            # 正常处理响应
            data = response_json.get('data')
            if data and data.get('status') == 3:
                text = data.get('text', '')
                redis_client.set(squirrel_key, session_data + text)

            if response_json.get('data'):
                full_text = session_data + data.get('text', '')
                response_json['data']['full_text'] = full_text

            return response_json
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            logger.error(f"call_asr_api response decode error "
                         f"session_id={session_id} error={str(e)}")
            return {
                'errcode': 500,
                'errmsg': f'ASR服务响应解析失败: {str(e)}'
            }
    else:
        logger.info(f"call_asr_api failed session_id={session_id} "
                    f"code={response.status_code} response={response.text}")
        return {
            'errcode': 500,
            'errmsg': (f"call_asr_api failed session_id={session_id} "
                       f"code={response.status_code} "
                       f"response={response.text}")
        }
