from email import message
import time
import json
from service import rag, request_aigc, context, order
from configs import lion_config

from utils.threaded_generator import ThreadedGenerator
from utils.logger import logger, update_trace_id

from utils.utils import timeit
from typing import Union, List, Dict, Optional
from service.COT.function_box import function_calling


# 主程序
@timeit
def main_chat(token: str, history: list[dict['content':str]], user_id: int, tts_session_id: None,
              generator: ThreadedGenerator, trace_id: str, user_feature: str = "", personal_reply: str = "",
              memory_content: str = "", mis_id: str = "", location_query: bool = False, order_count_query: bool = False):
    """主程序入口"""
    try:
        update_trace_id(trace_id)
        start_time = time.time()
        intent = function_calling("Intent", "call", messages=history, user_id=user_id, mis_id=mis_id, memory_content=memory_content)
        intent_time = round(time.time() - start_time, 2)
        logger.info(f"系统 用户{user_id} 意图：{intent}")

        limit = lion_config.AI_ORDER_LIMIT
        user_orders = order.get_user_orders_v2(token, user_id, limit)
        history_time = round(time.time() - start_time - intent_time, 2)
        logger.info(f"历史订单 {user_orders}")
        
        # 传递用户画像和回复规则，以及mis_id
        rag.chat_with_rag(intent, history, user_id, generator, tts_session_id, user_orders,
                         user_feature, personal_reply, memory_content, mis_id, 
                         location_query, order_count_query)

        logger.info(f"intent_time = {intent_time} history_time = {history_time}")

    except Exception as e:
        logger.info(f"系统 用户{user_id}" + f"发生错误: {str(e)}")
        logger.exception(e)
