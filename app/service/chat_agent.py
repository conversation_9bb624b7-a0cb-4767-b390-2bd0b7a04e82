"""
聊天代理模块 - 分析用户聊天内容并更新用户画像

该模块提供了一个聊天代理，可以分析用户的聊天内容，提取有价值的信息，
并基于这些信息更新用户画像。该模块与现有的用户画像系统集成。
"""

from utils.logger import logger
from service.user_portrait import search_user_portrait, upload_to_es, get_mt_userid
from service.ai_client import send_to_ai
from configs import lion_config
import json
import re
from threading import Thread
import asyncio
from datetime import datetime


class ChatAgent:
    """聊天代理类，用于分析用户聊天内容并更新用户画像"""
    
    def __init__(self):
        """初始化聊天代理"""
        self.chat_history_buffer = {}  # 用户ID -> 聊天历史缓冲区
        self.buffer_max_size = 10      # 每个用户的最大缓冲区大小
        self.update_threshold = 5      # 触发画像更新的阈值
        # 用户偏好引导状态存储
        self.food_preference_states = {}  # 用户ID -> 当前食物偏好引导状态
        logger.info("ChatAgent 初始化完成")
    
    def process_chat(self, mis_id, user_message, assistant_message=None, force_update=False):
        """
        处理用户聊天消息并更新用户画像
        
        Args:
            mis_id: 美团内部ID
            user_message: 用户消息
            assistant_message: 助手回复，可选
            force_update: 是否强制更新用户画像，默认为False
            
        Returns:
            bool: 是否成功处理，如果返回的assistant_message不为None，则同时返回生成的助手回复
        """
        try:
            # 获取用户ID
            mt_user_id = get_mt_userid(mis_id)
            if not mt_user_id:
                logger.error(f"无法获取用户ID，mis_id: {mis_id}")
                return False
                
            # 创建用户标识键
            buffer_key = f"{mis_id}_{mt_user_id}"
            
            # 处理食物偏好引导
            generated_response = None
            # 使用AI判断消息是否与食物相关并进行引导
            is_food_related, food_response = self._analyze_food_intent(user_message)
            if is_food_related:
                logger.info(f"AI检测到食物相关话题，用户ID: {mt_user_id}, 消息: {user_message[:30]}...")
                # 如果已经确认与食物相关，使用响应进行引导
                if food_response:
                    generated_response = food_response
                    assistant_message = generated_response
                    logger.info(f"生成食物偏好引导回复: {generated_response[:50]}...")
            
            # 初始化聊天历史缓冲区（如果不存在）
            if buffer_key not in self.chat_history_buffer:
                self.chat_history_buffer[buffer_key] = []
                
            # 添加用户消息到聊天历史
            chat_pair = {
                "user_message": user_message,
                "assistant_message": assistant_message,
                "timestamp": datetime.now().isoformat()
            }
            self.chat_history_buffer[buffer_key].append(chat_pair)
            
            # 控制缓冲区大小
            if len(self.chat_history_buffer[buffer_key]) > self.buffer_max_size:
                self.chat_history_buffer[buffer_key] = self.chat_history_buffer[buffer_key][-self.buffer_max_size:]
                
            # 检查是否需要更新用户画像
            should_update = force_update or len(self.chat_history_buffer[buffer_key]) >= self.update_threshold
            
            if should_update:
                logger.info(f"触发用户画像更新，用户ID: {mt_user_id}, 强制更新: {force_update}")
                # 异步更新用户画像，不阻塞当前流程
                Thread(target=self._update_user_portrait, args=(mis_id, mt_user_id, self.chat_history_buffer[buffer_key])).start()
                # 清空缓冲区
                self.chat_history_buffer[buffer_key] = []
            
            # 返回处理结果和生成的助手回复（如果有）
            if generated_response:
                return True, generated_response
            return True
            
        except Exception as e:
            logger.error(f"处理用户聊天时发生错误: {str(e)}")
            return False
    
    def _analyze_food_intent(self, message):
        """
        使用AI模型分析用户消息是否与食物相关，并生成引导回复
        
        Args:
            message: 用户消息
            
        Returns:
            tuple: (是否与食物相关, 引导回复)
        """
        try:
            # 构建系统提示词
            system_prompt = """你是一个餐饮引导助手，负责检测用户消息是否与食物相关并提供引导回复。
评估用户消息是否表达了以下意图：
1. 想要寻找美食推荐
2. 表达饥饿或想吃东西的意愿
3. 询问餐饮相关的建议
4. 讨论食物偏好、口味或类型

如果确认用户消息与食物相关，请提供一个引导性回复，帮助用户更具体地表达他们的食物偏好。
你应该循序渐进地询问用户，例如先询问想吃中餐还是西餐，然后再询问更具体的菜系或口味等。
"""
            
            # 构建用户提示词
            user_prompt = f"""用户消息: "{message}"

请以JSON格式回答:
{{
    "is_food_related": true/false,  // 用户消息是否与食物相关
    "confidence": 0-100,  // 判断的置信度
    "response": "如果与食物相关，这里提供引导性回复，否则为空字符串"
}}"""
            
            # 准备AI请求
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            data = {
                "model": lion_config.COT_MODEL,  # 使用配置中的模型
                "messages": messages,
                "temperature": 0.3,  # 较低的温度以获得更确定的回答
                "max_tokens": 1000,
            }
            
            # 调用AI服务
            response = send_to_ai(data)
            if not response:
                logger.error(f"AI服务未返回响应，无法分析食物意图")
                # 返回一个友好的默认回复，说明这是一个外卖助手
                default_response = "不好意思，我是一个专注于美食和外卖的助手，可能无法回答与餐饮无关的问题。您是否需要美食推荐或者外卖帮助呢？"
                return True, default_response
                
            # 解析AI响应
            try:
                response_json = response.json()
                content = response_json["choices"][0]["message"]["content"]
                
                # 记录原始内容以便调试
                logger.info(f"AI返回的原始内容: {content[:200]}...")
                
                # 检查内容是否为空
                if not content or content.isspace():
                    logger.warning("AI返回了空内容")
                    default_response = "不好意思，我是一个专注于美食和外卖的助手，可能无法回答与餐饮无关的问题。您是否需要美食推荐或者外卖帮助呢？"
                    return True, default_response
                
                # 尝试解析JSON字符串
                # 有时AI可能返回非标准JSON，尝试清理格式
                content = content.strip()
                if content.startswith('```json'):
                    content = content.split('```json')[1]
                if content.endswith('```'):
                    content = content.split('```')[0]
                content = content.strip()
                
                analysis = json.loads(content)
                
                is_food_related = analysis.get("is_food_related", False)
                confidence = analysis.get("confidence", 0)
                response_text = analysis.get("response", "")
                
                logger.info(f"AI分析结果: 是否食物相关={is_food_related}, 置信度={confidence}%")
                
                # 仅在高置信度时返回原始引导回复
                if is_food_related and confidence >= 70 and response_text:
                    return True, response_text
                
                # 如果不是食物相关，或者没有提供回复，返回一个友好的默认回复
                if not is_food_related:
                    default_response = "不好意思，我是一个专注于美食和外卖的助手，可能无法回答与餐饮无关的问题。您是否需要美食推荐或者外卖帮助呢？"
                    return True, default_response
                
                # 如果是食物相关但没有提供回复，返回一个通用的食物引导回复
                if not response_text:
                    default_food_response = "您似乎对美食感兴趣！请问您想了解什么类型的餐饮呢？例如中餐、西餐、日料，或者有特定口味的偏好吗？"
                    return True, default_food_response
                    
                return is_food_related, response_text
                
            except Exception as e:
                logger.error(f"解析AI食物意图分析响应失败: {str(e)}")
                logger.error(f"导致错误的内容: {response.text[:200] if response else 'None'}")
                # 返回一个友好的默认回复
                default_response = "不好意思，我是一个专注于美食和外卖的助手，可能无法回答与餐饮无关的问题。您是否需要美食推荐或者外卖帮助呢？"
                return True, default_response
                
        except Exception as e:
            logger.error(f"分析用户食物意图时出错: {str(e)}")
            # 返回一个友好的默认回复
            default_response = "不好意思，我是一个专注于美食和外卖的助手，可能无法回答与餐饮无关的问题。您是否需要美食推荐或者外卖帮助呢？"
            return True, default_response
    
    def _update_user_portrait(self, mis_id, mt_user_id, chat_history):
        """
        根据聊天历史更新用户画像
        
        Args:
            mis_id: 美团内部ID
            mt_user_id: 美团用户ID
            chat_history: 聊天历史列表
        """
        try:
            # 从ES获取现有用户画像
            portrait_result = search_user_portrait(mis_id, mt_user_id)
            existing_portrait = ""
            if portrait_result:
                existing_portrait = portrait_result.get("portrait", "")
                logger.info(f"从ES获取到现有用户画像，用户ID: {mt_user_id}")
            
            # 准备聊天历史文本
            chat_text = self._format_chat_history(chat_history)
            
            # 调用AI更新用户画像
            updated_portrait = self._generate_updated_portrait(existing_portrait, chat_text, mis_id, mt_user_id)
            
            if updated_portrait:
                # 上传更新后的画像到ES
                success = upload_to_es(mis_id, mt_user_id, updated_portrait)
                if success:
                    logger.info(f"用户画像更新成功，用户ID: {mt_user_id}")
                else:
                    logger.error(f"用户画像上传失败，用户ID: {mt_user_id}")
            else:
                logger.error(f"生成更新后的用户画像失败，用户ID: {mt_user_id}")
                
        except Exception as e:
            logger.error(f"更新用户画像时发生错误: {str(e)}")
    
    def _format_chat_history(self, chat_history):
        """
        格式化聊天历史为文本
        
        Args:
            chat_history: 聊天历史列表
            
        Returns:
            str: 格式化后的聊天历史文本
        """
        formatted_text = "### 最近的聊天记录:\n\n"
        
        for item in chat_history:
            user_msg = item.get("user_message", "")
            assistant_msg = item.get("assistant_message", "")
            timestamp = item.get("timestamp", "")
            
            formatted_text += f"- 时间: {timestamp}\n"
            formatted_text += f"  用户: {user_msg}\n"
            if assistant_msg:
                formatted_text += f"  助手: {assistant_msg}\n"
            formatted_text += "\n"
            
        return formatted_text
    
    def _generate_updated_portrait(self, existing_portrait, chat_text, mis_id, mt_user_id):
        """
        生成更新后的用户画像
        
        Args:
            existing_portrait: 现有用户画像文本
            chat_text: 格式化的聊天历史文本
            mis_id: 美团内部ID
            mt_user_id: 美团用户ID
            
        Returns:
            str: 更新后的用户画像文本
        """
        try:
            # 准备提示词
            if existing_portrait:
                system_prompt = "你是一位专业的用户画像分析师，请根据现有用户画像和新的聊天记录更新用户画像。"
                user_prompt = f"""请根据以下信息更新用户画像：

### 现有用户画像:
{existing_portrait}

{chat_text}

请根据新的聊天记录更新用户画像，特别关注:
1. 用户提及的新偏好或兴趣
2. 消费意向的变化
3. 情感状态的变化
4. 可能的生活状态变化

只更新有确切证据支持的内容，保留现有画像中其他信息。请保持原有画像的结构格式。"""
            else:
                system_prompt = "你是一位专业的用户画像分析师，请根据用户的聊天记录生成初步的用户画像。"
                user_prompt = f"""请根据以下用户聊天记录生成初步的用户画像:

{chat_text}

请从以下几个方面进行分析:
1. 用户可能的消费特征与偏好
2. 用户可能的性格特点
3. 用户的沟通风格
4. 可能的兴趣爱好
5. 情感状态评估
6. 可能的服务需求

请注意，由于信息有限，画像可能不完整，标明这是基于有限聊天记录的初步分析。"""

            # 调用AI服务
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            data = {
                "model": lion_config.COT_MODEL,  # 使用配置中的模型
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 4000,
            }
            
            # 发送请求到AI服务
            response = send_to_ai(data)
            if not response:
                logger.error(f"AI服务未返回响应，用户ID: {mt_user_id}")
                return None
                
            # 解析AI响应 - Response对象需要先转换为JSON
            try:
                response_json = response.json()
                portrait = response_json["choices"][0]["message"]["content"]
                logger.info(f"成功生成更新后的用户画像，用户ID: {mt_user_id}")
                return portrait
            except Exception as e:
                logger.error(f"解析AI响应时出错: {str(e)}, 响应状态码: {response.status_code}, 响应内容: {response.text[:100]}...")
                return None
            
        except Exception as e:
            logger.error(f"生成更新后的用户画像时发生错误: {str(e)}")
            return None


# 创建聊天代理单例
chat_agent = ChatAgent()


async def process_user_chat(mis_id, user_message, assistant_message=None):
    """
    处理用户聊天的异步包装函数
    
    Args:
        mis_id: 美团内部ID
        user_message: 用户消息
        assistant_message: 助手回复，可选
        
    Returns:
        bool: 是否成功处理
    """
    return chat_agent.process_chat(mis_id, user_message, assistant_message)


async def force_update_portrait(mis_id, user_message=None, assistant_message=None):
    """
    强制更新用户画像的异步函数
    
    Args:
        mis_id: 美团内部ID
        user_message: 用户消息，可选
        assistant_message: 助手回复，可选
        
    Returns:
        bool: 是否成功触发更新
    """
    if user_message:
        return chat_agent.process_chat(mis_id, user_message, assistant_message, force_update=True)
    else:
        # 如果没有提供消息，使用缓冲区中的消息
        mt_user_id = get_mt_userid(mis_id)
        if not mt_user_id:
            logger.error(f"无法获取用户ID，mis_id: {mis_id}")
            return False
            
        buffer_key = f"{mis_id}_{mt_user_id}"
        if buffer_key in chat_agent.chat_history_buffer and chat_agent.chat_history_buffer[buffer_key]:
            Thread(target=chat_agent._update_user_portrait, args=(
                mis_id, 
                mt_user_id, 
                chat_agent.chat_history_buffer[buffer_key]
            )).start()
            chat_agent.chat_history_buffer[buffer_key] = []
            return True
        else:
            logger.warning(f"没有可用的聊天历史进行强制更新，用户ID: {mt_user_id}")
            return False
