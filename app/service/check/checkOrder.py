from datetime import datetime
import json

from utils.logger import logger

from configs.lion_config import lion_client
from service.order import get_order_summary, get_order_details, fetch_order_details
from service.merchant_search_api.merchant_client import get_merchant_address, get_merchant_latitude, get_merchant_longitude, get_merchant_status
from service.ai_client import send_to_ai
def get_recent_order_status(user_id:int, token:str):

    logger.info(f"user_id: {user_id}正在查询订单弹窗")
    today = datetime.now()
    today_timestamp = int(today.timestamp())
    headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/json",
            "X-Requested-With": "XMLHttpRequest",
            "Origin": "http://*************:8080",
            "Referer": "http://*************:8080/",
            "access-token": token,
        }
    # 获取用户最近订单
    # order_list = get_user_order_list(user_id)
    # 获取用户最近订单的订单状态
    num, order_status_list = get_order_summary(token, user_id, today_timestamp - 3600* 100, today_timestamp, limit=20)
    if num == 0:
        return None
    else:
        logger.info(f"user_id: {user_id}查询订单弹窗成功")
        all_orders_info = []
        for order_summary in order_status_list:
            info = {
                "title": order_summary.get("title"),
                "order_id": order_summary.get("order_id"),
                "info": order_summary.get("info"),
                "pic": order_summary.get("pic"),
                "poiId": order_summary.get("poiId"),
            }
            details = fetch_order_details(order_summary.get("order_id"), headers, user_id)
            info["details"] = details
            address = get_merchant_address(order_summary.get("poiId"))
            info["address"] = address
            all_orders_info.append(info)

        SystemPrompt = lion_client.get_value_with_default("weiwei.check_recent_order_prompt", "你是一个经验丰富的营销学专家，现在需要你根据用户最近的外卖订单信息，引导用户对部分订单进行评价。")
        UserPromptTemplate = lion_client.get_value_with_default("weiwei.check_recent_order_user_prompt_template", "用户最近的外卖订单信息如下：{all_orders_info}")
        InputPrompt = UserPromptTemplate.format(all_orders_info=json.dumps(all_orders_info, ensure_ascii=False))
        query = {
            "model":"gpt-4.1",
            "messages":[
                {"role":"system", "content": SystemPrompt},
                {"role":"user", "content": InputPrompt}
            ],
            "stream":False,
            "temperature":0.5,
            "max_tokens":4000,
        }

        response = send_to_ai(query, False)
        content = json.loads(response.text).get("choices", [{}])[0].get("message", {}).get("content", "")
        logger.info(f"获取用户最近订单的评价结果: {content}")

        return content, [t['pic'] for t in all_orders_info]

if __name__ == "__main__":
    
    pass
