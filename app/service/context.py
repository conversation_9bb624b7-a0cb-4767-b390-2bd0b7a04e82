from unittest import result
import urllib.parse
import requests
from typing import Optional
import json
from datetime import datetime
import warnings
from utils.extract import extract_price_with_currency
from utils.logger import logger
from utils.utils import format_order_items
from configs.config import API_WEATHER, ORDER_LIST_URL, ORDER_DETAIL_URL, INGREDIENT_ANALYSIS_RESULTS_DIR, API_WEATHER_HOURLY, API_HOLIDAY
from utils.file_operator import get_merchant_data, get_rising_merchant_data, get_ingredient_data
from langchain.prompts import PromptTemplate
from es import es_client
from configs.lion_config import INDEX_INGREDIENT_ANALYSIS, INDEX_SHOP_FOOD_INFO



# TODO: 现在用户评价功能已经移除，后续可能需要恢复
def select_order_for_review(user_input: str, recent_orders: list, generator) -> Optional[dict]:
    """显示订单列表并获取用户选择
    
    Args:
        recent_orders (list): 最近的订单列表
        
    Returns:
        dict or None: 用户选择的订单
    """
    try:

        # 获取用户选择
        while True:
            try:
                user_choice = user_input
                if not user_choice:
                    return None

                order_index = int(user_choice) - 1
                if 0 <= order_index < len(recent_orders):
                    return recent_orders[order_index]

            except (ValueError, IndexError):
                generator.send(f"请输入有效的订单序号（1-{len(recent_orders)}）")
                generator.close()
                return None

    except Exception as e:
        logger.error("系统" + f"选择订单时出错: {str(e)}")
        return None


# TODO: 现在用户评价功能已经移除，后续可能需要恢复
def provide_order_for_review(recent_orders: list, current_user_id: int, generator) -> None:
    try:
        generator.send("************找到以下最近的订单************")

        # 显示订单列表
        for i, order in enumerate(recent_orders, 1):
            items_str = format_order_items(order.get('items', []))
            generator.send(
                f"{i}. {{'时间':{order.get('o_time', '未知时间')}, '商家名称':{order.get('poi_name', '未知商家')}, '物品信息':{items_str}}} \n\n")

        generator.send(f"请选择要评价的订单序号（输入1-{len(recent_orders)}）：")
        generator.close()
    except Exception as e:
        logger.error("系统" + f"review的提供订单列表时出错: {str(e)}")
        return None


# TODO: 现在用户评价功能已经移除，后续可能需要恢复
def provide_final_infomation_for_review(order: dict, generator):
    try:
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        generator.send(f"\n[{current_time}]：请对「{order.get('poi_name')}」的这份订单发表您的评价：")
        generator.close()
        return

    except (EOFError, KeyboardInterrupt):
        logger.error("\n取消评价...")
        return
    except Exception as e:
        logger.error(f"获取评价内容时出错: {str(e)}")
        return


# 在rag过程中，对店铺名称生成美团连接
def generate_meituan_link(link_type:str, **kwargs)->Optional[str]:
    """生成美团搜索链接"""
    try:
        # 对搜索词进行URL编码
        if link_type == "meituan_app_add_order_link": # 场景：美团手机app  功能：自动添加到购物车
            meituan_app_add_order_link = "imeituan://www.meituan.com/takeout/foods?poi_id={poi_id}&spu_id={spu_id}&add_to_shopcart=1"
            return meituan_app_add_order_link.format(poi_id=kwargs["poi_id"], spu_id=kwargs["spu_id"])
        elif link_type == "meituan_app_orderagain_link": # 场景：美团手机app  功能：再来一单
            meituan_app_orderagain_link = "imeituan://www.meituan.com/orderlist/waimai/orderagain?orderid={order_id}&user_id={user_id}"
            return meituan_app_orderagain_link.format(order_id=kwargs["order_id"], user_id=kwargs["user_id"])
        elif link_type == "meituan_app_waimai_merchant_link": # 场景：美团手机app  功能：进入商户菜单
            meituan_app_waimai_merchant_link = "meituanwaimai://waimai.meituan.com/menu?restaurant_id={poi_id}"
            return meituan_app_waimai_merchant_link.format(poi_id=kwargs["poi_id"])
        elif link_type == "meituan_app_search_link": # 场景：美团手机app  功能：搜索商户
            meituan_app_search_link = "imeituan://www.meituan.com/search/result?entrance=3&q={keyword}"
            encoded_keyword = urllib.parse.quote(kwargs["keyword"])
            return meituan_app_search_link.format(keyword=encoded_keyword)
        elif link_type == "meituan_h5_search_link": # 场景：美团h5  功能：搜索商户
            meituan_h5_search = "https://h5.waimai.meituan.com/waimai/mindex/searchresults?queryType=12002&keyword={keyword}&entranceId=0&qwTypeId=0&mode=search"
            encoded_keyword = urllib.parse.quote(kwargs["keyword"])
            return meituan_h5_search.format(keyword=encoded_keyword)
        else:
            return None
    except Exception as e:
        logger.error(f"生成美团链接出错: {str(e)}")
        return None


def generate_food_img_url(token:str, poi_id:str, food_id:str)->str:
    try:
        headers = {
                "Authorization": "Bearer 1838824241643597850",
                "Content-Type": "application/json",
                "access-token": token
            }
        URL = "https://xiaomeiai.meituan.com/web/v1/order/getSkuInfoBySkuIds"
        logger.info(f"获取食物图片的请求参数为: {poi_id, food_id}")
        payload = {
            "key": poi_id,
            "skuList":[food_id]
        }
        response = requests.post(URL, headers=headers, json=payload)
        if response.status_code != 200:
            logger.error(f"获取食物图片出错: {response.text}")
            return None
        data = json.loads(response.text)
        logger.info(f"获取食物图片成功: {data}")
        return data.get('data', {})[0].get('picture', None)
    except Exception as e:
        logger.error(f"获取食物图片出错: {str(e)}")
        return None

def check_merchant(merchant_name:str|None)->dict|None:
    if merchant_name == None:
        return None
    
    # 先精确查询商户名称
    match_queries = [
        {"term": {"poi_name_keyword": merchant_name}}
    ]
    bool_query = {
        "query": {
            "bool": {
                "should": match_queries, 
                "minimum_should_match": 1
            }
        },
        "size": 1
    }
    search_result = es_client.search(INDEX_SHOP_FOOD_INFO, bool_query)
    hits = search_result['hits']['hits']
    if len(hits) == 0:
        match_queries = [
            {"match": {"poi_name_text": merchant_name}}
        ]
        bool_query = {
            "query": {
                "bool": {
                    "should": match_queries, 
                    "minimum_should_match": 1
                }
            },
            "size": 1
        }
        search_result = es_client.search(INDEX_SHOP_FOOD_INFO, bool_query)
        hits = search_result['hits']['hits']
    
    if len(hits) == 0: # 已经给过你两次机会了，现在“对不起，我是警察”
        return None
    
    result:dict = [hit['_source'] for hit in hits][0]
    if isinstance(result, dict):
        return result
    else:
        logger.info(f'ES数据库中 merchant返回值类型错误 {result}, 应该是int，但是返回了{type(result)}')
        return None

def check_food_with_merchant_id(food_name:str, merchant_id:int)->dict|None:
    # 先精确查询商户名称
    match_queries = [
        {"term": {"poi_id": merchant_id}},
        {"term": {"food_name_keywords": food_name.strip()}}
    ]
    bool_query = {
        "query": {
            "bool": {
                "must": match_queries, 
            }
        },
        "size": 1
    }
    search_result = es_client.search(INDEX_SHOP_FOOD_INFO, bool_query)
    hits = search_result['hits']['hits']
    if len(hits) == 0:
        match_queries = [
            {"term": {"poi_id": merchant_id}},
            {"match": {"food_name_text": food_name}}
        ]
        bool_query = {
            "query": {
                "bool": {
                    "must": match_queries, 
                }
            },
            "size": 1
        }
        search_result = es_client.search(INDEX_SHOP_FOOD_INFO, bool_query)
        hits = search_result['hits']['hits']
        
    if len(hits) == 0: # 已经给过你两次机会了，现在“对不起，我是警察”
        return None
    
    result:dict = [hit['_source'] for hit in hits][0]
    if isinstance(result, dict):
        search_food_name = result["food_name_keyword"]
        # 计算search_food_name与food_name中，有多少相同的字符
        same_chars_count = 0
        for char in search_food_name:
            if char in food_name:
                same_chars_count += 1
        if same_chars_count > len(food_name) * 0.5:
            return result
        else:
            return None
    else:
        logger.info(f'ES数据库中 merchant返回值类型错误 {result}, 应该是dict，但是返回了{type(result)}')
        return None


def check_food_with_merchant_name(food_name:str, merchant_name:str)->dict|None:
    # 先精确查询商户名称
    match_queries = [
        {"match": {"poi_name_text": merchant_name}},
        {"term": {"food_name_keyword": food_name.strip()}}
    ]
    bool_query = {
        "query": {
            "bool": {
                "must": match_queries, 
            }
        },
        "size": 1
    }
    # logger.info("ES Search Query: {}".format(bool_query))
    search_result = es_client.search(INDEX_SHOP_FOOD_INFO, bool_query)
    hits = search_result['hits']['hits']
    # logger.info("his first: {}".format(hits))
    if len(hits) == 0:
        match_queries = [
            {"match": {"poi_name_text": merchant_name}},
            {"match": {"food_name_text": food_name}}
        ]
        bool_query = {
            "query": {
                "bool": {
                    "must": match_queries, 
                }
            },
            "size": 1
        }
        search_result = es_client.search(INDEX_SHOP_FOOD_INFO, bool_query)
        hits = search_result['hits']['hits']
        
    if len(hits) == 0: # 已经给过你两次机会了，现在“对不起，我是警察”
        return None
    
    result:dict = [hit['_source'] for hit in hits][0]
    if isinstance(result, dict):
        search_food_name = result["food_name_keyword"]
        # 计算search_food_name与food_name中，有多少相同的字符
        same_chars_count = 0
        for char in search_food_name:
            if char in food_name:
                same_chars_count += 1
        if same_chars_count > len(food_name) * 0.5:
            return result
        else:
            logger.info(f"搜索的菜品名 [{search_food_name}] 与输入的菜品名 [{food_name}] 相同的字符少于 {len(food_name) * 0.5}, 不符合要求")
            return None
    else:
        logger.info(f'ES数据库中 merchant返回值类型错误 {result}, 应该是dict，但是返回了{type(result)}')
        return None
    

def get_time_info() -> str:
    # 获取当前时间信息
    current_time = datetime.now()
    current_hour = current_time.hour
    
    # 获取星期几
    weekday_map = {
        0: "星期一",
        1: "星期二",
        2: "星期三",
        3: "星期四",
        4: "星期五",
        5: "星期六",
        6: "星期日"
    }
    weekday = weekday_map[current_time.weekday()]
    
    # 获取节假日信息
    from service.holiday import holiday_thrift
    holiday_info = holiday_thrift.get_holiday_info(int(current_time.timestamp() * 1000))
    
    # 工作日状态
    workday_status = "工作日" if holiday_info.workDayStatus == 1 else "休息日"
    
    # 假期类型
    holiday_type_map = {
        0: "非假日",
        1: "法定假日",
        2: "调休",
        3: "周末"
    }
    holiday_type = holiday_type_map.get(holiday_info.holidayType, "未知")
    
    # 法定节假日类型
    official_holiday_types = []
    official_type_map = {
        1: "元旦",
        2: "春节",
        3: "清明节",
        4: "劳动节",
        5: "端午节",
        6: "中秋节",
        7: "国庆节"
    }
    for type_id in holiday_info.officialHolidayTypes:
        if type_id in official_type_map:
            official_holiday_types.append(official_type_map[type_id])
    official_holidays = "、".join(official_holiday_types) if official_holiday_types else "无"
    
    # 公历节日
    solar_festival_map = {
        1: "元旦", 2: "情人节", 3: "妇女节", 4: "植树节", 
        5: "愚人节", 6: "劳动节", 7: "青年节", 8: "母亲节",
        9: "儿童节", 10: "父亲节", 11: "建党节", 12: "建军节",
        13: "教师节", 14: "国庆节", 15: "平安夜", 16: "圣诞节"
    }
    solar_festival = solar_festival_map.get(holiday_info.solarFestivalType, "无")
    
    # 农历节日
    lunar_festival_map = {
        1: "腊八", 2: "除夕", 3: "春节", 4: "元宵",
        5: "清明", 6: "端午", 7: "七夕", 8: "中元",
        9: "中秋", 10: "重阳", 11: "冬至"
    }
    lunar_festival = lunar_festival_map.get(holiday_info.lunarFestivalType, "无")
    
    # 根据时间段判断
    time_period = ""
    if 5 <= current_hour < 10:
        time_period = "早餐"
    elif 10 <= current_hour < 14:
        time_period = "午餐"
    elif 14 <= current_hour < 17:
        time_period = "下午茶"
    elif 17 <= current_hour < 21:
        time_period = "晚餐"
    else:
        time_period = "夜宵"
    
    # 构建时间信息字符串
    time_info = f"\n现在是{current_time.strftime('%Y年%m月%d日 %H:%M')}，{weekday}"
    time_info += f"\n工作日状态：{workday_status}"
    time_info += f"\n假期类型：{holiday_type}"
    time_info += f"\n法定节假日：{official_holidays}"
    time_info += f"\n公历节日：{solar_festival}"
    time_info += f"\n农历节日：{lunar_festival}"
    if holiday_info.dateDesc:
        time_info += f"\n节日描述：{holiday_info.dateDesc}"
    time_info += f"\n现在正是{time_period}时间。\n"
    
    return time_info

def get_time_summary() -> str:
    current_time = datetime.now()
    current_hour = current_time.hour
    # 获取星期几
    weekday_map = {
        0: "星期一",
        1: "星期二",
        2: "星期三",
        3: "星期四",
        4: "星期五",
        5: "星期六",
        6: "星期日"
    }
    weekday = weekday_map[current_time.weekday()]
    
    # 获取节假日信息
    from service.holiday import holiday_thrift
    holiday_info = holiday_thrift.get_holiday_info(int(current_time.timestamp() * 1000))
    
    # 工作日状态
    workday_status = "工作日" if holiday_info.workDayStatus == 1 else "休息日"
    
    # 假期类型
    holiday_type_map = {
        0: "非假日",
        1: "法定假日",
        2: "调休",
        3: "周末"
    }
    holiday_type = holiday_type_map.get(holiday_info.holidayType, "未知")

    time_info = f"现在是{current_time.strftime('%Y年%m月%d日 %H:%M')}，{weekday}"
    time_info += f"\n工作日状态：{workday_status}"
    time_info += f"\n假期类型：{holiday_type}"
    return time_info
    

# 在chat_with_ai_v2中调用
def get_weather_prompt():
    """获取实时天气信息和未来天气预报"""
    try:
        # 获取实时天气
        url = API_WEATHER
        logger.info(f"请求实时天气API: {url}")
        response = requests.get(url, timeout=5)

        if response.status_code == 200:
            logger.info(f"实时天气API响应状态码: {response.status_code}")
            logger.info(f"实时天气API响应内容: {response.text}")
            data = response.json()
            if (data.get("responseStatus", {}).get("code") == 0
                    and "realTimeWeather" in data
                    and "realTimeCondition" in data["realTimeWeather"]
                ):
                    weather_data = data["realTimeWeather"]["realTimeCondition"]
                    logger.info(f"原文获取到的天气数据: {json.dumps(weather_data, ensure_ascii=False, indent=2)}")
                    result = {
                        # 基本天气状况
                        "weather": weather_data["conditionItem"]["iconDesc"],
                        "temperature": weather_data["conditionItem"]["temperature"],
                        "feel_temperature": weather_data["apparentTemperature"],
                        "humidity": weather_data["humidity"],
                        "pressure": weather_data["pressure"],
                        
                        # 风况
                        "wind_level": weather_data["conditionItem"]["windLevel"],
                        "wind_speed": weather_data["conditionItem"]["windSpeed"],
                        "wind_direction": weather_data["conditionItem"]["windDirectionDesc"],
                        
                        # 降水情况
                        "precipitation": weather_data["rainFall"],
                        "rain_intensity": weather_data["rainFallIntensity"],
                        "rain_level": weather_data["rainLevel"],
                        "rain_type": weather_data["rainSnowType"],
                        
                        # 日照相关
                        "uv_index": weather_data["uvi"],
                        "sunrise": datetime.strptime(weather_data["sunRise"], "%Y-%m-%d %H:%M:%S").strftime("%H:%M"),
                        "sunset": datetime.strptime(weather_data["sunSet"], "%Y-%m-%d %H:%M:%S").strftime("%H:%M"),
                        # 其他信息
                        "update_time": weather_data["updateTime"]
                    }
                    
                    # 获取未来天气预报
                    future_url = f"{API_WEATHER_HOURLY}?adcode=110105&hours=1"
                    logger.info(f"请求未来天气预报API: {future_url}")
                    future_response = requests.get(future_url, timeout=5)
                    logger.info(f"未来天气预报API响应状态码: {future_response.status_code}")
                    logger.info(f"未来天气预报API响应内容: {future_response.text}")
                    
                    future_weather = None
                    if future_response.status_code == 200:
                        future_data = future_response.json()
                        logger.info(f"未来天气预报原始响应: {json.dumps(future_data, ensure_ascii=False, indent=2)}")
                        if (future_data.get("responseStatus", {}).get("code") == 0 and 
                            future_data.get("hourlyWeather", {}).get("hourlyConditionList")):
                            weather_item = future_data["hourlyWeather"]["hourlyConditionList"][0]
                            future_weather = {
                                "weather": weather_item["conditionItem"]["iconDesc"],
                                "temperature": weather_item["conditionItem"]["temperature"],
                                "feel_temperature": weather_item["apparentTemperature"],
                                "humidity": weather_item["humidity"],
                                "wind_level": weather_item["conditionItem"]["windLevel"],
                                "wind_direction": weather_item["conditionItem"]["windDirectionDesc"],
                                "rain_probability": weather_item["rainPercent"],
                                "precipitation": weather_item["qpf"],
                                "update_time": weather_item["updateTime"]
                            }
                            logger.info(f"获取到的未来天气数据: {json.dumps(future_weather, ensure_ascii=False, indent=2)}")
                        else:
                            logger.warning(f"未来天气预报数据格式不正确: {json.dumps(future_data, ensure_ascii=False, indent=2)}")
                    else:
                        logger.error(f"获取未来天气预报失败，响应内容: {future_response.text}")
                    
                    logger.info(f"获取到的天气数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    weather_prompt = ""
                    if result:
                        current_time = datetime.now().strftime("%H:%M")
                        weather_prompt = f"""现在是{current_time}，
当前天气{result['weather']}，气温{result['temperature']}℃，体感温度{result['feel_temperature']}℃，
湿度{result['humidity']}%，气压{result['pressure']}百帕。
{result['wind_direction']}，风力{result['wind_level']}级，风速{result['wind_speed']}米/秒。
降水量{result['precipitation']}毫米，降水强度{result['rain_intensity']}，降水等级{result['rain_level']}。
紫外线指数{result['uv_index']}，日出时间{result['sunrise']}，日落时间{result['sunset']}。"""

                        if future_weather:
                            weather_prompt += f"""

未来半小时天气预计{future_weather['weather']}，气温{future_weather['temperature']}℃，体感温度{future_weather['feel_temperature']}℃，
湿度{future_weather['humidity']}%，{future_weather['wind_direction']}，风力{future_weather['wind_level']}级，
降水概率{future_weather['rain_probability']}%，预计降水量{future_weather['precipitation']}毫米。"""

                    logger.info(f"构建的天气提示词: {weather_prompt}")
                    return weather_prompt
                    
            logger.warning("API响应中缺少必要的天气信息字段")
            return None
                    
    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"获取天气信息失败: {str(e)}")
        logger.error(f"异常类型: {type(e)}")
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        return None
    
def get_weather_summary():
    """获取实时天气信息和未来天气预报"""
    try:
        # 获取实时天气
        url = API_WEATHER
        logger.info(f"请求实时天气API: {url}")
        response = requests.get(url, timeout=5)

        if response.status_code == 200:
            logger.info(f"实时天气API响应状态码: {response.status_code}")
            logger.info(f"实时天气API响应内容: {response.text}")
            data = response.json()
            if (data.get("responseStatus", {}).get("code") == 0
                    and "realTimeWeather" in data
                    and "realTimeCondition" in data["realTimeWeather"]
                ):
                    weather_data = data["realTimeWeather"]["realTimeCondition"]
                    logger.info(f"原文获取到的天气数据: {json.dumps(weather_data, ensure_ascii=False, indent=2)}")
                    result = {
                        # 基本天气状况
                        "weather": weather_data["conditionItem"]["iconDesc"],
                        "temperature": weather_data["conditionItem"]["temperature"]
                    }
                    weather_prompt = ""
                    if result:
                        current_time = datetime.now().strftime("%H:%M")
                        weather_prompt = f"""现在是{current_time}，当前天气{result['weather']}，气温{result['temperature']}℃，"""

                        

                    logger.info(f"构建的天气提示词: {weather_prompt}")
                    return weather_prompt
            
            logger.warning("API响应中缺少必要的天气信息字段")
            return None
    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"获取天气信息失败: {str(e)}")
        logger.error(f"异常类型: {type(e)}")
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        return None

def generate_prompt(template: str, **kwargs) -> str:
    """
    使用给定的模板和键值对生成提示。

    Args:
        template (str): Prompt 模板字符串。
        **kwargs: 模板中使用的键值对。

    Returns:
        str: 格式化后的提示字符串。
    """
    prompt_template = PromptTemplate.from_template(template)
    return prompt_template.format(**kwargs)


def generate_merchant_prompt() -> str:
    merchant_data = get_merchant_data()
    rising_merchant_data = get_rising_merchant_data()
    merchant_info = "\n最近表现稳定的热门商家信息：\n"
    for merchant in merchant_data:
        merchant_info += f"- {merchant['comment']}\n"

    if rising_merchant_data:
        merchant_info += "\n最近表现突出的商家：\n"
        for merchant in rising_merchant_data:
            if 'comment' in merchant:
                merchant_info += f"- {merchant['comment']}\n"
    return merchant_info


def generate_ingredient_prompt(user_input: str) -> str:
    ingredient_data = get_ingredient_data(user_input, INGREDIENT_ANALYSIS_RESULTS_DIR)
    ingredient_info = ingredient_data if ingredient_data else ""
    return ingredient_info

warnings.filterwarnings('ignore', message='Unverified HTTPS request')

if __name__ == '__main__':
    url = generate_food_img_url(token="eAGF0EsrhFEYwPFOI4mNZmXnXVgwZeac55z3vM9rZYzb0q2Ujc7leVPKqCEbC6MsWNqIyCVlochGJllZzMJ-ErLDLHwCFi4La1_g369_E2tdO9hvCe4_q0-PAM0CfMTBGA09gUWrvfJCcg1KKm9kQhkQ0nOnIqtV3ydLd02SHXc0R8sDGvJSaIEFPRjGiPk4zA-iEhCHyAvQH1Tv9j6eoZPBv2H8JfWmhi-vK5UHGLn52r27hVWWaW4cHSsUPaXTL8cn9YvD1_XTt4Ny_ey4fl5uawhWrra7Ot-_tmo12GRNf7AdlrXCeZ5Yq3SC2sexthxjggS0RyDDp0Uk9Q8UlUaujljHEtlltERGhzFZAiUQUTpp3e8DEOQjMRU467wzJlQ8g8o4GUfaOouAFCU_H_Qqa88VzeLCDGR5bn7WUc44R6VS90JxluayQwMTGyxVKhW_AcZ1gFg**eAEFwQkBACAIBLBKgKBHHN7-EdxgOckjbBh2IKB9g432PDwnHt3YOl6g9Li1prFS3WPcsh84OhIi**k0LIFTW462wdVPdndBdOmyNtTcCs0N1b8uU3V-Txd07OxoQ0JLF1pMaK0exRYSVp8NdP7RscvdSFfEW4ep--6A**MjM1ODA2Nzcsc3VuaGFpeXVlLOWtmea1t-WysyxzdW5oYWl5dWVAbWVpdHVhbi5jb20sMSwzNDE4MTk3MSwxNzQyNjA4NjYxOTc2",
     poi_id="25686173", food_id="28408684843")
    print(url)