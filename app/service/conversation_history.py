import json
from datetime import datetime
from utils.logger import logger
from es import es_client
from configs import lion_config

def upload_conversation_history(conversation_id, new_message, api_name, user_id=None):
    """
    上传对话历史到Elasticsearch数据库
    
    Args:
        conversation_id (str): 对话的唯一标识符
        new_message (dict): 需要保存的新消息内容，包含消息ID、思考内容、主要内容和时间成本等
        api_name (str): 调用的API名称，如"rawchat"、"agentic","centralagent"或"dianping"等
        user_id (str, optional): 用户的唯一标识。默认为None
        
    Returns:
        bool: 上传成功返回True，失败返回False
    """
    try:
        index_name = "compare_history"

        # 查询是否存在该对话记录
        query = {
            "query": {
                "term": {
                    "_id": conversation_id
                }
            }
        }
        
        logger.info(f"查询对话ID: {conversation_id}")
        search_result = es_client.search(index_name, query)
        if search_result:
            history = search_result['hits']['hits']
            if history:
                # 如果已经存在记录，则更新
                history = history[0]['_source']
                if api_name in history:
                    history[api_name].append(new_message)
                else:
                    history[api_name] = [new_message]
                if user_id and "user_id" not in history:
                    history["user_id"] = user_id
                
                # 更新ES文档
                upsert_response = es_client.upsert_data(index_name, conversation_id, {"doc": history, "doc_as_upsert": True})
                if upsert_response:
                    logger.info(f"更新对话历史成功，对话ID: {conversation_id}")
                    return True
                else:
                    logger.error(f"更新对话历史失败，对话ID: {conversation_id}")
                    return False
            else:
                # 如果不存在记录，则创建新记录
                record = {
                    api_name: [new_message]
                }
                if user_id:
                    record["user_id"] = user_id
                
                # 创建新ES文档
                create_response = es_client.upsert_data(index_name, conversation_id, {"doc": record, "doc_as_upsert": True})
                if create_response:
                    logger.info(f"创建对话历史成功，对话ID: {conversation_id}")
                    return True
                else:
                    logger.error(f"创建对话历史失败，对话ID: {conversation_id}")
                    return False
        else:
            # ES查询出错
            logger.error(f"ES查询失败，对话ID: {conversation_id}")
            return False
    except Exception as e:
        logger.error(f"上传对话历史到ES失败，对话ID: {conversation_id}，错误: {e}")
        return False

def upload_used_ids(user_id):
    """
    记录使用过系统的用户ID
    
    Args:
        user_id (str): 用户ID
        
    Returns:
        bool: 上传成功返回True，失败返回False
    """
    try:
        mis_id_index = lion_config.lion_client.get_value_with_default("mis_id_index", "id_used_weiwei_compare")
        query = {
            "query": {
                "term": {
                    "_id": 1
                }
            }
        }
        search_result = es_client.search(mis_id_index, query)
        if search_result:
            history = search_result['hits']['hits']
            if history:
                history = history[0]['_source']
                if user_id not in history["mis_id"]:
                    history["mis_id"].append(user_id)
                    id_response = es_client.upsert_data(mis_id_index, 1, {"doc": history, "doc_as_upsert": True})
                    if id_response:
                        logger.info(f"用户ID: {user_id} 添加到使用者名单成功")
                        return True
                    else:
                        logger.error(f"用户ID: {user_id} 添加到使用者名单失败")
                        return False
                else:
                    logger.info(f"用户ID: {user_id} 已存在")
                    return True
            else:
                history = {"mis_id": [user_id]}
                id_response = es_client.upsert_data(mis_id_index, 1, {"doc": history, "doc_as_upsert": True})
                if id_response:
                    logger.info(f"用户ID: {user_id} 添加到使用者名单成功")
                    return True
                else:
                    logger.error(f"用户ID: {user_id} 添加到使用者名单失败")
                    return False
        else:
            logger.error(f"查询使用者名单失败")
            return False
    except Exception as e:
        logger.error(f"上传mis_id到ES失败，用户ID: {user_id}，错误: {e}")
        return False 