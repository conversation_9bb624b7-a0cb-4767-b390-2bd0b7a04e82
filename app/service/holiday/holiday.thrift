namespace java com.sankuai.meituan.holiday.thrift.iface


enum WorkDayStatus {
  WORKDAY=1, DAY_OFF=2
}

enum HolidayType {
  NOT_HOLIDAY=0, OFFICIAL_HOLIDAY=1, OFFICIAL_DAY_OFF=2, WEEKEND=3
}

enum OfficialHolidayType {
  NOT_OFFICIAL_HOLIDAY=0, NEW_YEAR=1, SPRING_DAY=2, QINGMING=3, MAY_DAY=4, DRAGON_BOAT=5, MID_AUTUMN=6, NATIONAL_DAY=7
}

enum SolarFestivalType {
  NOT_FESTIVAL=0, NEW_YEAR=1, VALENTINE=2, WOMEN_DAY=3, ARBOR_DAY=4, APRIL_FOOL_DAY=5, MAY_DAY=6, YOUTH_DAY=7, MOTHER_DAY=8, CHILDREN_DAY=9, FATHER_DAY=10, CPC_FOUNDING_DAY=11, ARMY_DAY=12, TEACHERS_DAY=13, NATIONAL_DAY=14, CHRISTMAS_EVE=15, CHRISTMAS=16, MAO_BIRTHDAY=17
}

enum LunarFestivalType {
  NOT_FESTIVAL=0, LABA=1, NEW_YEAR_EVE=2, NEW_YEAR=3, LANTERN=4, QINGMING=5, DRAGON_BOAT=6, QIXI=7, ZHONGYUAN=8, MID_AUTUMN=9, CHUNG_YEUNG=10, DONGZHI=11
}

exception HolidayException {
  1:  string code;
  2:  string message;
}


// 日历服务返回值结构体
struct MtHoliday {
  1:  i64 day;
  2:  i32 year;
  3:  WorkDayStatus workDayStatus;
  4:  bool salaryDayStatus;
  5:  HolidayType holidayType;
  6:  list<OfficialHolidayType> officialHolidayTypes;
  7:  SolarFestivalType solarFestivalType;
  8:  LunarFestivalType lunarFestivalType;
  9:  string dateDesc;
}


// 日历服务 (根据国家节假日政策，结合司内考勤政策安排，提供节假日、工休日查询，及逻辑计算) (使用场景: 国家法定节假日查询、班休状态查询、公历节日查询、农历节日查询、发薪日查询等)
service MtHolidayService {

  // 获取所有农历节日 (获取某年所有农历节日)
  list<MtHoliday> getAllLunarFestivalDays(1:  i32 arg0) throws (1: HolidayException ex1);

  // 获取所有法定假日，按key-value方式返回，key = 假期类型，value = 假期日期集合
  map<OfficialHolidayType, list<MtHoliday>> getAllOfficialHolidays(1:  i32 arg0) throws (1: HolidayException ex1);

  // 获取所有的公历节日 (获取某年所有公历节日)
  list<MtHoliday> getAllSolarFestivalDays(1:  i32 arg0) throws (1: HolidayException ex1);

  // 日历状态查询 (查询某一天的日历状态)
  MtHoliday getMtHoliday(1:  i64 arg0) throws (1: HolidayException ex1);

  // 月份日历状态 (查询某个月的日历状态)
  list<MtHoliday> getMtHolidayListOfMonth(1:  i32 arg0, 2:  i32 arg1) throws (1: HolidayException ex1);

  // 区间日历状态查询 (查询某段时间内的日历状态)
  list<MtHoliday> getMtHolidayListOfRange(1:  i64 arg0, 2:  i64 arg1) throws (1: HolidayException ex1);

  // 年份日历状态 (查询某一年的日历状态)
  list<MtHoliday> getMtHolidayListOfYear(1:  i32 arg0) throws (1: HolidayException ex1);

  // 获取某人某段时间内日历状态
  list<MtHoliday> getMtHolidayListWithUserOfMonth(1:  string arg0, 2:  i32 arg1, 3:  i32 arg2) throws (1: HolidayException ex1);

  // 获取某人某段时间内日历状态
  list<MtHoliday> getMtHolidayListWithUserOfRange(1:  string arg0, 2:  i64 arg1, 3:  i64 arg2) throws (1: HolidayException ex1);

  // 某月发薪日查询 (查询某个月的发薪日期)
  i64 getSalaryDay(1:  i32 arg0, 2:  i32 arg1) throws (1: HolidayException ex1);

  // 某天前/后工作日查询 (查询某天前/后第几个工作日：sort = 0，当前天；sort < 0，当前天之前第|sort|个工作日；sort > 0，当前天之后第|sort|个工作日。|sort| <= 365)
  MtHoliday getTargetWorkDay(1:  i64 arg0, 2:  i32 arg1) throws (1: HolidayException ex1);

  // 某月的第几个工作日查询 (查询某月的第几个工作日：sort = 0，月份第一天；sort < 0，该月倒数第|sort|个工作日；sort > 0，该月第|sort个|工作日。|sort| <= year.month.value)
  MtHoliday getTargetWorkDayOfMonth(1:  i32 arg0, 2:  i32 arg1, 3:  i32 arg2) throws (1: HolidayException ex1);

  // 某天班/休状态查询 (查询某天是工作日还是休息日)
  bool isWorkDay(1:  i64 arg0) throws (1: HolidayException ex1);

  // 某员工某天是否为工作日 (查询某员工某天工作日状态（由于有特殊班次的员工，所以要按人员搜索）)
  bool isWorkDayWithUser(1:  string arg0, 2:  i64 arg1) throws (1: HolidayException ex1);
}
