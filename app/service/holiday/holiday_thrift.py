from octo_rpc import NonMeshClient, load
import os
import datetime
from configs.config import APP_KEY

current_dir = os.path.dirname(os.path.abspath(__file__))
holiday = load(os.path.join(current_dir, "holiday.thrift"))

client = NonMeshClient(
    service=holiday.MtHolidayService,
    service_name="com.sankuai.meituan.holiday.thrift.iface.MtHolidayService",
    appkey=APP_KEY,  # 客户端的appkey
    remote_appkey="com.sankuai.corehr.holiday",  # 服务端的appkey
)


def get_holiday_info(date: int) -> holiday.MtHoliday:
    return client.getMtHoliday(date)


def get_range_holiday(start_date: int, end_date: int) -> list[holiday.MtHoliday]:
    return client.getMtHolidayListOfRange(start_date, end_date)


if __name__ == "__main__":
    print(get_holiday_info(int(datetime.datetime(2025, 5, 1).timestamp() * 1000)))
    """
    各个字段参考thrift定义
    MtHoliday(day=1746028800000, year=2025, workDayStatus=2, salaryDayStatus=False, 
    holidayType=1, officialHolidayTypes=[4], solarFestivalType=6, lunarFestivalType=0, dateDesc='劳动节')
    """
    print(get_range_holiday(
        int(datetime.datetime(2025, 5, 1).timestamp() * 1000),
        int(datetime.datetime(2025, 5, 5).timestamp() * 1000)
    ))