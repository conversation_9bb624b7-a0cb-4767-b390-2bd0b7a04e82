from datetime import datetime, timedelta
import math
import logging
from es import es_client
from typing import List, Dict

from service.share_to_plaza import (
    get_all_share_ids,
    get_plaza_shares_by_ids,
    PLAZA_INDEX
)
from web import response

logger = logging.getLogger(__name__)

# 配置常量
WEIGHTS = {
    "like": 1.0,     # 点赞权重
    "dislike": -0.5, # 点踩权重
    "comment": 2.0   # 评论权重
}
TIME_THRESHOLD_DAYS = 3  # 3天内不衰减
TOP_M = 500  # 返回前500条热门帖子

def calculate_hot_score(share: Dict) -> float:
    """计算帖子的热度分数
    
    Args:
        share: 帖子信息
        
    Returns:
        float: 热度分数
    """
    try:
        # 1. 计算互动分数
        like_count = len(share.get('liked_users', []))
        dislike_count = len(share.get('disliked_users', []))
        comment_count = len([c for c in share.get('comment_record', []) 
                           if not c.get('is_deleted', False)])
        
        # 计算点赞点踩分数
        like_score = math.log(like_count * WEIGHTS['like'] + 1)
        dislike_score = math.log(dislike_count * abs(WEIGHTS['dislike']) + 1)
        interaction_score = max(0, like_score - dislike_score)
        
        # 计算评论分数
        comment_score = math.log(comment_count * WEIGHTS['comment'] + 1)
        
        base_score = interaction_score + comment_score
        
        # 2. 计算时间衰减
        create_time = share.get('time')
        if create_time:
            create_datetime = datetime.strptime(create_time, "%Y-%m-%d %H:%M:%S")
            age_in_days = (datetime.now() - create_datetime).days
            
            if age_in_days <= TIME_THRESHOLD_DAYS:
                time_decay = 1.0
            else:
                time_decay = 1.0 / (age_in_days - TIME_THRESHOLD_DAYS + 1)
                
            return base_score * time_decay
        
        return base_score
        
    except Exception as e:
        logger.error(f"计算热度分数时出错, share_id: {share.get('id')}, error: {str(e)}")
        return 0.0

def get_hot_share_ids() -> List[str]:
    """获取按热度排序的帖子ID列表
    
    Returns:
        List[str]: 排序后的share_id列表
    """
    try:
        # 1. 获取所有帖子ID
        share_ids = get_all_share_ids()
        if not share_ids:
            logger.warning("没有找到任何帖子")
            return []
            
        # 2. 批量获取帖子详情
        shares = get_plaza_shares_by_ids(share_ids)
        if not shares:
            logger.warning("获取帖子详情失败")
            return []
            
        # 3. 计算热度分数并排序
        scored_shares = [
            (share.get('id'), calculate_hot_score(share))
            for share in shares
        ]
        
        # 4. 按分数降序排序并提取ID
        scored_shares.sort(key=lambda x: x[1], reverse=True)
        hot_share_ids = [share_id for share_id, _ in scored_shares[:TOP_M]]
        
        logger.info(f"成功获取热门帖子ID列表,共{len(hot_share_ids)}条")
        return hot_share_ids
        
    except Exception as e:
        logger.error(f"获取热门帖子ID列表时出错: {str(e)}")
        return []

def get_user_preferences(mis_id: str) -> Dict:
    """获取用户偏好数据"""
    try:
        # 1. 获取用户点赞过的帖子
        query_liked = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"liked_users": mis_id}}
                    ]
                }
            },
            "size": 100  # 获取最近100条点赞记录
        }
        liked_result = es_client.search(PLAZA_INDEX, query_liked)
        
        # 2. 获取用户点踩过的帖子
        query_disliked = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"disliked_users": mis_id}}
                    ]
                }
            },
            "size": 100
        }
        disliked_result = es_client.search(PLAZA_INDEX, query_disliked)
        
        # 3. 获取用户发布的帖子
        query_shared = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"mis_id_keyword.keyword": mis_id}}
                    ]
                }
            },
            "size": 100
        }
        shared_result = es_client.search(PLAZA_INDEX, query_shared)
        
        # 4. 分析用户偏好
        preferences = {
            "liked_shops": set(),
            "disliked_shops": set(),
            "shared_shops": set(),
            "liked_foods": set(),
            "price_range": {"min": float('inf'), "max": 0, "avg": 0},
            "active_hours": set(),
            "negative_shops": set()  # 用户点踩的商家
        }
        
        price_sum = 0
        price_count = 0
        
        # 处理点赞数据
        for hit in liked_result.get('hits', {}).get('hits', []):
            source = hit.get('_source', {})
            if source.get('shop_name'):
                preferences['liked_shops'].add(source['shop_name'])
            if source.get('order_detail'):
                preferences['liked_foods'].update(source['order_detail'])
            if source.get('money_detail'):
                price = float(source['money_detail'])
                preferences['price_range']['min'] = min(preferences['price_range']['min'], price)
                preferences['price_range']['max'] = max(preferences['price_range']['max'], price)
                price_sum += price
                price_count += 1
            if source.get('time'):
                hour = datetime.strptime(source['time'], '%Y-%m-%d %H:%M:%S').hour
                preferences['active_hours'].add(hour)
                
        # 处理点踩数据
        for hit in disliked_result.get('hits', {}).get('hits', []):
            source = hit.get('_source', {})
            if source.get('shop_name'):
                preferences['disliked_shops'].add(source['shop_name'])
                preferences['negative_shops'].add(source['shop_name'])
                
        # 处理分享数据
        for hit in shared_result.get('hits', {}).get('hits', []):
            source = hit.get('_source', {})
            if source.get('shop_name'):
                preferences['shared_shops'].add(source['shop_name'])
                
        # 计算平均价格
        if price_count > 0:
            preferences['price_range']['avg'] = price_sum / price_count
            
        return preferences
        
    except Exception as e:
        logger.error(f"获取用户偏好时出错: {str(e)}")
        return {}

def calculate_content_score(share: Dict, user_preferences: Dict) -> float:
    """计算内容相关度分数"""
    try:
        score = 0.0
        
        # 1. 商家匹配度 (30%)
        shop_name = share.get('shop_name', '')
        if shop_name:
            if shop_name in user_preferences['liked_shops']:
                score += 30
            elif shop_name in user_preferences['shared_shops']:
                score += 20
            elif shop_name in user_preferences['negative_shops']:
                return 0  # 直接过滤掉用户点踩过的商家
                
        # 2. 价格匹配度 (20%)
        if share.get('money_detail') and user_preferences['price_range']['avg'] > 0:
            price = float(share['money_detail'])
            price_diff = abs(price - user_preferences['price_range']['avg'])
            price_score = 20 * (1 - min(price_diff / user_preferences['price_range']['avg'], 1))
            score += price_score
            
        # 3. 食物偏好匹配度 (20%)
        if share.get('order_detail') and user_preferences['liked_foods']:
            common_foods = set(share['order_detail']) & user_preferences['liked_foods']
            food_score = 20 * (len(common_foods) / len(set(share['order_detail'])))
            score += food_score
            
        # 4. 时间匹配度 (10%)
        if share.get('time'):
            hour = datetime.strptime(share['time'], '%Y-%m-%d %H:%M:%S').hour
            if hour in user_preferences['active_hours']:
                score += 10
                
        # 5. 互动热度 (20%)
        interaction_score = 0
        like_count = share.get('like_count', 0)
        comment_count = share.get('comment_count', 0)
        if like_count > 0 or comment_count > 0:
            interaction_score = 20 * min((math.log(like_count + 1) + math.log(comment_count + 1)) / 10, 1)
        score += interaction_score
        
        return score
        
    except Exception as e:
        logger.error(f"计算内容分数时出错: {str(e)}")
        return 0.0

def get_personalized_shares(mis_id: str, offset: int = 0, batch_size: int = 10) -> Dict:
    """获取个性化推荐的帖子"""
    try:
        # 1. 获取用户偏好
        user_preferences = get_user_preferences(mis_id)
        if not user_preferences:
            return {
                "total": 0,
                "total_count": 0,
                "offset": offset,
                "batch_size": batch_size,
                "has_more": False,
                "shares": []
            }
            
        # 2. 构建基础查询
        query = {
            "query": {
                "bool": {
                    "must_not": [
                        {"term": {"mis_id_keyword.keyword": mis_id}},  # 排除自己的帖子
                        {"terms": {"shop_name.keyword": list(user_preferences['negative_shops'])}}  # 排除点踩的商家
                    ],
                    "should": [
                        {"terms": {"shop_name.keyword": list(user_preferences['liked_shops'])}},
                        {"terms": {"order_detail.keyword": list(user_preferences['liked_foods'])}}
                    ],
                    "minimum_should_match": 0
                }
            },
            "size": batch_size * 3  # 获取更多候选，后续排序筛选
        }
        
        # 3. 执行查询
        search_result = es_client.search(PLAZA_INDEX, query)
        
        # 4. 计算个性化分数并排序
        scored_shares = []
        if search_result and 'hits' in search_result:
            for hit in search_result['hits']['hits']:
                share = hit['_source']
                score = calculate_content_score(share, user_preferences)
                if score > 0:  # 只保留有效分数的帖子
                    scored_shares.append((share, score))
                    
        # 5. 按分数排序
        scored_shares.sort(key=lambda x: x[1], reverse=True)
        
        # 6. 获取当前批次的帖子
        batch_shares = [share for share, _ in scored_shares[offset:offset + batch_size]]
        
        return {
            "total": len(batch_shares),
            "total_count": len(scored_shares),
            "offset": offset,
            "batch_size": batch_size,
            "has_more": offset + batch_size < len(scored_shares),
            "shares": batch_shares
        }
        
    except Exception as e:
        logger.error(f"获取个性化推荐帖子时出错: {str(e)}")
        return {
            "total": 0,
            "total_count": 0,
            "offset": offset,
            "batch_size": batch_size,
            "has_more": False,
            "shares": []
        }


