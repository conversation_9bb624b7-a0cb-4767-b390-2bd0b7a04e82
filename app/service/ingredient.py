import json

from my_mysql.entity import ingredient_analysis
from es import es_client
from configs.lion_config import INDEX_INGREDIENT_ANALYSIS, INDEX_SHOP_FOOD_INFO, MAINDB_INDEX
from utils.logger import logger


def upsert_ingredient(name, data, status):
    try:
        if status == 0:
            ingredient_analysis.delete_ingredient(name)
        else:
            ingredient_analysis.upsert_data(name, data)
    except Exception as e:
        logger.error(f"插入SQL中ingredient {name} 出错: {str(e)}")


def get_ingredient(name):
    return ingredient_analysis.select_ingredient(name)


def upsert_es_ingredient(name, data, status):
    try:
        if status == 0:
            logger.info(f"删除es中ingredient {name}")
            es_client.delete_doc(INDEX_INGREDIENT_ANALYSIS, name)
        else:
            logger.info(f"更新es中ingredient {name} data={data}")
            upsert_doc = {
                "doc": {
                    "ingredient": name,
                    "analysis": data
                },
                "doc_as_upsert": True  # 如果文档不存在则插入，存在则更新
            }
            es_client.upsert_data(INDEX_INGREDIENT_ANALYSIS, name, upsert_doc)
    except Exception as e:
        logger.error(f"插入es中ingredient {name} 出错: {str(e)}")


def terms_ingredient_list(ingredient_list, limit=10):
    # 批量精确查询ingredient
    terms_query = {
        "query": {
            "terms": {
                "ingredient": ingredient_list
            }
        },
        "size": limit
    }
    logger.info(f"terms_ingredient_list {json.dumps(terms_query, ensure_ascii=False)}")
    search_result = es_client.search(INDEX_INGREDIENT_ANALYSIS, terms_query)
    hits = search_result['hits']['hits']
    analysis_list = []
    for hit in hits:
        analysis_list.append(hit['_source']['analysis'])
    return analysis_list


def match_ingredient_list(ingredient_list:list, limit=10):
    # 模糊查询analysis
    match_queries = [{"match_phrase": {"analysis": term}} for term in ingredient_list]
    # 构建bool查询，组合多个match查询
    bool_query = {
        "query": {
            "bool": {
                "should": match_queries,
                "minimum_should_match": 1
            }
        },
        "size": limit
    }
    logger.info(f"match_ingredient_list {json.dumps(bool_query, ensure_ascii=False)}")
    search_result = es_client.search(INDEX_INGREDIENT_ANALYSIS, bool_query)
    hits = search_result['hits']['hits']
    analysis_list = []
    # original_list = []
    for hit in hits:
        analysis_list.append(hit['_source']['analysis'])
        # original_list.append(hit['_source'])
    return analysis_list

# 新版数据库的检索函数
def match_ingredient_listV2(ingredient_list:list, limit=10):
    # 模糊查询analysis
    match_queries = [{"match_phrase": {"content": term}} for term in ingredient_list]
    # 构建bool查询，组合多个match查询
    bool_query = {
        "query": {
            "bool": {
                "should": match_queries,
                "minimum_should_match": 1
            }
        },
        "size": limit
    }
    logger.info(f"match_ingredient_list {json.dumps(bool_query, ensure_ascii=False)}")
    search_result = es_client.search(MAINDB_INDEX, bool_query)
    hits = search_result['hits']['hits']
    analysis_list = []
    # original_list = []
    for hit in hits:
        analysis_list.append(hit['_source'])
    return analysis_list

def match_ingredient_list_with_weight(ingredient_list, limit=10):
    # 模糊查询analysis
    match_queries = [{"match_phrase": {"analysis": term[0]}} for term in ingredient_list]
    bool_query = {
        "query": {
            "bool": {"should": match_queries, "minimum_should_match": 1}
        },
        "size": limit
    }
    logger.info("ES Search Query: " + json.dumps(bool_query, ensure_ascii=False))
    search_result = es_client.search(INDEX_INGREDIENT_ANALYSIS, bool_query)
    hits = search_result['hits']['hits']
    analysis_list = []
    for hit in hits:
        analysis_list.append(hit['_source']['analysis'])
    return analysis_list

def match_merchant_list(merchant_list, limit=10):
    # 模糊查询analysis
    match_queries = [{"terms": {"name": term}} for term in merchant_list]
    bool_query = {
        "query": {
            "bool": {
                "should": match_queries, 
                "minimum_should_match": 1
            }
        },
        "size": limit
    }
    search_result = es_client.search(INDEX_SHOP_FOOD_INFO, bool_query)
    hits = search_result['hits']['hits']
    return [hit['_source'] for hit in hits]

def casual_upsert(index_name, doc_id, doc):
    """
    doc_id: 文档id, 同一个doc_id只能插入一次，否则会覆盖
    doc: 文档内容
    """
    es_client.upsert_data(index_name, doc_id, doc)

def casual_search(index_name, query):
    """
    doc_id: 文档id
    """
    search_result = es_client.search(index_name, query)
    hits = search_result['hits']['hits']
    analysis_list = []
    for hit in hits:
        analysis_list.append(hit['_source'])
    return analysis_list

if __name__ == '__main__':
    print(terms_ingredient_list(['测试']))
    print(match_ingredient_list(['wasd']))
