from vex.vex_thrift import embedding
from utils.logger import logger
import os
import numpy as np
import json
import ast
from configs.local_config import INSIGHT_DIR
import re
from my_mysql import sql_client
from my_mysql.entity import ingredient_analysis
from sqlalchemy import text, select, and_

def read_json_file(file_path):
    """
    读取指定路径的文件，提取并解析其中的JSON数据。

    参数:
    file_path (str): 文件路径

    返回:
    dict: 解析后的JSON数据

    异常:
    ValueError: 如果文件中未找到有效的JSON数据
    """
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
        
        # 找到第一个 '{' 和最后一个 '}'，提取中间的内容
        start_index = content.find('{')
        end_index = content.rfind('}')
        
        if start_index != -1 and end_index != -1 and start_index < end_index:
            json_content = content[start_index:end_index + 1]
            try:
                data = json.loads(json_content)
                return data
            except json.JSONDecodeError:
                pass
        
        raise ValueError("未找到有效的JSON数据")

def read_markdown_file(file_path):
    """
    读取Markdown文件并返回其内容。

    :param file_path: Markdown文件的路径
    :return: 文件内容的字符串
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        return content
    except FileNotFoundError:
        print(f"文件 {file_path} 未找到。")
    except Exception as e:
        print(f"读取文件时发生错误: {e}")

def read_file(file_name:str, file_type:str='json'):
    if file_type == 'json':
        return read_json_file(os.path.join(INSIGHT_DIR, file_name + '.json'))
    elif file_type == 'md':
        return read_markdown_file(os.path.join(INSIGHT_DIR, file_name + '.md'))
    else:
        raise ValueError(f"未找到有效的文件类型: {file_type}")

def construct_prompt(data, food_name):
    """
    根据提供的数据构建分析提示文本。

    参数:
    data (dict): 包含分析信息的数据

    返回:
    str: 构建的提示文本
    """
    prompt = f"以下是关于{food_name}的菜品和做法分析：\n\n"
    
    # 最受欢迎的菜品
    prompt += "最受欢迎的菜品:\n"
    for dish in data.get("最受欢迎的菜品", []):
        prompt += f"- {dish['name']} (热度: {dish['heat']})\n"
    
    # 做法分析
    prompt += "\n做法分析:\n"
    for method, details in data.get("做法分析", {}).items():
        prompt += f"{method}类 (数量: {details['value']}):\n"
        for example in details.get("examples", []):
            prompt += f"  - {example}\n"
    
    # 菜品分析
    prompt += "\n菜品分析:\n"
    prompt += "最受欢迎的口味:\n"
    for flavor in data.get("dish_analysis", {}).get("最受欢迎的口味", []):
        prompt += f"- {flavor}\n"
    
    prompt += "\n创新菜品:\n"
    for innovative_dish in data.get("dish_analysis", {}).get("创新菜品", []):
        prompt += f"- {innovative_dish}\n"
    
    prompt += "\n潜力菜品:\n"
    for potential_dish in data.get("dish_analysis", {}).get("潜力菜品", []):
        prompt += f"- {potential_dish}\n"
    
    return prompt

# TODO:如果要运行这个，需要kv_file.txt文件，这个文件是用户输入的文本和向量，需要提前生成
def get_insight_embedding(user_input:str, topk:int=5)->list[str]:
    """
    获取与用户输入相关的洞察信息。(向量查询版)

    参数:
    user_input (str): 用户输入的文本
    topk (int): 返回的洞察信息数量

    返回:
    list[str]: 洞察信息列表
    """
    kv_file_path = os.path.join(INSIGHT_DIR, 'kv_file.txt')
    if not os.path.exists(kv_file_path):
        logger.error(f"获取洞察信息化失败，目录 {INSIGHT_DIR} 中不存在 kv_file.txt 文件")
        return []
    input_tensor = np.array(embedding(user_input))
    kv_dict = {}
    with open(kv_file_path, 'r', encoding='utf-8') as f:
        for line in f:
            tensor_str, food_name = line.strip().split('|')
            tensor_list = ast.literal_eval(tensor_str)
            tensor = np.array(tensor_list)
            kv_dict[food_name] = np.dot(tensor, input_tensor)
        kv_dict = sorted(kv_dict.items(), key=lambda x: x[1], reverse=True)
        topk_food_name = [food_name for food_name, _ in kv_dict[:topk]]
        insight_list = []
        for name in topk_food_name:
            try:
                json_path = os.path.join(INSIGHT_DIR, f"{name}.json")
                insight_info = read_json_file(json_path)
                insight_list.append(construct_prompt(insight_info))
            except Exception as e:
                logger.error(f"获取洞察信息化失败，目录 {INSIGHT_DIR} 中 {name}.json 文件不存在")
                continue

        if len(insight_list) == 0:
            logger.error(f"获取洞察信息化失败，目录 {INSIGHT_DIR} 中匹配不到合适的洞察信息")
            return []
        return insight_list

def get_insight_keywords(user_input:str, topk:int=5)->list[str]:
    """
    获取与用户输入相关的洞察信息。(关键词查询版)

    参数:
    user_input (str): 用户输入的文本
    topk (int): 返回的洞察信息数量

    返回:
    list[str]: 洞察信息列表
    """
    # kv_file_path = os.path.join(INSIGHT_DIR, 'kv_file.txt')
    # if not os.path.exists(kv_file_path):
    #     logger.error(f"获取洞察信息化失败，目录 {INSIGHT_DIR} 中不存在 kv_file.txt 文件")
    #     return []
    try:    
        insight_list = []
        ids = 0
        file_list = os.listdir(INSIGHT_DIR)
        for line in file_list:
            food_name_and_time, file_type = line.strip().split('.')
            food_name, _ = food_name_and_time.split('_')
            if food_name in user_input:
                insight_info = read_file(food_name_and_time, file_type=file_type)
                insight_list.append(insight_info if file_type == 'md' else construct_prompt(insight_info, food_name))
                logger.info(f"获取洞察信息化成功，目录 {INSIGHT_DIR} 中 {insight_info} 文件存在")
                ids += 1
            if ids == topk:
                break
        if len(insight_list) == 0:
            logger.error(f"获取洞察信息化失败，目录 {INSIGHT_DIR} 中匹配不到合适的洞察信息")
            return []
        return insight_list
    except Exception as e:
        logger.error(f"获取洞察信息化失败，{str(e)}")
        return []
    
def get_insight_keywords_sql(user_input:str, topk:int=5)->list[str]:
    """
    获取与用户输入相关的洞察信息。(关键词查询版)

    参数:
    user_input (str): 用户输入的文本
    topk (int): 返回的洞察信息数量

    返回:
    list[str]: 洞察信息列表
    """
    try:    
        insight_list = ingredient_analysis.search_ingredient(user_input, topk)  

        logger.info(f"获取洞察信息化成功，{insight_list}")
        return insight_list
    except Exception as e:
        logger.error(f"获取洞察信息化失败，{str(e)}")
        return []

def set_insight(food_name:str, output_dir:str):
    """
    新增洞察信息，将食物名称及其向量追加到指定目录的kv_file.txt文件中。

    参数:
    food_name (str): 食物名称
    output_dir (str): 输出目录

    返回:
    bool: 操作是否成功
    """
    try:
        vector = embedding(food_name)
        kv_file_path = os.path.join(output_dir, 'kv_file.txt')
        with open(kv_file_path, 'a', encoding='utf-8') as f:
            f.write(f"{food_name}|{vector}\n")
        return True
    except Exception as e:
        logger.error(f"新增洞察信息失败: {str(e)}")
        return False


