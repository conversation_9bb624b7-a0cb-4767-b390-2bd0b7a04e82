import json
import time
from datetime import datetime
from utils.logger import logger
from es import es_client

# ES索引名称
PLAZA_INDEX = "user_plaza_shares_test"

def add_like(share_id: str, mis_id: str) -> bool:
    """添加点赞记录
    
    Args:
        share_id (str): 分享ID
        mis_id (str): 用户ID
        
    Returns:
        bool: 是否添加成功
    """
    try:
        # 获取当前分享
        share = es_client.get_doc(PLAZA_INDEX, share_id)
        if not share:
            logger.warning(f"未找到分享: {share_id}")
            return False
            
        # 获取当前点赞记录
        like_records = share.get('_source', {}).get('like_records', {})
        
        # 检查是否已经点赞
        if mis_id in like_records:
            logger.info(f"用户{mis_id}已经点赞过分享{share_id}")
            return False
            
        # 添加点赞记录
        like_records[mis_id] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 更新文档
        update_doc = {
            "doc": {
                "like_records": like_records,
                "like_count": len(like_records),
                "last_interaction_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        }
        
        result = es_client.update_doc(PLAZA_INDEX, share_id, update_doc)
        if result:
            logger.info(f"用户{mis_id}成功点赞分享{share_id}")
            return True
        else:
            logger.error(f"更新点赞记录失败: {share_id}")
            return False
            
    except Exception as e:
        logger.error(f"添加点赞记录出错: {str(e)}")
        return False

def remove_like(share_id: str, mis_id: str) -> bool:
    """移除点赞记录
    
    Args:
        share_id (str): 分享ID
        mis_id (str): 用户ID
        
    Returns:
        bool: 是否移除成功
    """
    try:
        # 获取当前分享
        share = es_client.get_doc(PLAZA_INDEX, share_id)
        if not share:
            logger.warning(f"未找到分享: {share_id}")
            return False
            
        # 获取当前点赞记录
        like_records = share.get('_source', {}).get('like_records', {})
        
        # 检查是否有点赞记录
        if mis_id not in like_records:
            logger.info(f"用户{mis_id}没有点赞过分享{share_id}")
            return False
            
        # 移除点赞记录
        del like_records[mis_id]
        
        # 更新文档
        update_doc = {
            "doc": {
                "like_records": like_records,
                "like_count": len(like_records),
                "last_interaction_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        }
        
        result = es_client.update_doc(PLAZA_INDEX, share_id, update_doc)
        if result:
            logger.info(f"用户{mis_id}成功取消点赞分享{share_id}")
            return True
        else:
            logger.error(f"更新点赞记录失败: {share_id}")
            return False
            
    except Exception as e:
        logger.error(f"移除点赞记录出错: {str(e)}")
        return False

def add_dislike(share_id: str, mis_id: str) -> bool:
    """添加点踩记录
    
    Args:
        share_id (str): 分享ID
        mis_id (str): 用户ID
        
    Returns:
        bool: 是否添加成功
    """
    try:
        # 获取当前分享
        share = es_client.get_doc(PLAZA_INDEX, share_id)
        if not share:
            logger.warning(f"未找到分享: {share_id}")
            return False
            
        # 获取当前点踩记录
        dislike_records = share.get('_source', {}).get('dislike_records', {})
        
        # 检查是否已经点踩
        if mis_id in dislike_records:
            logger.info(f"用户{mis_id}已经点踩过分享{share_id}")
            return False
            
        # 添加点踩记录
        dislike_records[mis_id] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 更新文档
        update_doc = {
            "doc": {
                "dislike_records": dislike_records,
                "dislike_count": len(dislike_records),
                "last_interaction_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        }
        
        result = es_client.update_doc(PLAZA_INDEX, share_id, update_doc)
        if result:
            logger.info(f"用户{mis_id}成功点踩分享{share_id}")
            return True
        else:
            logger.error(f"更新点踩记录失败: {share_id}")
            return False
            
    except Exception as e:
        logger.error(f"添加点踩记录出错: {str(e)}")
        return False

def remove_dislike(share_id: str, mis_id: str) -> bool:
    """移除点踩记录
    
    Args:
        share_id (str): 分享ID
        mis_id (str): 用户ID
        
    Returns:
        bool: 是否移除成功
    """
    try:
        # 获取当前分享
        share = es_client.get_doc(PLAZA_INDEX, share_id)
        if not share:
            logger.warning(f"未找到分享: {share_id}")
            return False
            
        # 获取当前点踩记录
        dislike_records = share.get('_source', {}).get('dislike_records', {})
        
        # 检查是否有点踩记录
        if mis_id not in dislike_records:
            logger.info(f"用户{mis_id}没有点踩过分享{share_id}")
            return False
            
        # 移除点踩记录
        del dislike_records[mis_id]
        
        # 更新文档
        update_doc = {
            "doc": {
                "dislike_records": dislike_records,
                "dislike_count": len(dislike_records),
                "last_interaction_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        }
        
        result = es_client.update_doc(PLAZA_INDEX, share_id, update_doc)
        if result:
            logger.info(f"用户{mis_id}成功取消点踩分享{share_id}")
            return True
        else:
            logger.error(f"更新点踩记录失败: {share_id}")
            return False
            
    except Exception as e:
        logger.error(f"移除点踩记录出错: {str(e)}")
        return False

def add_comment(share_id: str, mis_id: str, content: str, parent_id: str = None) -> bool:
    """添加评论
    
    Args:
        share_id: 分享ID
        mis_id: 用户ID
        content: 评论内容
        parent_id: 父评论ID（可选）
        
    Returns:
        bool: 是否成功
    """
    try:
        # 获取当前时间戳
        current_timestamp = int(time.time() * 1000)  # 转换为毫秒级时间戳
        
        # 生成唯一评论ID
        comment_id = f"comment_{current_timestamp}_{mis_id}"
        
        # 构建评论记录
        comment_record = {
            "mis_id": mis_id,
            "content": content,
            "parent_id": parent_id,
            "create_time": current_timestamp,  # 使用时间戳而不是字符串
            "is_deleted": False
        }
        
        # 获取现有的分享内容
        share = get_plaza_share_by_id(share_id)
        if not share:
            logger.error(f"分享内容不存在: {share_id}")
            return False
            
        # 获取现有的评论记录
        existing_comments = share.get('comment_record', {})
        if not isinstance(existing_comments, dict):
            existing_comments = {}
            
        # 添加新评论
        existing_comments[comment_id] = comment_record
        
        # 更新ES中的评论记录
        update_data = {
            "comment_record": existing_comments,
            "comment_count": len(existing_comments),
            "last_interaction_time": current_timestamp  # 使用时间戳而不是字符串
        }
        
        # 调用更新函数
        success = update_plaza_share_content(share_id, update_data)
        
        if success:
            logger.info(f"评论添加成功: share_id={share_id}, comment_id={comment_id}")
            return True
        else:
            logger.error(f"评论添加失败: share_id={share_id}")
            return False
            
    except Exception as e:
        logger.error(f"添加评论时出错: {str(e)}")
        return False

def delete_comment(share_id: str, comment_id: str, mis_id: str) -> bool:
    """删除评论
    
    Args:
        share_id (str): 分享ID
        comment_id (str): 评论ID
        mis_id (str): 用户ID
        
    Returns:
        bool: 是否删除成功
    """
    try:
        # 获取当前分享
        share = es_client.get_doc(PLAZA_INDEX, share_id)
        if not share:
            logger.warning(f"未找到分享: {share_id}")
            return False
            
        # 获取当前评论记录
        comment_records = share.get('_source', {}).get('comment_records', {})
        
        # 检查评论是否存在
        if comment_id not in comment_records:
            logger.warning(f"未找到评论: {comment_id}")
            return False
            
        # 检查权限
        comment = comment_records[comment_id]
        if comment["mis_id"] != mis_id:
            logger.warning(f"用户{mis_id}无权删除评论{comment_id}")
            return False
            
        # 标记评论为已删除
        comment_records[comment_id]["is_deleted"] = True
        
        # 更新文档
        update_doc = {
            "doc": {
                "comment_records": comment_records,
                "comment_count": len(comment_records),
                "last_interaction_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        }
        
        result = es_client.update_doc(PLAZA_INDEX, share_id, update_doc)
        if result:
            logger.info(f"用户{mis_id}成功删除评论{comment_id}")
            return True
        else:
            logger.error(f"更新评论记录失败: {share_id}")
            return False
            
    except Exception as e:
        logger.error(f"删除评论出错: {str(e)}")
        return False

def get_interaction_stats(share_id: str) -> dict:
    """获取分享的互动统计数据
    
    Args:
        share_id (str): 分享ID
        
    Returns:
        dict: 互动统计数据
    """
    try:
        # 获取当前分享
        share = es_client.get_doc(PLAZA_INDEX, share_id)
        if not share:
            logger.warning(f"未找到分享: {share_id}")
            return None
            
        source = share.get('_source', {})
        
        # 构建返回数据
        stats = {
            "like_count": source.get('like_count', 0),
            "dislike_count": source.get('dislike_count', 0),
            "comment_count": source.get('comment_count', 0),
            "last_interaction_time": source.get('last_interaction_time')
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"获取互动统计数据出错: {str(e)}")
        return None

def get_user_interactions(share_id: str, mis_id: str) -> dict:
    """获取用户在特定分享上的互动状态
    
    Args:
        share_id (str): 分享ID
        mis_id (str): 用户ID
        
    Returns:
        dict: 用户互动状态
    """
    try:
        # 获取当前分享
        share = es_client.get_doc(PLAZA_INDEX, share_id)
        if not share:
            logger.warning(f"未找到分享: {share_id}")
            return None
            
        source = share.get('_source', {})
        
        # 构建返回数据
        interactions = {
            "has_liked": mis_id in source.get('like_records', {}),
            "has_disliked": mis_id in source.get('dislike_records', {}),
            "comments": []
        }
        
        # 获取用户的评论
        for comment_id, comment in source.get('comment_records', {}).items():
            if comment["mis_id"] == mis_id and not comment["is_deleted"]:
                interactions["comments"].append({
                    "comment_id": comment_id,
                    "content": comment["content"],
                    "create_time": comment["create_time"]
                })
        
        return interactions
        
    except Exception as e:
        logger.error(f"获取用户互动状态出错: {str(e)}")
        return None 