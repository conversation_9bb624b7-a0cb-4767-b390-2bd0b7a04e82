# 记忆模块 (Memory Module)

该模块用于处理大模型的记忆功能，允许存储和检索用户对话中的重要事实。

## 使用方法

### 基本用法

```python
from app.service.memory import MemoryService

# 初始化记忆服务
memory_service = MemoryService()

# 使用记忆更新功能
memory_service.update_memory(
    assistant_message_id="msg123",  # 预生成的消息ID
    agent_id="agent456",           # Agent ID
    new_retrieved_facts=[          # 要存储的事实列表
        "用户喜欢吃辣",
        "用户对海鲜过敏"
    ],
    user_id="user789",             # 用户ID
    model="gpt-4",                 # 模型类型
    async_update=True              # 是否异步更新
)

# 检索用户和代理的记忆数据
memory_data = memory_service.get_memory(
    user_id="user789",             # 用户ID
    agent_id="agent456"            # Agent ID
)

# 使用检索到的记忆数据
if memory_data:
    print(f"找到记忆数据: {memory_data}")
else:
    print("未找到记忆数据")
```

### 记忆API接口

记忆模块使用远程API接口存储和管理记忆：

- 更新记忆: `https://aigc.sankuai.com/web/v1/memory/updateStatus`
- 检索记忆: `https://aigc.sankuai.com/web/v1/memory/getMemory`

## 参数说明

### 更新记忆时需要提供以下参数：

| 参数 | 类型 | 说明 |
|------|------|------|
| assistant_message_id | String | 预生成的消息ID |
| agent_id | String | Agent ID |
| new_retrieved_facts | List[String] | 要存入的数据 |
| user_id | String | 用户ID |
| model | String | 模型类型 |

### 检索记忆时需要提供以下参数：

| 参数 | 类型 | 说明 |
|------|------|------|
| user_id | String | 用户ID |
| agent_id | String | Agent ID |

## 实现细节

1. `MemoryService`：主要服务类，提供记忆管理功能
2. `MemoryClient`：负责与远程记忆API交互

远程API是通过以下端点实现的：
- 更新记忆: `https://aigc.sankuai.com/web/v1/memory/updateStatus`
- 检索记忆: `https://aigc.sankuai.com/web/v1/memory/getMemory`

## 异步处理

记忆更新支持异步操作，避免阻塞主线程：

```python
# 异步更新（默认）
memory_service.update_memory(..., async_update=True)

# 同步更新
result = memory_service.update_memory(..., async_update=False)
```