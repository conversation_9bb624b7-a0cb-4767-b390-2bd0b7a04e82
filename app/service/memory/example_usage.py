"""
记忆模块的使用示例。
"""

from .memory_service import MemoryService


def memory_example():
    """演示如何使用记忆模块。"""
    # 初始化记忆服务
    memory_service = MemoryService()
    
    # 示例参数
    user_id = "user123"
    agent_id = "agent456"
    assistant_message_id = "msg789"
    model = "gpt-4"
    
    # 示例要存储的事实
    facts = [
        "用户喜欢吃辣食",
        "用户提到他们对花生过敏",
        "用户最喜欢的餐厅是'金龙'"
    ]
    
    # 存储记忆（此示例为同步更新）
    memory_service.update_memory(
        assistant_message_id=assistant_message_id,
        agent_id=agent_id,
        new_retrieved_facts=facts,
        user_id=user_id,
        model=model,
        async_update=False  # 在生产环境中使用True以获得更好的性能
    )
    
    # 检索记忆数据
    print("\n获取记忆数据:")
    memory_data = memory_service.get_memory(user_id, agent_id)
    if memory_data:
        print(f"成功获取到用户 {user_id} 和代理 {agent_id} 的记忆数据")
        print(f"记忆数据: {memory_data}")
    else:
        print(f"未找到用户 {user_id} 和代理 {agent_id} 的记忆数据")


def api_usage_example():
    """
    展示如何直接调用记忆API端点。
    
    可用的端点:
    
    - 更新记忆: POST https://aigc.sankuai.com/web/v1/memory/updateStatus
    - 获取记忆: GET https://aigc.sankuai.com/web/v1/memory/getMemory
    """
    import requests
    
    # 示例: 更新记忆
    create_data = {
        "assistantMessageId": "msg123",
        "agentId": "agent456",
        "newRetrievedFacts": [
            "用户喜欢披萨",
            "用户不喜欢海鲜"
        ],
        "userId": "user789",
        "model": "gpt-4"
    }
    
    response = requests.post(
        "https://aigc.sankuai.com/web/v1/memory/updateStatus",
        json=create_data
    )
    print("更新记忆响应:", response.json())
    
    # 示例: 获取记忆
    params = {
        "userId": "user789",
        "agentId": "agent456"
    }
    
    response = requests.get(
        "https://aigc.sankuai.com/web/v1/memory/getMemory",
        params=params
    )
    print("获取记忆响应:", response.json())


if __name__ == "__main__":
    memory_example()
    # 取消注释以显示API使用示例
    # api_usage_example()