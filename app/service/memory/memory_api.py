"""
Memory API endpoints for the memory service.
"""

from flask import Blueprint, request, jsonify
from typing import Dict, Any, List
from utils.logger import logger
from .memory_service import MemoryService
from .memory_model import Memory, MemoryFact

# Create Blueprint
memory_api = Blueprint('memory_api', __name__)
memory_service = MemoryService()


@memory_api.route('/memory', methods=['POST'])
def create_or_update_memory():
    """
    Create or update memory facts.
    
    Request body:
    {
        "assistant_message_id": "string",  # Required
        "agent_id": "string",              # Required
        "new_retrieved_facts": ["string"], # Required
        "user_id": "string",               # Required
        "model": "string",                 # Optional
        "importance": float,               # Optional (default: 0.5)
        "async_update": boolean            # Optional (default: true)
    }
    
    Response:
    {
        "success": boolean,
        "message": "string"
    }
    """
    try:
        data = request.json
        
        # Validate required fields
        required_fields = ['assistant_message_id', 'agent_id', 'new_retrieved_facts', 'user_id']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'message': f'Missing required field: {field}'
                }), 400
                
        # Extract fields
        assistant_message_id = data['assistant_message_id']
        agent_id = data['agent_id']
        new_retrieved_facts = data['new_retrieved_facts']
        user_id = data['user_id']
        model = data.get('model')
        importance = float(data.get('importance', 0.5))
        async_update = bool(data.get('async_update', True))
        
        # Validate fact data
        if not isinstance(new_retrieved_facts, list):
            return jsonify({
                'success': False,
                'message': 'new_retrieved_facts must be a list of strings'
            }), 400
            
        # Update memory
        result = memory_service.update_memory(
            assistant_message_id=assistant_message_id,
            agent_id=agent_id,
            new_retrieved_facts=new_retrieved_facts,
            user_id=user_id,
            model=model,
            importance=importance,
            async_update=async_update
        )
        
        if result:
            return jsonify({
                'success': True,
                'message': 'Memory updated successfully'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to update memory'
            }), 500
            
    except Exception as e:
        logger.error(f"Error in create_or_update_memory API: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        }), 500


@memory_api.route('/memory/<user_id>/<agent_id>', methods=['GET'])
def get_memory(user_id: str, agent_id: str):
    """
    Get memory for a specific user and agent.
    
    Response:
    {
        "success": boolean,
        "memory": {
            "user_id": "string",
            "agent_id": "string",
            "facts": [
                {
                    "content": "string",
                    "source_message_id": "string",
                    "timestamp": "string",
                    "importance": float,
                    "metadata": {}
                }
            ],
            "model": "string",
            "metadata": {},
            "created_at": "string",
            "updated_at": "string"
        }
    }
    """
    try:
        memory = memory_service.get_memory(user_id, agent_id)
        
        if memory:
            return jsonify({
                'success': True,
                'memory': memory.to_dict()
            })
        else:
            return jsonify({
                'success': False,
                'message': f'No memory found for user {user_id} and agent {agent_id}'
            }), 404
            
    except Exception as e:
        logger.error(f"Error in get_memory API: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        }), 500


@memory_api.route('/memory/content/<user_id>/<agent_id>', methods=['GET'])
def get_memory_content(user_id: str, agent_id: str):
    """
    Get formatted memory content for a specific user and agent.
    
    Query parameters:
    - limit: Maximum number of facts to include (default: 10)
    - importance_threshold: Minimum importance threshold (default: 0.0)
    
    Response:
    {
        "success": boolean,
        "content": "string"
    }
    """
    try:
        # Parse query parameters
        limit = int(request.args.get('limit', 10))
        importance_threshold = float(request.args.get('importance_threshold', 0.0))
        
        content = memory_service.get_memory_content(
            user_id=user_id,
            agent_id=agent_id,
            limit=limit,
            importance_threshold=importance_threshold
        )
        
        return jsonify({
            'success': True,
            'content': content
        })
            
    except Exception as e:
        logger.error(f"Error in get_memory_content API: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        }), 500


@memory_api.route('/memory/user/<user_id>', methods=['GET'])
def get_memories_by_user(user_id: str):
    """
    Get all memories for a specific user.
    
    Response:
    {
        "success": boolean,
        "memories": [
            {
                "user_id": "string",
                "agent_id": "string",
                "facts": [...],
                "model": "string",
                "metadata": {},
                "created_at": "string",
                "updated_at": "string"
            }
        ]
    }
    """
    try:
        memories = memory_service.get_memories_by_user(user_id)
        
        return jsonify({
            'success': True,
            'memories': [memory.to_dict() for memory in memories]
        })
            
    except Exception as e:
        logger.error(f"Error in get_memories_by_user API: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        }), 500


@memory_api.route('/memory/agent/<agent_id>', methods=['GET'])
def get_memories_by_agent(agent_id: str):
    """
    Get all memories for a specific agent.
    
    Response:
    {
        "success": boolean,
        "memories": [
            {
                "user_id": "string",
                "agent_id": "string",
                "facts": [...],
                "model": "string",
                "metadata": {},
                "created_at": "string",
                "updated_at": "string"
            }
        ]
    }
    """
    try:
        memories = memory_service.get_memories_by_agent(agent_id)
        
        return jsonify({
            'success': True,
            'memories': [memory.to_dict() for memory in memories]
        })
            
    except Exception as e:
        logger.error(f"Error in get_memories_by_agent API: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        }), 500


@memory_api.route('/memory/<user_id>/<agent_id>', methods=['DELETE'])
def delete_memory(user_id: str, agent_id: str):
    """
    Delete memory for a specific user and agent.
    
    Response:
    {
        "success": boolean,
        "message": "string"
    }
    """
    try:
        result = memory_service.delete_memory(user_id, agent_id)
        
        if result:
            return jsonify({
                'success': True,
                'message': f'Memory for user {user_id} and agent {agent_id} deleted successfully'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'No memory found for user {user_id} and agent {agent_id}'
            }), 404
            
    except Exception as e:
        logger.error(f"Error in delete_memory API: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        }), 500


@memory_api.route('/memory/user/<user_id>', methods=['DELETE'])
def delete_memories_by_user(user_id: str):
    """
    Delete all memories for a specific user.
    
    Response:
    {
        "success": boolean,
        "message": "string",
        "count": int
    }
    """
    try:
        count = memory_service.delete_memories_by_user(user_id)
        
        return jsonify({
            'success': True,
            'message': f'Deleted {count} memories for user {user_id}',
            'count': count
        })
            
    except Exception as e:
        logger.error(f"Error in delete_memories_by_user API: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        }), 500


@memory_api.route('/memory/agent/<agent_id>', methods=['DELETE'])
def delete_memories_by_agent(agent_id: str):
    """
    Delete all memories for a specific agent.
    
    Response:
    {
        "success": boolean,
        "message": "string",
        "count": int
    }
    """
    try:
        count = memory_service.delete_memories_by_agent(agent_id)
        
        return jsonify({
            'success': True,
            'message': f'Deleted {count} memories for agent {agent_id}',
            'count': count
        })
            
    except Exception as e:
        logger.error(f"Error in delete_memories_by_agent API: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        }), 500