"""
与远程记忆API服务交互的客户端。
"""
from configs.config import CURRENT_ENV
import requests
import json
from typing import List, Optional, Dict, Any
from utils.logger import logger


class MemoryClient:
    """与远程记忆API服务交互的客户端。"""
    
    def __init__(self):
        """
        初始化记忆客户端。
        
        参数:
            base_url: 记忆API服务的基础URL
        """

        self.base_url = "http://10.101.180.38:8080" if CURRENT_ENV == "test" else "https://aigc.sankuai.com"
        self.update_endpoint = f"{self.base_url}/web/v1/memory/updateMemory"
        self.retrieve_endpoint = f"{self.base_url}/web/v1/memory/getMemory"
        
    def update_memory(self,
                      assistant_message_id: str,
                      agent_id: str,
                      new_retrieved_facts: List[str],
                      user_id: str,
                      model: Optional[str] = None,
                      token: Optional[str] = None) -> bool:
        """
        使用新事实更新记忆。
        
        参数:
            assistant_message_id: 预生成的消息ID
            agent_id: Agent ID
            new_retrieved_facts: 要存储的事实字符串列表
            user_id: 用户ID(废弃，不会再上传到记忆数据)
            model: 模型类型(废弃，不会再上传到记忆数据)
            token: 认证令牌
            
        返回:
            bool: 成功状态
        """
        try:
            # 准备请求数据
            data = {
                "assistantMessageId": assistant_message_id,
                "agentId": agent_id,
                "newRetrievedFacts": new_retrieved_facts,
                # "userId": user_id   
            }
            
            # 如果提供了model，则添加
            # if model:
            #     data["model"] = model
                
            # 添加认证头（如果有token）
            headers = {
                "Content-Type": "application/json"
            }
            if token and token.strip():
                headers["Authorization"] = f"Bearer {token}"
                # 只在请求体中也加入token
                headers["access-token"] = token
            
            logger.info(f"记忆上传的headers：{headers},data：{data}")
            
            # 向记忆API发送POST请求
            try:
                response = requests.post(
                    self.update_endpoint,
                    json=data,
                    headers=headers
                )
            except Exception as e:
                logger.error(f"记忆上传时出错: {str(e)}")
                return False
            
            # 检查响应状态
            if response.status_code == 200:
                result = response.json()
                logger.debug(f"Memory API response: {result}")
                
                # 检查状态码，0表示成功
                if result.get("status") == 0:
                    logger.info(f"成功为用户 {user_id} 和代理 {agent_id} 更新了记忆")
                    return True
                else:
                    error_msg = result.get("data", {}).get("message", "未知错误")
                    logger.error(f"记忆API错误: {error_msg}")
                    # 打印完整响应内容以便调试
                    logger.error(f"完整响应: {response.text}")
                    return False
            elif response.status_code == 401:
                logger.warning(f"记忆API认证失败 (401): {response.text} - 用户可能需要登录")
                # 记录详细的错误信息便于排查认证问题
                if "ssoid" in response.text:
                    logger.warning("检测到SSO认证问题，可能需要配置正确的SSO凭证")
                return False
            else:
                logger.error(f"记忆API返回状态码 {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"通过API更新记忆时出错: {str(e)}")
            return False
            
    def get_memory(self,
                   user_id: str,
                   agent_id: str) -> Optional[Dict[str, Any]]:
        """
        获取特定用户和代理的记忆内容。
        
        参数:
            user_id: 用户ID
            agent_id: Agent ID
            
        返回:
            Dict[str, Any] 或 None: 记忆内容，如果未找到则为None
        """
        try:
            # 准备请求数据
            params = {
                "userId": user_id,
                "agentId": agent_id
            }
            
            # 向记忆API发送GET请求
            response = requests.get(
                self.retrieve_endpoint,
                params=params,
                headers={
                    "Content-Type": "application/json"
                }
            )
            
            # 检查响应状态
            if response.status_code == 200:
                result = response.json()
                # 检查状态码，0表示成功
                if result.get("status") == 0:
                    memory_data = result.get("data", {})
                    logger.info(f"成功获取用户 {user_id} 和代理 {agent_id} 的记忆")
                    return memory_data
                else:
                    error_msg = result.get("message", "未知错误")
                    logger.error(f"获取记忆API错误: {error_msg}")
                    return None
            elif response.status_code == 404:
                logger.info(f"未找到用户 {user_id} 和代理 {agent_id} 的记忆")
                return None
            else:
                logger.error(f"记忆API返回状态码 {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"通过API获取记忆时出错: {str(e)}")
            return None