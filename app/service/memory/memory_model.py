"""
用于存储和管理记忆事实的数据模型。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
import json


class MemoryFact:
    """从对话中提取的单个记忆事实。"""
    
    def __init__(self, 
                 content: str,
                 source_message_id: Optional[str] = None,
                 timestamp: Optional[datetime] = None,
                 importance: float = 0.5,
                 metadata: Optional[Dict[str, Any]] = None):
        """
        初始化记忆事实。
        
        参数:
            content: 事实内容，字符串形式
            source_message_id: 提取该事实的消息ID
            timestamp: 创建/提取该事实的时间
            importance: 表示重要性的值，从0.0到1.0（值越高越重要）
            metadata: 关于该事实的额外元数据
        """
        self.content = content
        self.source_message_id = source_message_id
        self.timestamp = timestamp or datetime.now()
        self.importance = max(0.0, min(1.0, importance))  # 限制在0和1之间
        self.metadata = metadata or {}
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典，用于序列化。"""
        return {
            "content": self.content,
            "source_message_id": self.source_message_id,
            "timestamp": self.timestamp.isoformat(),
            "importance": self.importance,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryFact':
        """从字典创建MemoryFact。"""
        return cls(
            content=data.get("content", ""),
            source_message_id=data.get("source_message_id"),
            timestamp=datetime.fromisoformat(data.get("timestamp")) if data.get("timestamp") else None,
            importance=data.get("importance", 0.5),
            metadata=data.get("metadata", {})
        )


class Memory:
    """用户的记忆事实集合。"""
    
    def __init__(self, 
                 user_id: str,
                 agent_id: str,
                 facts: Optional[List[MemoryFact]] = None,
                 model: Optional[str] = None,
                 metadata: Optional[Dict[str, Any]] = None):
        """
        初始化记忆集合。
        
        参数:
            user_id: 记忆所属用户的ID
            agent_id: 记忆所属代理的ID
            facts: 记忆事实列表
            model: 生成/使用这些记忆的模型
            metadata: 关于记忆集合的额外元数据
        """
        self.user_id = user_id
        self.agent_id = agent_id
        self.facts = facts or []
        self.model = model
        self.metadata = metadata or {}
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        
    def add_fact(self, fact: MemoryFact) -> None:
        """向记忆添加事实。"""
        self.facts.append(fact)
        self.updated_at = datetime.now()
        
    def add_facts(self, facts: List[MemoryFact]) -> None:
        """向记忆添加多个事实。"""
        self.facts.extend(facts)
        self.updated_at = datetime.now()
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典，用于序列化。"""
        return {
            "user_id": self.user_id,
            "agent_id": self.agent_id,
            "facts": [fact.to_dict() for fact in self.facts],
            "model": self.model,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Memory':
        """从字典创建Memory。"""
        memory = cls(
            user_id=data.get("user_id", ""),
            agent_id=data.get("agent_id", ""),
            model=data.get("model"),
            metadata=data.get("metadata", {})
        )
        
        # 如果可用，设置时间戳
        if data.get("created_at"):
            memory.created_at = datetime.fromisoformat(data.get("created_at"))
        if data.get("updated_at"):
            memory.updated_at = datetime.fromisoformat(data.get("updated_at"))
            
        # 如果可用，添加事实
        if data.get("facts"):
            memory.facts = [MemoryFact.from_dict(fact_data) for fact_data in data.get("facts", [])]
            
        return memory
    
    def to_json(self) -> str:
        """转换为JSON字符串。"""
        return json.dumps(self.to_dict())
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Memory':
        """从JSON字符串创建Memory。"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def get_most_important_facts(self, limit: int = 10) -> List[MemoryFact]:
        """获取最重要的事实，按重要性排序。"""
        sorted_facts = sorted(self.facts, key=lambda f: f.importance, reverse=True)
        return sorted_facts[:limit]
    
    def get_most_recent_facts(self, limit: int = 10) -> List[MemoryFact]:
        """获取最近的事实，按时间戳排序。"""
        sorted_facts = sorted(self.facts, key=lambda f: f.timestamp, reverse=True)
        return sorted_facts[:limit]
    
    def get_memory_content(self, limit: int = 10, importance_threshold: float = 0.0) -> str:
        """
        获取适合插入提示词的格式化记忆内容字符串。
        
        参数:
            limit: 包含的最大事实数量
            importance_threshold: 包含的最小重要性阈值（0.0到1.0）
            
        返回:
            格式化的记忆内容字符串
        """
        filtered_facts = [fact for fact in self.facts if fact.importance >= importance_threshold]
        sorted_facts = sorted(filtered_facts, key=lambda f: f.importance, reverse=True)
        limited_facts = sorted_facts[:limit]
        
        if not limited_facts:
            return ""
        
        # 格式化为项目符号列表
        content = "用户记忆:\n"
        for fact in limited_facts:
            content += f"- {fact.content}\n"
            
        return content