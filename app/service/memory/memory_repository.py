"""
Repository for memory storage operations using Redis.
"""

import json
from typing import Optional, List, Dict, Any
from datetime import datetime
import redis
from utils.logger import logger
from .memory_model import Memory, MemoryFact


class MemoryRepository:
    """Repository for storing and retrieving memory data using Redis."""
    
    def __init__(self, redis_client=None, prefix: str = "memory"):
        """
        Initialize the memory repository.
        
        Args:
            redis_client: Optional Redis client (uses app's client if not provided)
            prefix: Key prefix for Redis storage
        """
        from app.redis.cache import redis_client as app_redis_client
        self.redis = redis_client or app_redis_client
        self.prefix = prefix
        
    def _get_key(self, user_id: str, agent_id: str) -> str:
        """Generate a Redis key for the memory."""
        return f"{self.prefix}:{user_id}:{agent_id}"
    
    def _get_user_prefix(self, user_id: str) -> str:
        """Generate a Redis key prefix for all memories of a user."""
        return f"{self.prefix}:{user_id}:*"
    
    def _get_agent_prefix(self, agent_id: str) -> str:
        """Generate a Redis key prefix for all memories of an agent."""
        return f"{self.prefix}:*:{agent_id}"
    
    def save(self, memory: Memory) -> bool:
        """
        Save a memory to Redis.
        
        Args:
            memory: Memory object to save
            
        Returns:
            bool: Whether the save was successful
        """
        try:
            if not self.redis:
                logger.error("Redis client not available")
                return False
                
            key = self._get_key(memory.user_id, memory.agent_id)
            data = memory.to_json()
            
            # Set the memory in Redis (no expiration)
            self.redis.set(key, data)
            logger.info(f"Saved memory for user {memory.user_id} and agent {memory.agent_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving memory: {str(e)}")
            return False
    
    def get(self, user_id: str, agent_id: str) -> Optional[Memory]:
        """
        Get a memory from Redis.
        
        Args:
            user_id: ID of the user
            agent_id: ID of the agent
            
        Returns:
            Memory or None: The memory if found, None otherwise
        """
        try:
            if not self.redis:
                logger.error("Redis client not available")
                return None
                
            key = self._get_key(user_id, agent_id)
            data = self.redis.get(key)
            
            if not data:
                logger.info(f"No memory found for user {user_id} and agent {agent_id}")
                return None
                
            # Create a new Memory from the stored data
            memory = Memory.from_json(data)
            return memory
            
        except Exception as e:
            logger.error(f"Error retrieving memory: {str(e)}")
            return None
    
    def find_by_user_id(self, user_id: str) -> List[Memory]:
        """
        Find all memories for a specific user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            List[Memory]: List of memories for the user
        """
        try:
            if not self.redis:
                logger.error("Redis client not available")
                return []
                
            # Get all keys for this user
            pattern = self._get_user_prefix(user_id)
            keys = self.redis.keys(pattern)
            
            if not keys:
                logger.info(f"No memories found for user {user_id}")
                return []
                
            # Get all memories
            memories = []
            for key in keys:
                data = self.redis.get(key)
                if data:
                    try:
                        memory = Memory.from_json(data)
                        memories.append(memory)
                    except Exception as e:
                        logger.error(f"Error parsing memory data: {str(e)}")
                        
            return memories
            
        except Exception as e:
            logger.error(f"Error searching memories by user_id: {str(e)}")
            return []
    
    def find_by_agent_id(self, agent_id: str) -> List[Memory]:
        """
        Find all memories for a specific agent.
        
        Args:
            agent_id: ID of the agent
            
        Returns:
            List[Memory]: List of memories for the agent
        """
        try:
            if not self.redis:
                logger.error("Redis client not available")
                return []
                
            # Get all keys for this agent
            pattern = self._get_agent_prefix(agent_id)
            keys = self.redis.keys(pattern)
            
            if not keys:
                logger.info(f"No memories found for agent {agent_id}")
                return []
                
            # Get all memories
            memories = []
            for key in keys:
                data = self.redis.get(key)
                if data:
                    try:
                        memory = Memory.from_json(data)
                        memories.append(memory)
                    except Exception as e:
                        logger.error(f"Error parsing memory data: {str(e)}")
                        
            return memories
            
        except Exception as e:
            logger.error(f"Error searching memories by agent_id: {str(e)}")
            return []
    
    def update(self, user_id: str, agent_id: str, 
               new_facts: List[str], 
               source_message_id: Optional[str] = None,
               importance: float = 0.5,
               model: Optional[str] = None) -> bool:
        """
        Update a memory with new facts.
        
        Args:
            user_id: ID of the user
            agent_id: ID of the agent
            new_facts: List of new fact strings to add
            source_message_id: ID of the message these facts came from
            importance: Importance level for the new facts
            model: Optional model identifier
            
        Returns:
            bool: Whether the update was successful
        """
        try:
            # Get existing memory or create a new one
            memory = self.get(user_id, agent_id)
            if not memory:
                memory = Memory(user_id=user_id, agent_id=agent_id, model=model)
            
            # Add the new facts
            timestamp = datetime.now()
            fact_objects = [
                MemoryFact(
                    content=fact,
                    source_message_id=source_message_id,
                    timestamp=timestamp,
                    importance=importance
                )
                for fact in new_facts if fact.strip()  # Skip empty facts
            ]
            
            memory.add_facts(fact_objects)
            
            # If model is provided, update the memory's model
            if model:
                memory.model = model
                
            # Save the updated memory
            return self.save(memory)
            
        except Exception as e:
            logger.error(f"Error updating memory: {str(e)}")
            return False
    
    def delete(self, user_id: str, agent_id: str) -> bool:
        """
        Delete a memory from Redis.
        
        Args:
            user_id: ID of the user
            agent_id: ID of the agent
            
        Returns:
            bool: Whether the deletion was successful
        """
        try:
            if not self.redis:
                logger.error("Redis client not available")
                return False
                
            key = self._get_key(user_id, agent_id)
            result = self.redis.delete(key)
            
            if result:
                logger.info(f"Deleted memory for user {user_id} and agent {agent_id}")
                return True
            else:
                logger.info(f"No memory found to delete for user {user_id} and agent {agent_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting memory: {str(e)}")
            return False
            
    def delete_by_user_id(self, user_id: str) -> int:
        """
        Delete all memories for a specific user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            int: Number of memories deleted
        """
        try:
            if not self.redis:
                logger.error("Redis client not available")
                return 0
                
            # Get all keys for this user
            pattern = self._get_user_prefix(user_id)
            keys = self.redis.keys(pattern)
            
            if not keys:
                logger.info(f"No memories found to delete for user {user_id}")
                return 0
                
            # Delete all memories
            count = self.redis.delete(*keys)
            logger.info(f"Deleted {count} memories for user {user_id}")
            return count
            
        except Exception as e:
            logger.error(f"Error deleting memories by user_id: {str(e)}")
            return 0
            
    def delete_by_agent_id(self, agent_id: str) -> int:
        """
        Delete all memories for a specific agent.
        
        Args:
            agent_id: ID of the agent
            
        Returns:
            int: Number of memories deleted
        """
        try:
            if not self.redis:
                logger.error("Redis client not available")
                return 0
                
            # Get all keys for this agent
            pattern = self._get_agent_prefix(agent_id)
            keys = self.redis.keys(pattern)
            
            if not keys:
                logger.info(f"No memories found to delete for agent {agent_id}")
                return 0
                
            # Delete all memories
            count = self.redis.delete(*keys)
            logger.info(f"Deleted {count} memories for agent {agent_id}")
            return count
            
        except Exception as e:
            logger.error(f"Error deleting memories by agent_id: {str(e)}")
            return 0