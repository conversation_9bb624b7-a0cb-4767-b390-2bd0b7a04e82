"""
用于管理模型记忆的服务。
"""

from typing import List, Optional, Dict, Any
import json
from datetime import datetime
from threading import Thread
from utils.logger import logger
from .memory_model import Memory, MemoryFact
from .memory_client import MemoryClient
import time


class MemoryService:
    """用于管理模型记忆的服务。"""
    
    def __init__(self):
        """初始化记忆服务。"""
        self.client = MemoryClient()
    
    def update_memory(self,
                      assistant_message_id: str,
                      agent_id: str,
                      new_retrieved_facts: List[str],
                      user_id: str,
                      model: str = None,
                      importance: float = 0.5,
                      async_update: bool = True,
                      token: str = None) -> bool:
        """
        使用新事实更新记忆（主要函数，按需求实现）。
        
        参数:
            assistant_message_id: 预生成的消息ID
            agent_id: Agent ID
            new_retrieved_facts: 要存储的事实字符串列表
            user_id: 用户ID
            model: 模型类型
            importance: 这些事实的重要性（0.0到1.0）- 在远程API中不使用
            async_update: 是否异步更新
            token: 访问令牌
        返回:
            bool: 成功状态
        """
        try:
            if not new_retrieved_facts:
                logger.warning(f"没有提供用于存储的事实，用户 {user_id} 和代理 {agent_id}")
                return False
                
            # 过滤掉空事实
            valid_facts = [fact for fact in new_retrieved_facts if fact and fact.strip()]
            
            if not valid_facts:
                logger.warning(f"没有有效的事实可存储，用户 {user_id} 和代理 {agent_id}")
                return False
                
            if async_update:
                # 启动线程异步更新记忆
                try:
                    Thread(
                        target=self._update_memory_sync, 
                        args=(assistant_message_id, agent_id, valid_facts, user_id, model, token),
                        daemon=True  # 设置为daemon线程，不会阻止主程序退出
                    ).start()
                    logger.info(f"已启动异步线程更新用户 {user_id} 的记忆")
                    return True
                except Exception as e:
                    logger.error(f"启动异步记忆更新线程时出错: {str(e)}")
                    # 线程启动失败，但不影响主流程
                    return True
            else:
                # 同步更新记忆
                return self._update_memory_sync(assistant_message_id, agent_id, valid_facts, user_id, model, token)
        except Exception as e:
            logger.error(f"准备记忆更新时发生错误: {str(e)}")
            # 即使记忆更新失败，也不应影响主流程
            return True
    
    def _update_memory_sync(self, 
                           assistant_message_id: str,
                           agent_id: str,
                           facts: List[str],
                           user_id: str,
                           model: Optional[str] = None,
                           token: str = None) -> bool:
        """使用远程API同步实现记忆更新。"""
        try:
            # 增加重试机制
            max_retries = 2
            retry_count = 0
            success = False
            
            while retry_count < max_retries and not success:
                try:
                    success = self.client.update_memory(
                        assistant_message_id=assistant_message_id,
                        agent_id=agent_id,
                        new_retrieved_facts=facts,
                        user_id=user_id,
                        model=model,
                        token=token
                    )
                    if success:
                        break
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.info(f"记忆更新重试 ({retry_count}/{max_retries})...")
                        time.sleep(1)  # 等待1秒后重试
                except Exception as e:
                    logger.error(f"记忆更新重试 {retry_count + 1} 时出错: {str(e)}")
                    retry_count += 1
                    if retry_count < max_retries:
                        time.sleep(1)  # 等待1秒后重试
            
            if not success and retry_count >= max_retries:
                logger.warning(f"记忆更新重试耗尽 ({max_retries}次尝试后失败)")
            
            return success
        except Exception as e:
            logger.error(f"更新记忆时出错: {str(e)}")
            return False
            
    def get_memory(self, user_id: str, agent_id: str, token: str = None) -> Optional[Dict[str, Any]]:
        """
        获取特定用户和代理的记忆。
        
        参数:
            user_id: 用户ID
            agent_id: Agent ID
            token: 认证令牌
            
        返回:
            Dict[str, Any] 或 None: 记忆数据，如果未找到则为None
        """
        try:
            return self.client.get_memory(user_id, agent_id, token)
        except Exception as e:
            logger.error(f"获取记忆时出错: {str(e)}")
            return None