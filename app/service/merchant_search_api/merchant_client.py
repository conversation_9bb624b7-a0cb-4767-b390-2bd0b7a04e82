import requests

from utils.logger import logger
from configs.config import MERCHANT_DATA_URL

# Merchant API Field Descriptions
MERCHANT_FIELDS_DESCRIPTION = [
    "WM_POI_FIELD_WM_POI_ID",
    "WM_POI_FIELD_POI_ID",
    "WM_POI_FIELD_CITY_ID",
    "WM_POI_FIELD_NAME",
    "WM_POI_FIELD_ADDRESS",
    "WM_POI_FIELD_LATITUDE",
    "WM_POI_FIELD_LONGITUDE",
    "WM_POI_FIELD_OWNER_UID",
    "WM_POI_FIELD_AOR_ID",
    "WM_POI_FIELD_PIC_URL",
    "WM_POI_FIELD_PIC_URL_SECOND",
    "WM_POI_FIELD_CALL_CENTER",
    "WM_POI_FIELD_ECOM_ACCOUNT_PHONENUM",
    "WM_POI_FIELD_BULLETIN",
    "WM_POI_FIELD_POI_CONFIRM",
    "WM_POI_FIELD_PRE_BOOK",
    "WM_POI_FIELD_TIME_SELECT",
    "WM_POI_FIELD_ACROSS_DAY",
    "WM_POI_FIELD_INVOICE_SUPPORT",
    "WM_POI_FIELD_INVOICE_MIN_PRICE",
    "WM_POI_FIELD_INVOICE_DESCRIPTION",
    "WM_POI_FIELD_SHIPPING_TIME_X",
    "WM_POI_FIELD_VALID",
    "WM_POI_FIELD_STATUS_RESTORE_UTIME",
    "WM_POI_FIELD_STATUS_RESTORE_MAX_TIME",
    "WM_POI_FIELD_SUPPORT_PAY",
    "WM_POI_FIELD_SUPPORT_FACE2FACE_PAY",
    "WM_POI_FIELD_SELF_SERVICE",
    "WM_POI_FIELD_IS_DELETE",
    "WM_POI_FIELD_SOURCE_ID",
    "WM_POI_FIELD_SOURCE",
    "WM_POI_FIELD_WM_TEMP_POI_ID",
    "WM_POI_FIELD_EC_LABEL",
    "WM_POI_FIELD_BATCH_AUDIT_COMMITTED",
    "WM_POI_FIELD_ONLINE_TIME",
    "WM_POI_FIELD_AGENT_ID",
    "WM_POI_FIELD_REST_TYPE",
    "WM_POI_FIELD_OFFLINE_TYPE",
    "WM_POI_FIELD_REST_REASON",
    "WM_POI_FIELD_OFFLINE_REASON",
    "WM_POI_FIELD_BRAND_ID",
    "WM_POI_FIELD_OWNER_TYPE",
    "WM_POI_FIELD_RECEIVEORDER_TYPE",
    "WM_POI_FIELD_WM_LOGISTICS_IDS",
    "WM_POI_FIELD_BM_AREA_ID",
    "WM_POI_FIELD_LOGISTICS_AHEAD",
    "WM_POI_FIELD_TAG_IDS",
    "WM_POI_FIELD_PRIMARY_TAG_ID",
    "WM_POI_FIELD_QUALI_TYPE",
    "WM_POI_FIELD_FIRST_ONLINE_TIME",
    "WM_POI_FIELD_WEIGHT_TIME",
    "WM_POI_FIELD_MASTER_ID",
    "WM_POI_FIELD_DP_ID",
    "WM_POI_FIELD_AVG_DELIVERY_TIME",
    "WM_POI_FIELD_MONTH_SALE_NUM",
    "WM_POI_FIELD_WM_POI_SCORE",
    "WM_POI_FIELD_CTIME",
    "WM_POI_FIELD_UTIME",
    "WM_POI_FIELD_UTIME_POI",
    "WM_POI_FIELD_WM_POI_QUA_INFO_LIST",
    "WM_POI_FIELD_WM_POI_QUA_CONF",
    "WM_POI_FIELD_WDC_TAG_TYPE",
    "WM_POI_FIELD_FIRST_TAG",
    "WM_POI_FIELD_QUA_CONF_CHANNEL",
    "WM_POI_FIELD_QUA_CONF_TYPE",
    "WM_POI_FIELD_LOCATION_ID",
    "WM_FOURTH_CITY_ID",
    "WM_POI_FIELD_LOGISTICS_PRODUCT_PHF",
    "WM_POI_FIELD_PHF_FIRST_ONLINE_TIME",
    "WM_POI_FIELD_ORIGIN_BRAND_ID",
    "WM_POI_FIELD_LABEL_IDS",
    "WM_POI_FIELD_WM_POI_UNDER_LINE_ADVANCE",
    "WM_POI_FIELD_STORY_ID",
    "WM_POI_FIELD_STORY_STATUS",
    "WM_POI_FIELD_SPU_IDS",
    "WM_POI_FIELD_SPEED_REFUND_STATUS",
    "WM_POI_FIELD_SPEED_REFUND_OPENTIME",
    "WM_POI_FIELD_HAS_WEIGHT_QUA",
    "WM_POI_FIELD_TOTAL_BUSINESS_TIME",
    "WM_LOGISTICS_TYPE",
    "WM_POI_FIELD_BIZ_ORG_CODE",
    "WM_POI_FIELD_BUSINESS_NAME",
    "WM_POI_FIELD_CONTRACT_BANKID",
    "WM_POI_FIELD_TRANS_COUNT",
    "WM_POI_FIELD_SCM_VERSION",
    "WM_POI_FIELD_CUSTOMER_ID",
    "WM_POI_FIELD_PARTY_A_PEOPLE",
    "WM_POI_FIELD_PARTY_A_PHONE",
    "WM_POI_FIELD_EPP_JOIN_STATUS",
    "WM_POI_FIELD_EPP_JOIN_MODE",
    "WM_POI_FIELD_EPP_PER_ORDER_DONATION_AMOUNT",
    "WM_POI_FIELD_KP_COMPELLATION",
    "WM_POI_FIELD_PHONE_NUM_TOKEN",
    "WM_POI_FIELD_INSURANCE_INFO",
    "WM_POI_FIELD_INSURANCE_TYPES",
    "WM_POI_FIELD_IS_SUNSHINE",
    "WM_POI_FIELD_CONTACTS",
    "WM_POI_FIELD_PALACE_ID",
    "WM_POI_FIELD_MEDIA_URLS",
    "WM_POI_FIELD_SLA_LEAD_TIME",
    "WM_POI_FIELD_SLA_PACKAGE_TYPE",
    "WM_POI_FIELD_PAYMENT_MERCHANT_ID",
    "WM_POI_FIELD_IS_COMPLIANCE_SIGN_COMPLETED",
    "WM_POI_FIELD_MEAL_LOSS_PERCENT",
    "WM_POI_FIELD_MT_CUSTOMER_ID",
    "WM_POI_FIELD_CDA_STATUS",
    "WM_POI_FIELD_SHIPPING_TIME_SPECIAL",
    "WM_POI_FIELD_SHIPPING_TIME_INTERSECTION",
    "SETTLE_ID",
    "LOGISTICS_FEE_MODE",
    "SPECIAL_BUSINESS",
    "ADDRESS_REMARK",
    "PUBLICITY_PERIOD_STATUS",
    "PUBLICITY_PERIOD_TIME",
    "WM_POI_FIELD_PARENT_WM_POI_ID",
    "WM_POI_FIELD_SUB_WM_POI_TYPE",
    "WM_POI_FIELD_SELF_DISPATCH_DURATION",
    "WM_POI_FIELD_USE_SELF_DURATION",
    "WM_POI_FIELD_NEED_COMPLIANCE",
    "WM_POI_FIELD_DELIVERY_DEALER_INFO",
    "WM_POI_FIELD_DIAGNOSE_SCORE",
    "WM_POI_FIELD_LOGISTICS_PRODUCT",
    "WM_POI_FIELD_HEAD_SCULPTURE_TYPE",
    "WM_POI_FIELD_BRAND_PRICE_MODE",
    "WM_POI_FIELD_LOGISTICS_FEE_MODES",
    "WM_POI_FIELD_SELF_PICKUP_SUPPORT",
    "WM_POI_FIELD_LEAD_TIME",
    "WM_POI_FIEDL_SELF_PICKUP_PROMOTION_SUPPORT",
    "WM_POI_FIELD_IS_XBK",
    "WM_POI_FIELD_SELLER_UID",
    "WM_POI_FIELD_POI_BIZ_TYPES",
    "WM_POI_FIELD_MEDICINE_POI_TYPE",
    "WM_POI_FIELD_BRAND_SHOW_FLAG",
    "WM_POI_FIELD_PHF_AOR_ID",
    "WM_POI_FIELD_PHF_STATUS",
    "WM_POI_FIELD_BRAND_TYPE",
    "WM_POI_FIELD_POI_LEVEL_V1",
    "WM_POI_FIELD_IS_SCHOOL_CANTEEN",
    "WM_POI_FIELD_IS_SG_AGGREGATE",
    "WM_POI_FIELD_SG_AGGREGATE_TYPE",
    "WM_POI_FIELD_PHF_MAIN_SELF_PICKUP_SUPPORT",
]

merchant_field_mapping = {
    "WM_POI_FIELD_WM_POI_ID": "wm_poi_id",
    "WM_POI_FIELD_POI_ID": "poi_id",
    "WM_POI_FIELD_CITY_ID": "city_id",
    "WM_POI_FIELD_NAME": "name",
    "WM_POI_FIELD_ADDRESS": "address",
    "WM_POI_FIELD_LATITUDE": "latitude",
    "WM_POI_FIELD_LONGITUDE": "longitude",
    "WM_POI_FIELD_OWNER_UID": "owner_uid",
    "WM_POI_FIELD_AOR_ID": "aor_id",
    "WM_POI_FIELD_SELLER_UID": "seller_uid",
    "WM_POI_FIELD_PIC_URL": "pic_url",
    "WM_POI_FIELD_PIC_URL_SECOND": "pic_url_second",
    "WM_POI_FIELD_CALL_CENTER": "call_center",
    "WM_POI_FIELD_ECOM_ACCOUNT_PHONENUM": "ecom_account_phonenum",
    "WM_POI_FIELD_BULLETIN": "bulletin",
    "WM_POI_FIELD_POI_CONFIRM": "poi_confirm",
    "WM_POI_FIELD_PRE_BOOK": "pre_book",
    "WM_POI_FIELD_TIME_SELECT": "time_select",
    "WM_POI_FIELD_ACROSS_DAY": "across_day",
    "WM_POI_FIELD_INVOICE_SUPPORT": "invoice_support",
    "WM_POI_FIELD_INVOICE_MIN_PRICE": "invoice_min_price",
    "WM_POI_FIELD_INVOICE_DESCRIPTION": "invoice_description",
    "WM_POI_FIELD_USE_MT_SHIPPING_CLIENT": "use_mt_shipping_client",
    "WM_POI_FIELD_SHIPPING_TIME_X": "shipping_time_x",
    "WM_POI_FIELD_SELF_PICKUP_SUPPORT": "self_pickup_support",
    "WM_POI_FIELD_LEAD_TIME": "lead_time",
    "WM_POI_FIEDL_SELF_PICKUP_PROMOTION_SUPPORT": "self_pickup_promotion_support", # 注意：原始常量名可能存在拼写错误 "FIEDL"
    "WM_POI_FIELD_PACKET_PAY_TYPE": "packet_pay_type",
    "WM_POI_FIELD_PACKET_PRICE": "packet_price",
    "WM_POI_FIELD_VALID": "valid",
    "WM_POI_FIELD_STATUS": "status",
    "WM_POI_FIELD_STATUS_RESTORE_UTIME": "status_restore_utime",
    "WM_POI_FIELD_STATUS_RESTORE_MAX_TIME": "status_restore_max_time",
    "WM_POI_FIELD_SUPPORT_PAY": "support_pay",
    "WM_POI_FIELD_SUPPORT_FACE2FACE_PAY": "support_face2face_pay",
    "WM_POI_FIELD_SELF_SERVICE": "self_service",
    "WM_POI_FIELD_IS_DELETE": "is_delete",
    "WM_POI_FIELD_POI_WEIGHT": "poi_weight",
    "WM_POI_FIELD_SOURCE_ID": "source_id",
    "WM_POI_FIELD_SOURCE": "source",
    "WM_POI_FIELD_WM_TEMP_POI_ID": "wm_temp_poi_id",
    "WM_POI_FIELD_EC_LABEL": "ec_label",
    "WM_POI_FIELD_BATCH_AUDIT_COMMITTED": "batch_audit_committed",
    "WM_POI_FIELD_ONLINE_TIME": "online_time",
    "WM_POI_FIELD_AGENT_ID": "agent_id",
    "WM_POI_FIELD_REST_TYPE": "rest_type",
    "WM_POI_FIELD_OFFLINE_TYPE": "offline_type",
    "WM_POI_FIELD_REST_REASON": "rest_reason",
    "WM_POI_FIELD_OFFLINE_REASON": "offline_reason",
    "WM_POI_FIELD_REST_UID": "rest_uid",
    "WM_POI_FIELD_BRAND_ID": "brand_id",
    "WM_POI_FIELD_OWNER_TYPE": "owner_type",
    "WM_POI_FIELD_RECEIVEORDER_TYPE": "receiveorder_type",
    "WM_POI_FIELD_BRAND_KA": "brand_ka",
    "WM_POI_FIELD_BRAND_TYPE": "brand_type",
    "WM_POI_FIELD_WM_LOGISTICS_IDS": "wm_logistics_ids",
    "WM_POI_FIELD_BM_AREA_ID": "bm_area_id",
    "WM_POI_FIELD_LOGISTICS_AHEAD": "logistics_ahead",
    "WM_POI_FIELD_TAG_IDS": "tag_ids",
    "WM_POI_FIELD_PRIMARY_TAG_ID": "primary_tag_id",
    "WM_POI_FIELD_QUALI_TYPE": "quali_type",
    "WM_POI_FIELD_FIRST_ONLINE_TIME": "first_online_time",
    "WM_POI_FIELD_WEIGHT_TIME": "weight_time",
    "WM_POI_FIELD_MASTER_ID": "master_id",
    "WM_POI_FIELD_DP_ID": "dp_id",
    "WM_POI_FIELD_AVG_DELIVERY_TIME": "avg_delivery_time",
    "WM_POI_FIELD_MONTH_SALE_NUM": "month_sale_num",
    "WM_POI_FIELD_WM_POI_SCORE": "wm_poi_score",
    "WM_POI_FIELD_CTIME": "ctime",
    "WM_POI_FIELD_UTIME": "utime",
    "WM_POI_FIELD_UTIME_POI": "utime_poi",
    "WM_POI_FIELD_WM_POI_QUA_INFO_LIST": "wm_poi_qua_info_list",
    "WM_POI_FIELD_WM_POI_SP_AREA_LIST": "wm_poi_sp_area_list",
    "WM_POI_FIELD_WM_POI_QUA_CONF": "wm_poi_qua_conf",
    "WM_POI_FIELD_WDC_TAG_TYPE": "tag_type",
    "WM_POI_FIELD_FIRST_TAG": "first_tag_id",
    "WM_POI_FIELD_QUA_CONF_CHANNEL": "qua_conf_channel",
    "WM_POI_FIELD_QUA_CONF_TYPE": "qua_conf_type",
    "WM_POI_FIELD_HAS_QUALIFICATION": "has_qualification",
    "WM_POI_FIELD_LOCATION_ID": "location_id",
    "WM_POI_FIELD_CITY_LOCATION_ID": "city_location_id",
    "WM_POI_FIELD_ORIGIN_BRAND_ID": "origin_brand_id",
    "WM_POI_FIELD_LABEL_IDS": "label_ids",
    "WM_POI_FIELD_WM_POI_UNDER_LINE_ADVANCE": "under_line_advance",
    "WM_POI_FIELD_STORY_ID": "story_id",
    "WM_POI_FIELD_STORY_STATUS": "story_status",
    "WM_POI_FIELD_SPU_IDS": "spu_ids",
    "WM_POI_FIELD_SPEED_REFUND_STATUS": "speed_refund_status",
    "WM_POI_FIELD_SPEED_REFUND_OPENTIME": "speed_refund_opentime",
    "WM_POI_FIELD_HAS_WEIGHT_QUA": "has_weight_qua",
    "WM_POI_FIELD_TOTAL_BUSINESS_TIME": "total_business_time",
    "WM_LOGISTICS_TYPE": "wm_logistics_types",
    "WM_POI_FIELD_BIZ_ORG_CODE": "biz_org_code",
    "WM_POI_FIELD_BUSINESS_LICENCE": "business_licence",
    "WM_POI_FIELD_BUSINESS_NAME": "business_name",
    "WM_POI_FIELD_QUA_INFO": "qua_info",
    "WM_POI_FIELD_CONTRACT_EMAIL": "contract_email",
    "WM_POI_FIELD_CONTRACT_ACC_CARDNO": "contract_acc_cardno",
    "WM_POI_FIELD_CONTRACT_BANKID": "contract_bankid",
    "WM_POI_FIELD_TRANS_COUNT": "trans_count",
    "WM_FOURTH_CITY_ID": "fourth_city_id",
    "WM_POI_FIELD_SCM_VERSION": "scm_version",
    "WM_POI_FIELD_CUSTOMER_ID": "customer_id",
    "WM_POI_FIELD_PARTY_A_PEOPLE": "party_a_people",
    "WM_POI_FIELD_PARTY_A_PHONE": "party_a_phone",
    "WM_POI_FIELD_SPECIAL_BUSINESS": "special_business",
    "WM_POI_FIELD_EPP_JOIN_STATUS": "epp_join_status",
    "WM_POI_FIELD_EPP_JOIN_MODE": "epp_join_mode",
    "WM_POI_FIELD_EPP_PER_ORDER_DONATION_AMOUNT": "epp_per_order_donation_amount",
}

# 您可以使用这个字典进行查找，例如：
# field_name_in_api = merchant_field_mapping.get("WM_POI_FIELD_NAME")
# print(field_name_in_api)  # 输出: name


def get_merchant_info(merchant_id: int, fields_needed: list[str]=[]) -> dict:
    import json
    fields_needed = [
    "WM_POI_FIELD_NAME", 
    "WM_POI_FIELD_ADDRESS", 
    "WM_POI_FIELD_LATITUDE", 
    "WM_POI_FIELD_LONGITUDE", 
    "WM_POI_FIELD_STATUS",
    ]
    headers = {
        "Authorization": "Bearer 1838824241643597850",
        "Content-Type": "application/json",
    }
    try:
        headers = {
            "Authorization": "Bearer 1838824241643597850",
            "Content-Type": "application/json",
        }
        try:
            logger.info(f"请求的URL: {MERCHANT_DATA_URL}") # Test: http://*************:8080/merchant/search
            result = requests.post(MERCHANT_DATA_URL, headers=headers, json={"merchantId": merchant_id, "fieldsNeeded": fields_needed})
            data:str = result.json().get("data", {})
            ret = {}
            for field in fields_needed:
                ret[field] = data.get(merchant_field_mapping.get(field, "code"))
            return ret
        except Exception as e:
            logger.error(f"获取商家信息失败: {e}")
            return {}
    except Exception as e:
        logger.error(f"获取商家信息失败: {e}")
        return {}

def get_merchant_address(merchant_id: int) -> str:
    merchant_info = get_merchant_info(merchant_id, ["WM_POI_FIELD_ADDRESS"])
    return merchant_info.get("WM_POI_FIELD_ADDRESS", "错误的地点，遇到了正确的你。")

def get_merchant_latitude(merchant_id: int) -> float:
    merchant_info = get_merchant_info(merchant_id, ["WM_POI_FIELD_LATITUDE"])
    return merchant_info.get("latitude", 0.0)

def get_merchant_longitude(merchant_id: int) -> float:
    merchant_info = get_merchant_info(merchant_id, ["WM_POI_FIELD_LONGITUDE"])
    return merchant_info.get("longitude", 0.0)

def get_merchant_status(merchant_id: int) -> int:
    merchant_info = get_merchant_info(merchant_id, ["WM_POI_FIELD_STATUS"])
    return merchant_info.get("status", 0)