import requests
import json
import re
from typing import List, Dict, Any
from utils.logger import logger
from configs.config import MERCHANT_DATA_URL
class MerchantService:
    """商家信息服务，用于获取商家开店状态"""
    
    def __init__(self, base_url="http://10.171.59.195:8080"):
        self.base_url = base_url
        self.search_endpoint = MERCHANT_DATA_URL
        self._cache = {}  # 简单缓存，避免短时间内重复请求
        
    def check_merchant_status(self, merchant_id):
        """检查商家是否开店"""
        # 清理和验证merchant_id
        try:
            merchant_id = self._clean_merchant_id(merchant_id)
            
            # 检查缓存
            if merchant_id in self._cache:
                logger.info(f"从缓存获取商家[{merchant_id}]状态")
                return self._cache[merchant_id]
                
            # 需要的字段
            fields_needed = [
                "WM_POI_FIELD_STATUS",      # 营业状态
                "WM_POI_FIELD_VALID",       # 上线状态
                "WM_POI_FIELD_NAME",        # 门店名称
                "WM_POI_FIELD_REST_REASON"  # 置休原因
            ]
            
            # 请求数据
            request_data = {
                "merchantId": merchant_id,
                "fieldsNeeded": fields_needed
            }
            
            logger.info(f"发送请求: {json.dumps(request_data, ensure_ascii=False)}")
            
            # 发送请求
            response = requests.post(
                self.search_endpoint,
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=5
            )
            
            logger.info(f"响应状态码: {response.status_code}")
            
            if response.status_code != 200:
                logger.error(f"请求返回非200状态码: {response.status_code}")
                return self._error_response("请求失败", f"状态码: {response.status_code}")
            
            try:
                data = response.json()
                logger.info(f"响应内容: {json.dumps(data, ensure_ascii=False)}")
                
                # 检查是否是错误响应
                if "error" in data or "errorMsg" in data or ("code" in data and data.get("code") != 0):
                    error_msg = data.get("errorMsg") or data.get("error") or f"错误码: {data.get('code')}"
                    logger.error(f"API返回错误: {error_msg}")
                    return self._error_response("API返回错误", error_msg)
                
                # 检查并提取嵌套在data字段中的数据
                merchant_data = data
                if "data" in data and isinstance(data["data"], dict):
                    merchant_data = data["data"]
                    logger.info("从嵌套的data字段提取商家数据")
                elif "data" in data and data["data"] == "服务器报错":
                    logger.error(f"服务器报错: {data['data']}")
                    return self._error_response("服务器内部错误", data['data'])
                
                # 定义字段映射，处理可能的字段名差异
                field_mappings = {
                    "status": ["WM_POI_FIELD_STATUS", "status", "Status"],
                    "valid": ["WM_POI_FIELD_VALID", "valid", "Valid"],
                    "name": ["WM_POI_FIELD_NAME", "name", "Name"],
                    "rest_reason": ["WM_POI_FIELD_REST_REASON", "rest_reason", "restReason", "RestReason"]
                }
                
                # 提取商家数据
                result = {}
                for field, keys in field_mappings.items():
                    for key in keys:
                        if key in merchant_data:
                            result[field] = merchant_data[key]
                            break
                
                # 如果没有找到必要字段
                if "status" not in result and "valid" not in result:
                    logger.error("未找到商家状态信息")
                    return self._error_response("数据格式错误", "未找到商家状态信息")
                
                # 处理可能缺失的字段
                status = result.get("status")
                valid = result.get("valid")
                name = result.get("name", "未知商家")
                rest_reason = result.get("rest_reason", "")
                
                # 格式化状态
                is_open = status == 1
                status_text = "营业中" if status == 1 else "休息中" if status == 3 else "未知状态"
                
                is_online = valid == 1
                valid_text = {
                    0: "已下线",
                    1: "已上线",
                    2: "上单中",
                    3: "审核通过可上线"
                }.get(valid, "未知状态")
                
                logger.info(f"商家[{name}]状态查询: 营业状态={status_text}({status}), 上线状态={valid_text}({valid})")
                
                result = {
                    "is_open": is_open,
                    "is_online": is_online,
                    "status": status,
                    "valid": valid,
                    "status_text": status_text,
                    "valid_text": valid_text,
                    "name": name,
                    "rest_reason": rest_reason
                }
                
                # 存入缓存
                self._cache[merchant_id] = result
                return result
                
            except json.JSONDecodeError:
                logger.error(f"返回的不是有效的JSON: {response.text[:500]}")
                return self._error_response("解析失败", "返回的不是有效的JSON")
                
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP请求异常: {str(e)}")
            return self._error_response("请求异常", str(e))
        except Exception as e:
            logger.error(f"查询商家状态时出错: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return self._error_response("查询异常", str(e))

    def is_merchant_available(self, merchant_id):
        """快速检查商家是否可用（已上线且正在营业）"""
        status = self.check_merchant_status(merchant_id)
        return status.get("is_open", False) and status.get("is_online", False)
        
    def batch_check_merchant_status(self, merchant_ids: List[int]) -> Dict[int, Dict[str, Any]]:
        """批量检查多个商家状态
        
        Args:
            merchant_ids: 商家ID列表
            
        Returns:
            字典，格式为 {merchant_id: status_dict}
        """
        if not merchant_ids:
            return {}
            
        results = {}
        for merchant_id in merchant_ids:
            try:
                status = self.check_merchant_status(merchant_id)
                results[merchant_id] = status
            except Exception as e:
                logger.error(f"检查商家[{merchant_id}]状态时出错: {str(e)}")
                results[merchant_id] = self._error_response("查询异常", str(e))
                
        return results
        
    def filter_available_merchants(self, merchants: List[Dict[str, Any]], id_field="poi_id") -> List[Dict[str, Any]]:
        """过滤出可用的商家（已上线且正在营业）
        
        Args:
            merchants: 商家信息列表，每个商家信息是一个字典
            id_field: 商家ID在字典中的字段名，默认为"poi_id"
            
        Returns:
            过滤后的商家列表，仅包含可用的商家
        """
        if not merchants:
            return []
            
        # 提取所有商家ID
        merchant_ids = []
        merchant_dict = {}
        
        for merchant in merchants:
            if id_field in merchant:
                merchant_id = self._clean_merchant_id(merchant[id_field])
                merchant_ids.append(merchant_id)
                merchant_dict[merchant_id] = merchant
        
        # 批量检查商家状态
        statuses = self.batch_check_merchant_status(merchant_ids)
        
        # 过滤可用商家
        available_merchants = []
        for merchant_id, status in statuses.items():
            if status.get("is_open", False) and status.get("is_online", False):
                # 将状态信息添加到商家信息中
                merchant = merchant_dict[merchant_id].copy()
                merchant["status_info"] = status
                available_merchants.append(merchant)
                logger.info(f"商家[{status.get('name', merchant_id)}]({merchant_id})状态正常，可以推荐")
            else:
                logger.info(f"商家[{status.get('name', merchant_id)}]({merchant_id})状态异常，已过滤掉。状态: {status}")
                
        return available_merchants
        
    def get_unavailable_merchants_info(self, merchants: List[Dict[str, Any]], id_field="poi_id") -> List[Dict[str, Any]]:
        """获取不可用商家的状态信息
        
        Args:
            merchants: 商家信息列表
            id_field: 商家ID字段名
            
        Returns:
            不可用商家的状态信息列表
        """
        if not merchants:
            return []
            
        # 提取所有商家ID
        merchant_ids = []
        merchant_dict = {}
        
        for merchant in merchants:
            if id_field in merchant:
                merchant_id = self._clean_merchant_id(merchant[id_field])
                merchant_ids.append(merchant_id)
                merchant_dict[merchant_id] = merchant
        
        # 批量检查商家状态
        statuses = self.batch_check_merchant_status(merchant_ids)
        
        # 筛选不可用商家
        unavailable_merchants = []
        for merchant_id, status in statuses.items():
            if not status.get("is_open", False) or not status.get("is_online", False):
                merchant = merchant_dict[merchant_id].copy()
                merchant["status_info"] = status
                unavailable_merchants.append(merchant)
                
        return unavailable_merchants
        
    def _clean_merchant_id(self, merchant_id):
        """清理和验证商家ID"""
        if merchant_id is None:
            raise ValueError("商家ID不能为空")
            
        merchant_id = re.sub(r'[<>\s]', '', str(merchant_id).strip())
        
        if not merchant_id.isdigit():
            raise ValueError(f"商家ID必须是数字，当前值: '{merchant_id}'")
            
        return int(merchant_id)
        
    def _error_response(self, status_text, error_message):
        """生成标准化的错误响应"""
        return {
            "is_open": False,
            "is_online": False,
            "status": None,
            "valid": None,
            "status_text": status_text,
            "valid_text": status_text,
            "error": error_message
        } 