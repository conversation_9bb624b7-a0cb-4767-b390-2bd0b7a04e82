import json
import requests
import time
from utils.logger import logger
from utils.squirrel import RedisClient, build_category_key
from datetime import datetime
from utils.utils import timeit
from configs.config import (
    API_WEATHER,
    ORDER_LIST_URL,
    ORDER_DETAIL_URL,
    INGREDIENT_ANALYSIS_RESULTS_DIR,
)
from configs import lion_config
from concurrent.futures import ThreadPoolExecutor
from utils.extract import extract_price_with_currency
import concurrent

def limit_user_orders(all_orders_info: dict, limit: int = None) -> dict:
    """限制发送给AI的订单数量，只保留最近的N单

    Args:
        all_orders_info: 所有订单信息
        limit: 限制数量，如果不指定则使用lion配置中的AI_ORDER_LIMIT

    Returns:
        dict: 限制后的订单信息
    """
    if not all_orders_info:
        return all_orders_info
    logger.info(f"limit function:{limit}")
    if limit is None:
        limit = lion_config.AI_ORDER_LIMIT

    # 收集所有订单并按时间排序
    all_orders = []
    for shop_info in all_orders_info.values():
        all_orders.extend(shop_info.get("details", []))
    
    total_orders = len(all_orders)
    
    # 按订单时间排序
    all_orders.sort(key=lambda x: x.get("o_time", ""), reverse=True)
    
    # 只保留最近的N单
    limited_orders = all_orders[:limit]
    limited_count = len(limited_orders)

    # logger.info(f"all_orders: {all_orders}")
    # logger.info(f"limited_orders: {limited_orders}")
    
    logger.info(f"用户订单限制：原始订单数量={total_orders}，限制后数量={limited_count}，限制值={limit}")
    
    # 重新按商家分组
    limited_orders_info = {}
    for order in limited_orders:
        poi_name = order.get("poi_name", "")
        if poi_name not in limited_orders_info:
            limited_orders_info[poi_name] = {
                "poi_name": poi_name,
                "details": []
            }
        limited_orders_info[poi_name]["details"].append(order)
    
    return limited_orders_info

@timeit
def get_user_orders_v2(token, user_id, limit: int = 10):
    """获取用户订单历史"""
    try:
        logger.info(f"get_user_orders_v2 function:{limit}")
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/json",
            "X-Requested-With": "XMLHttpRequest",
            "Origin": "http://10.201.31.175:8080",
            "Referer": "http://10.201.31.175:8080/",
            "access-token": token,
        }
        user_squirrel_id = get_user_order_squirrel_id(user_id)
        all_orders_info = {}
        redis_client = RedisClient.get_instance().get_client()
        user_orders = redis_client.zrange(user_squirrel_id, 0, -1, desc=True, withscores=False)
        history_user_orders = convert_user_orders_to_all_orders_info(user_orders)
        last_order_time_unix = int(time.time()) - lion_config.REDIS_INIT_TIME_RANGE
        if len(user_orders) > 0:
            last_order_time = json.loads(user_orders[0])["o_time"]
            last_order_time_dt = datetime.strptime(last_order_time, "%Y-%m-%d %H:%M")
            last_order_time_unix = int(last_order_time_dt.timestamp())
            if int(time.time()) - last_order_time_unix < lion_config.REDIS_UPDATE_PER_SECONDS:
                return limit_user_orders(history_user_orders)

        logger.info("正在获取订单列表...")
        response = requests.post(
            ORDER_LIST_URL,
            headers=headers,
            json={
                "userId": user_id,  
                "offset": 0,
                "limit": lion_config.ORDER_CACHE_NUM,
                "beginTime": str(last_order_time_unix),
                "endTime": str(int(time.time())),
                "partnerIdList": [
                    6  # 外卖业务
                ]
            },
            timeout=5,
        )
        logger.info(f"response: {response}")
        logger.info(f"response text: {response.text}")

        if response.status_code != 200:
            logger.info(f"获取订单列表失败: {response}")
            return limit_user_orders(history_user_orders,limit)

        orders_data = response.json()

        if not orders_data.get("data", {}).get("orderList"):
            return limit_user_orders(history_user_orders,limit)

        new_order_list = orders_data["data"]["orderList"]

        history_order_ids = [json.loads(order).get("order_id") for order in user_orders]

        new_order_list = [
            order for order in new_order_list if order.get("orderId") not in history_order_ids
        ]

        if len(new_order_list) == 0:
            return limit_user_orders(history_user_orders,limit)

        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = {
                executor.submit(
                    fetch_order_details, order.get("orderId", ""), headers, user_id
                ): order
                for order in new_order_list
                if order.get("partnerId") == 6 and order.get("cateId") == 6
            }

            # 处理并行任务的结果
            for future in concurrent.futures.as_completed(futures):
                order = futures[future]
                try:
                    items = future.result()
                    if items is not None:
                        order_info = json.loads(order.get("info", "{}"))
                        order_time = order_info.get("info1", "").replace(
                            "下单时间：", ""
                        )
                        order_status = order_info.get("allStatus", 0)
                        order_currency, order_price = extract_price_with_currency(
                            order_info.get("info2", "")
                        )

                        order_data = {
                            "o_time": order_time,
                            "items": items,
                            "poi_name": order.get("title", ""),
                            "status": order_status,
                            "order_id": order.get("orderId", ""),
                            "total_price": order_price,
                            "total_currency": order_currency,
                        }
                        all_orders_info[order.get("title", "")] = {
                            "poi_name": order.get("title", ""),
                            "details": [],
                        }
                        all_orders_info[order.get("title", "")]["details"].append(
                            order_data
                        )
                        order_time_dt = datetime.strptime(order_time, "%Y-%m-%d %H:%M")
                        order_time_unix = int(order_time_dt.timestamp())
                        order_data_json = json.dumps(order_data)
                        redis_client.zadd(user_squirrel_id, order_data_json, order_time_unix)
                        if redis_client.zcard(user_squirrel_id) > lion_config.ORDER_CACHE_NUM:
                            redis_client.zremrangebyrank(user_id, 0, 0)
                except Exception as e:
                    logger.error(
                        f"处理订单 {order.get('orderId', '')} 的并行任务结果时出错: {str(e)}"
                    )
        return limit_user_orders(merge_old_and_new_orders(history_user_orders, all_orders_info),limit)

    except Exception as e:
        logger.error(f"获取用户订单时出错: {str(e)}")
        return {}  # 返回空字典而不是None

async def get_user_orders_v3(token, user_id):
    """获取用户订单历史"""
    try:
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/json",
            "X-Requested-With": "XMLHttpRequest",
            "Origin": "http://10.201.31.175:8080",
            "Referer": "http://10.201.31.175:8080/",
            "access-token": token,
        }
        user_squirrel_id = get_user_order_squirrel_id(user_id)
        all_orders_info = {}
        redis_client = RedisClient.get_instance().get_client()
        user_orders = redis_client.zrange(user_squirrel_id, 0, -1, desc=True, withscores=False)
        history_user_orders = convert_user_orders_to_all_orders_info(user_orders)
        last_order_time_unix = int(time.time()) - lion_config.REDIS_INIT_TIME_RANGE
        if len(user_orders) > 0:
            last_order_time = json.loads(user_orders[0])["o_time"]
            last_order_time_dt = datetime.strptime(last_order_time, "%Y-%m-%d %H:%M")
            last_order_time_unix = int(last_order_time_dt.timestamp())
            if int(time.time()) - last_order_time_unix < lion_config.REDIS_UPDATE_PER_SECONDS:
                return limit_user_orders(history_user_orders)

        logger.info("正在获取订单列表...")
        response = requests.post(
            ORDER_LIST_URL,
            headers=headers,
            json={
                "userId": user_id,
                "offset": 0,
                "limit": lion_config.ORDER_CACHE_NUM,
                "beginTime": str(last_order_time_unix),
                "endTime": str(int(time.time())),
                "partnerIdList": [
                    6  # 外卖业务
                ]
            },
            timeout=50,
        )

        if response.status_code != 200:
            logger.info(f"获取订单列表失败: {response}")
            return limit_user_orders(history_user_orders)

        orders_data = response.json()

        if not orders_data.get("data", {}).get("orderList"):
            return limit_user_orders(history_user_orders)

        new_order_list = orders_data["data"]["orderList"]

        history_order_ids = [json.loads(order).get("order_id") for order in user_orders]

        new_order_list = [
            order for order in new_order_list if order.get("orderId") not in history_order_ids
        ]

        if len(new_order_list) == 0:
            return limit_user_orders(history_user_orders)

        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = {
                executor.submit(
                    fetch_order_details, order.get("orderId", ""), headers, user_id
                ): order
                for order in new_order_list
                if order.get("partnerId") == 6 and order.get("cateId") == 6
            }

            # 处理并行任务的结果
            for future in concurrent.futures.as_completed(futures):
                order = futures[future]
                try:
                    items = future.result()
                    if items is not None:
                        order_info = json.loads(order.get("info", "{}"))
                        order_time = order_info.get("info1", "").replace(
                            "下单时间：", ""
                        )
                        order_status = order_info.get("allStatus", 0)
                        order_currency, order_price = extract_price_with_currency(
                            order_info.get("info2", "")
                        )

                        order_data = {
                            "o_time": order_time,
                            "items": items,
                            "poi_name": order.get("title", ""),
                            "status": order_status,
                            "order_id": order.get("orderId", ""),
                            "total_price": order_price,
                            "total_currency": order_currency,
                        }
                        all_orders_info[order.get("title", "")] = {
                            "poi_name": order.get("title", ""),
                            "details": [],
                        }
                        all_orders_info[order.get("title", "")]["details"].append(
                            order_data
                        )
                        order_time_dt = datetime.strptime(order_time, "%Y-%m-%d %H:%M")
                        order_time_unix = int(order_time_dt.timestamp())
                        order_data_json = json.dumps(order_data)
                        redis_client.zadd(user_squirrel_id, order_data_json, order_time_unix)
                        if redis_client.zcard(user_squirrel_id) > lion_config.ORDER_CACHE_NUM:
                            redis_client.zremrangebyrank(user_id, 0, 0)
                except Exception as e:
                    logger.error(
                        f"处理订单 {order.get('orderId', '')} 的并行任务结果时出错: {str(e)}"
                    )
        return limit_user_orders(merge_old_and_new_orders(history_user_orders, all_orders_info))

    except Exception as e:
        logger.error(f"获取用户订单时出错: {str(e)}")
        return {}  # 返回空字典而不是None


def fetch_order_details(order_id, headers, user_id):
    """获取订单商品详情"""
    try:
        detail_response = requests.post(
            ORDER_DETAIL_URL,
            headers=headers,
            json={"userId": user_id, "viewId": order_id},
            timeout=10,
        )

        if detail_response.status_code == 200:
            outer_data = detail_response.json()
            if outer_data.get("status") == 0 and outer_data.get("data"):
                items_data = json.loads(outer_data["data"])
                if items_data and items_data.get("wmOrderDetailResults"):
                    items = []
                    for item in items_data["wmOrderDetailResults"]:
                        items.append(
                            {
                                "food_name": item.get("food_name", ""),
                                "count": item.get("count", 1),
                                "price": item.get("food_price", 0),
                                "origin_food_price": item.get("origin_food_price", 0),
                            }
                        )
                    return items
    except Exception as e:
        logger.info(f"解析订单 {order_id} 的商品详情失败: {str(e)}")
    return None


def query_user_orders_by_user_id(user_id):
    """根据用户ID查询用户订单"""
    redis_client = RedisClient.get_instance().get_client()
    user_squirrel_id = get_user_order_squirrel_id(user_id)
    user_orders = redis_client.zrange(user_squirrel_id, 0, -1, desc=True, withscores=False)
    return user_orders


def remove_orders_by_user_id(user_id):
    """根据用户ID删除用户订单"""
    redis_client = RedisClient.get_instance().get_client()
    user_squirrel_id = get_user_order_squirrel_id(user_id)
    redis_client.delete(user_squirrel_id)


def get_user_order_squirrel_id(user_id):
    return build_category_key("user_orders", "u{0}", user_id)


def convert_user_orders_to_all_orders_info(user_orders):
    all_orders_info = {}
    for order in user_orders:
        order = json.loads(order)
        poi_name = order.get("poi_name", "")
        if poi_name not in all_orders_info:
            all_orders_info[poi_name] = {"poi_name": poi_name, "details": []}
        all_orders_info[poi_name]["details"].append(order)
    return all_orders_info

def merge_old_and_new_orders(history_user_orders, all_orders_info):
    for poi_name, orders in all_orders_info.items():
        if poi_name in history_user_orders:
            history_user_orders[poi_name]["details"].extend(orders["details"])
        else:
            history_user_orders[poi_name] = orders
    return history_user_orders

def get_order_details(access_token, userId, viewId)->list[dict]:
    try:
        OrderDetailsUrl = "https://aigc.sankuai.com/web/v1/order/getOrderGroupResultByViewIdAndUserIdWithParam"
        headers = {
                    "Authorization": "Bearer 1838824241643597850",
                    "Content-Type": "application/json",
                    "access-token": access_token
                }
        reqeust_body = {
            "userId":userId,
            "viewId":viewId
            }
        response = requests.post(OrderDetailsUrl, headers=headers, json=reqeust_body)
        responseInfo = response.json()
        data = responseInfo.get('data', "")
        
        return json.loads(data).get("wmOrderDetailResults", [])
    except Exception as e:
        logger.error(f"获取订单详情失败: {e}")
        return []

def get_order_summary(access_token, user_id, begin_time=None, end_time=None, offset=0, limit=20)->list[int, list[dict]]:
    import time
    if begin_time is None:
        begin_time = int(time.time()) - 3600 * 3
    if end_time is None:
        end_time = int(time.time())
    request_body = {
        "userId": user_id,
        "beginTime": begin_time,
        "endTime": end_time,
        "offset": offset,
        "limit": limit, # 数目
        "partnerIdList": [6] # 外卖
    }
    headers = {
                "Authorization": "Bearer 1838824241643597850",
                "Content-Type": "application/json",
                "access-token": access_token
            }
    OrderInfoUrl = "https://aigc.sankuai.com/web/v1/order/orderList"
    response = requests.post(OrderInfoUrl, headers=headers, json=request_body)
    responseInfo = response.json()
    data = responseInfo.get('data', {})
    orderNum = int(data.get("total", 0))
    orderList = data.get("orderList", [])
    if orderNum == len(orderList):
        return orderNum, orderList
    else:
        logger.info(f"Failed to get order summary for user {user_id}")
        return 0, []

if __name__ == "__main__":
    my_token = "eAGFj71KA0EYRRlsYmwklZ1bWMSAm5lvZjMzViqIlgEFwUbmFyGQFTYhTQqTwsImELDQSkIQsTCFgjY25jEsBCUasbMWcUWsfYBzz7kZNP3Ru5oK3o8-354BslRjYYBERi4GhIAXApS30jJrtRRYFBQQ4nQkReRXHlFucsvpDeOqrhm0OuejF8gj-BcUP8oltH7_New_QblzMbx7gDYKs9llY-J6tVZu2FxufH026tyMBgevp63xZX88aM2gYP_2eD7_C3RR5k9-gsK0z3kFkgIHScBQDwaLUgkrpjHFfodwBhGjEQfGRQ_NNZxucie8MulbjIERwoQCajQjzHpLIym3AwfGKevAywJninGtSzKdl1qlHDG8jWaLsarXdiHExb2KcUVljEuShVpccdVwbXXzEE0kSfwN8Eh6Ng**eAENyMkBwDAIA7CVqMEpGYdz_xFaPfWeZTdAHzYkezqP8taTHij_h5oBES1X9B5TtRsmw9jdD0fsEgU**n6xm_Z0GQNSXhBJH9wtjtetnGoibumfloMHnrhQuot-qxFb5DNrxsSOoLsF5nuWviEIk9rZMqR2AeHbHL3q7Bg**MjM5MDg3MjIsbGl1bGluZ2ZlbmcwNSzliJjlh4zplIssbGl1bGluZ2ZlbmcwNUBtZWl0dWFuLmNvbSwxLDM0MTkxODI3LDE3NDQ3ODQyNjM5MTU"
    print("sunhaiyue:",get_user_orders_v2(my_token, 3577324110,100))
    print("zhangzheng51:",get_user_orders_v2(my_token, 1423699186,100))
    print("liulingfeng05:",get_user_orders_v2(my_token, 3343136914,100))