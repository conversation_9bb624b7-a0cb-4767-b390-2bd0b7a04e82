import json
from service import request_aigc, context
from typing import Optional

from configs import lion_config
from utils.extract import parse_user_response, fill_shop_dict
from utils.threaded_generator import ThreadedGenerator
from utils.logger import logger
from service.insight import get_insight_keywords_sql
from collections import defaultdict
import vex.vex_thrift as vex
from service import ingredient
from service import context
import json
from service.user_portrait import search_user_portrait, upload_to_es, get_user_portrait
from service.ai_client import send_to_ai
from es import es_client
from utils.location_utils import calculate_distance
# from rerank import rerank_info_plus

# 暂时弃用
def get_intent_from_es(intent:str|list[str]) -> list:
    try:
        if isinstance(intent, list):
            query = {
                "query": 
                    {
                        "bool": 
                        {
                            "should": [{"match_phrase": {"intent_text": i}} for i in intent],
                            "minimum_should_match": 1
                        }
                    },
                "size": 1
            }
        elif isinstance(intent, str):
            query = {
                        "query": 
                            {
                                "bool": 
                                {
                                    "must": [{"match": {"intent_text": intent}}]
                                }
                            },
                        "size": 1
                    }
            
        else:
            logger.error(f"从es提取意图失败，格式错误: Expected 'str' or 'list[str]' , but got {type(intent)}")
            return []
        res = es_client.search(lion_config.INDEX_INTENT_KEYWORD, query)
        search_result = res["hits"]["hits"]
        intent_list = []
        for hit in search_result:
            intent_list.append(hit['_source']['intent_keyword'])
            intent_list.extend(json.loads(hit['_source']['extended_intent']))
        return intent_list
    except Exception as e:
        logger.error(f"搜索es失败: {str(e)}")
        return []

def get_user_intent(messages: list[dict['role':str, 'content':str]], user_id: int, memory_content: str, mis_id: str = "") -> list|None:
    """获取用户意图

    Args:
        messages: 消息列表
        user_id: 用户ID
        mis_id: 美团内部ID

    Returns:
        list: 意图列表
    """
    def extract_intent_from_str(intent:str)->list:
        try:
            text_dict = json.loads(intent)
            text_list = []
            text_list.extend(list(text_dict.keys()))
            for values in text_dict.values():
                if isinstance(values, list):
                    text_list.extend(values)
            
            intent = text_list[:lion_config.INTENT_NUM]
            logger.info(f"模型提取的用户意图解析结果: {intent}")
            return intent
        except Exception as e:
            logger.info(f"模型提取的用户意图解析结果为空， 错误信息为: {str(e)}")
            return []

    # mini 微型对话函数，如此可以看出流式返回有多么臃肿
    def get_intent_from_ai(formatted_messages):
        query_input = {
            "model": lion_config.INTENT_MODEL,
            "messages": formatted_messages,
            "stream": False,
            "temperature": 0.0,
            "max_tokens": 4000,
        }
        response = send_to_ai(query_input)
        json_data = json.loads(response.text)
        logger.info(f"模型直接返回的的结果: {json_data}")
        return json_data["choices"][0]["message"]["content"]

    try:
        # 获取用户画像
        es_user_portrait = ""
        if mis_id:
            try:
                from service.user_portrait import search_user_portrait
                portrait_result = search_user_portrait(mis_id, str(user_id))
                if portrait_result:
                    es_user_portrait = portrait_result.get("portrait", "")
                    logger.info(f"成功获取用户画像用于意图解析，用户ID: {user_id}, MIS ID: {mis_id}")
            except Exception as e:
                logger.error(f"获取用户画像时发生错误: {str(e)}")
            
        formatted_messages = [{
            'role': 'system',
            'content': lion_config.INTENT_PROMPT
        }]

        # 如果没有消息，直接返回空意图
        if not messages:
            return get_intent_from_ai(formatted_messages)
            
        # 获取最近的对话历史
        context_content = "以下是最近的对话历史（仅供参考）：\n\n"
        
        # 保留最后10条消息或更少
        recent_messages = messages[-10:]
        
        # 构建对话历史
        last_user_msg = None
        last_assistant_msg = None
        dialog_pairs = []
        
        for msg in recent_messages:
            if msg['role'] == 'user':
                if last_user_msg and last_assistant_msg:
                    dialog_pairs.append((last_user_msg, last_assistant_msg))
                last_user_msg = msg['content']
                last_assistant_msg = None
            elif msg['role'] == 'assistant':
                if last_user_msg:
                    last_assistant_msg = msg['content']
        
        # 如果最后一组对话不完整，但有用户消息，也加入对话对
        if last_user_msg and last_assistant_msg:
            dialog_pairs.append((last_user_msg, last_assistant_msg))
            
        # 只保留最近的5对对话
        dialog_pairs = dialog_pairs[-5:]
        
        # 构建对话历史文本
        for user_msg, assistant_msg in dialog_pairs:
            context_content += f"用户：{user_msg}\n助手：{assistant_msg}\n\n"
            
        # 添加当前用户的最新消息（如果最后一条是用户消息）
        if recent_messages and recent_messages[-1]['role'] == 'user':
            context_content += f"当前用户问题：{recent_messages[-1]['content']}"
        
        formatted_messages.append({
            'role': 'user',
            'content': context_content
        })
        
        logger.info(f"发送给意图模型的消息：{formatted_messages}")        
        aigc_response = get_intent_from_ai(formatted_messages)
        aigc_intent = extract_intent_from_str(aigc_response)
        return aigc_intent
    except Exception as e:
        logger.error(f"系统， 用户{user_id} 获取用户意图时出错: {str(e)}")
        return None



def chat_with_rag(intent: list, history: list[dict['content':str]], current_user_id: int,
                  generator: ThreadedGenerator, tts_session_id: None, user_orders=None,
                  user_feature: str = "", personal_reply: str = "", memory_content: str= "", mis_id: str = "",
                  location_query: bool = False, order_count_query: bool = False) -> None:
    """处理更多数据请求

    Args:
        memory_content: 记忆内容
        intent: 用户意图
        history: 聊天历史记录
        current_user_id: 当前用户ID
        generator: 生成器实例
        user_orders: 用户订单数据
        tts_session_id: tts会话id
        user_feature: 用户画像
        personal_reply: 回复规则
        mis_id: 美团内部ID
        location_query: 是否查询商家地址经纬度
        order_count_query: 是否查询商家月订单数量
    """
    try:
        # 初始化ES用户画像
        es_user_portrait = ""

        # 处理用户画像
        if mis_id:
            try:
                # 使用传入的mis_id和current_user_id
                portrait_result = search_user_portrait(mis_id, str(current_user_id))
                if not portrait_result:
                    # 如果没有找到画像，生成并上传
                    logger.info(f"未找到用户画像，开始生成新画像，用户ID: {current_user_id}, MIS ID: {mis_id}")
                    portrait = get_user_portrait(mis_id, user_orders, memory_content)  # 使用mis_id获取用户画像
                    if portrait:
                        # 上传到ES
                        upload_result = upload_to_es(mis_id, str(current_user_id), portrait)
                        if upload_result:
                            logger.info(f"成功生成并上传用户画像，用户ID: {current_user_id}, MIS ID: {mis_id}")
                            es_user_portrait = portrait
                else:
                    logger.info(f"成功找到现有用户画像，用户ID: {current_user_id}, MIS ID: {mis_id}")
                    es_user_portrait = portrait_result.get("portrait", "")
            except Exception as e:
                logger.error(f"处理用户画像时发生错误: {str(e)}")

        user_last_input = parse_user_response(history)
        logger.info(f"type INtent : {type(intent)}, :{intent}")
            
        # 调用search函数时传递所有参数
        analysis_result = search(
            intent if isinstance(intent, list) else [intent], 
            current_user_id, 
            user_orders=user_orders, 
            location_query=location_query,
            order_count_query=order_count_query
        )
        insight_result = get_insight_keywords_sql(intent, topk=lion_config.INSIGHT_TOPK)

        user_last_input = history[-1]["content"]
        logger.info(f"RAG : {lion_config.CHAT_PROMPT_TEMPLATE}")
        prompt_content = lion_config.CHAT_PROMPT_TEMPLATE.format(
            user_input=user_last_input,
            time_info=context.get_time_info(),
            weather_info=context.get_weather_prompt(),
            analysis=analysis_result,
            thought_chains=lion_config.CONSUMER_THOUGHT_CHAINS_MAIN,
            insight_result=insight_result,
            memory_content = memory_content
        )

        # 构建系统提示词，包含用户画像和ES中的用户画像
        system_prompt = lion_config.SYSTEM_PROMPT
        if user_feature:
            system_prompt += f"\n用户画像：{user_feature}"
        if es_user_portrait:
            system_prompt += f"\nES用户画像：{es_user_portrait}"
        if personal_reply:
            system_prompt += f"\n回复规则：{personal_reply}"

        history_tmp = [{
            "role": "system",
            "content": system_prompt
        }] + history[:-1] + [
                          {
                              "role": "user",
                              "content": prompt_content
                          }
                      ]
        
        request_aigc.send(history_tmp, tts_session_id, current_user_id, generator)
    except Exception as e:
        logger.error(f"用户{current_user_id} 处理推荐对话时出错: {str(e)}")
        logger.exception(e)

def search(intent: list, current_user_id: int, user_orders=None, location_query: bool = False, order_count_query: bool = False) -> Optional[str]:
    """处理分析请求

    Args:
        intent: 模型返回的rag 输入
        current_user_id: 当前用户ID
        user_orders: 用户订单数据
        location_query: 是否查询商家地址经纬度
        order_count_query: 是否查询商家月订单数量

    Returns:
        str or None: 分析结果
    """
    try:

        # ============================ Vex数据库相关 ============================
        try:
            results:list[dict] = vex.search(intent, lion_config.VEX_SEARCH_NUM)
            visual_result = ""
            for result in results:
                visual_result += f"{result['value']} similarity: {result['score']}\n"
            merchants_dict, ignore_info = fill_shop_dict(results) # 按照商店的名称处理召回信息
            parse_visual_result = ''
            for shop_name in merchants_dict:
                parse_visual_result += f"<merchants_name>{shop_name}</merchants_name>\n"
                for order_dict in merchants_dict[shop_name]:
                    order_content = order_dict['content']
                    parse_visual_result += f"\t<order>{order_content}</order>\n"
            logger.info(f"vex召回解析结果: {parse_visual_result}")
            ignore = '\n'.join(ignore_info)
            logger.info(f"Vex无法解析的结果: {ignore}")
        except Exception as e:
            logger.error(f"用户{current_user_id} 处理vex召回时出错: {str(e)}")
            merchants_dict = {}
            ignore_info = []
        # ======================================================================

        # ============================ ES数据库相关 ============================
        try:
            es_result = ingredient.match_ingredient_list(intent, limit=lion_config.ES_MATCH_NUM)
            es_result = "\n\n".join(es_result)
            logger.info(f"es查询结果：{es_result}")
        except Exception as e:
            logger.error(f"用户{current_user_id} 处理es召回时出错: {str(e)}")
            es_merchants_dict = {}
            es_ignore_info = []

        # 处理用户历史订单的商家信息和订单数量
        merchant_stats = {}
        merchant_locations = {}
        merchant_positions = {}  # 新增：存储商家经纬度
        merchant_month_nums = {} # 新增：存储商家月订单数量
        structured_orders = []
        
        # 从ES中获取的商家信息缓存
        es_merchant_info_cache = {}
        
        if user_orders:
            try:
                # 先收集需要从ES查询的商家ID
                merchant_ids = set()
                
                for shop_name, shop_data in user_orders.items():
                    for order in shop_data.get("details", []):
                        # 如果订单中有poiId，收集起来
                        if "poi_id" in order:
                            merchant_ids.add(order["poi_id"])
                
                # 从ES批量查询商家信息
                if merchant_ids:
                    try:
                        logger.info(f"从ES查询商家信息，商家ID数量: {len(merchant_ids)}")
                        # 构建ES查询，使用es_client中的方法
                        from app.es.es_client import search as es_search, INDEX_MERCHANT_INFO
                        
                        # 构建查询条件
                        merchant_query = {
                            "query": {
                                "terms": {
                                    "poi_id": list(merchant_ids)
                                }
                            },
                            "size": len(merchant_ids)
                        }
                        
                        # 直接使用es_client.search方法查询
                        es_merchant_response = es_search(INDEX_MERCHANT_INFO, merchant_query)
                        
                        # 处理查询结果
                        if es_merchant_response and 'hits' in es_merchant_response:
                            for hit in es_merchant_response.get("hits", {}).get("hits", []):
                                source = hit.get("_source", {})
                                poi_id = source.get("poi_id")
                                if poi_id:
                                    es_merchant_info_cache[poi_id] = source
                                    
                                    # 如果需要查询经纬度并且商家信息中有position字段
                                    if location_query and "position" in source:
                                        position = source.get("position", {})
                                        if "latitude" in position and "longitude" in position:
                                            merchant_positions[poi_id] = {
                                                "latitude": position["latitude"],
                                                "longitude": position["longitude"]
                                            }
                                    
                                    # 如果需要查询月订单数量并且商家信息中有month_num字段
                                    if order_count_query and "month_num" in source:
                                        merchant_month_nums[poi_id] = source["month_num"]
                            
                            logger.info(f"成功从ES获取商家信息: {len(es_merchant_info_cache)}/{len(merchant_ids)}")
                            
                            # 记录位置和订单数量查询结果
                            if location_query:
                                logger.info(f"成功获取商家位置信息: {len(merchant_positions)}/{len(merchant_ids)}")
                            if order_count_query:
                                logger.info(f"成功获取商家月订单数量: {len(merchant_month_nums)}/{len(merchant_ids)}")
                        else:
                            logger.warning(f"ES商家查询返回为空或格式异常")
                    except Exception as e:
                        logger.error(f"从ES查询商家信息时出错: {str(e)}")
                        logger.exception(e)
                
                # 遍历用户订单，只处理商家地址和订单数量
                for shop_name, shop_data in user_orders.items():
                    # 统计订单数量
                    order_count = len(shop_data.get("details", []))
                    merchant_stats[shop_name] = order_count
                    
                    # 处理每个订单详情，尝试获取地址信息
                    for order in shop_data.get("details", []):
                        # 从订单中获取位置信息 - 修改为使用position
                        poi_id = order.get("poi_id")
                        
                        # 从ES获取position信息（优先使用）
                        if poi_id and poi_id in merchant_positions:
                            # 已经在上面获取到了position信息，不需要额外处理
                            pass
                        else:
                            # 如果订单中有position字段，也可以使用
                            if "position" in order and poi_id:
                                position_data = order.get("position", {})
                                if "latitude" in position_data and "longitude" in position_data:
                                    merchant_positions[poi_id] = {
                                        "latitude": position_data["latitude"],
                                        "longitude": position_data["longitude"]
                                    }
                        
                        # 为了兼容性，仍然保留地址文本信息
                        if "address" in order and shop_name not in merchant_locations:
                            merchant_locations[shop_name] = order["address"]
                        
                        # 从ES补充商家地址文本信息
                        if poi_id and poi_id in es_merchant_info_cache and shop_name not in merchant_locations:
                            es_merchant_info = es_merchant_info_cache[poi_id]
                            # 补充商家位置文本
                            if "address" in es_merchant_info:
                                merchant_locations[shop_name] = es_merchant_info["address"]
                        
                        # 生成简化的结构化订单信息
                        order_time = order.get("o_time", "未知时间")
                        total_price = order.get("total_price", "")
                        total_currency = order.get("total_currency", "¥")
                        price_str = f"{total_currency}{total_price}" if total_price else "未知价格"
                        
                        # 使用地址文本（如果有）
                        position_text = merchant_locations.get(shop_name, "未知位置")
                        order_sentence = f"<时间:{order_time}> <店铺:{shop_name}> <位置:{position_text}> <总价:{price_str}>"
                        
                        # 添加商品信息
                        items = order.get("items", [])
                        for item in items:
                            food_name = item.get("food_name", "")
                            food_price = item.get("price", 0)
                            if food_name:
                                order_sentence += f" <{food_name} {food_price}元> "
                        
                        structured_orders.append(order_sentence)
                
                logger.info(f"用户历史订单商家统计: {merchant_stats}")
                logger.info(f"用户历史订单商家位置: {merchant_locations}")
            except Exception as e:
                logger.error(f"处理用户历史订单统计信息时出错: {str(e)}")
                logger.exception(e)  # 输出完整的异常堆栈，便于调试

        # 订单数据
        result_str = "下面是用户自己近期的订单数据，格式是json：\n"
        result_str += json.dumps(user_orders, ensure_ascii=False)
        result_str += "\n用户近期的订单到此结束。\n\n"
        
        # 添加结构化的用户历史订单信息
        if structured_orders:
            result_str += "用户历史订单结构化信息：\n"
            result_str += "<structured_orders>\n"
            for order_str in structured_orders:
                result_str += f"  <order>{order_str}</order>\n"
            result_str += "</structured_orders>\n\n"
        
        # 添加用户历史订单的商家统计信息
        if merchant_stats:
            result_str += "用户历史订单商家统计信息：\n"
            result_str += "<merchant_statistics>\n"
            for shop_name, count in merchant_stats.items():
                result_str += f"  <merchant name=\"{shop_name}\" order_count=\"{count}\">"
                
                # 添加文本地址信息（作为属性）
                if shop_name in merchant_locations:
                    result_str += f" address=\"{merchant_locations[shop_name]}\""
                
                # 添加经纬度信息（从用户订单中查找对应商家的poi_id）
                found_poi_id = None
                for shop_data in user_orders.values():
                    for order in shop_data.get("details", []):
                        if order.get("shop_name") == shop_name and "poi_id" in order:
                            found_poi_id = order["poi_id"]
                            break
                    if found_poi_id:
                        break
                
                # 添加position标签（包含经纬度）
                if found_poi_id and found_poi_id in merchant_positions:
                    position = merchant_positions[found_poi_id]
                    result_str += f">\n    <position latitude=\"{position['latitude']}\" longitude=\"{position['longitude']}\"/>"
                else:
                    result_str += ">"
                
                # 添加月订单数量
                if found_poi_id and found_poi_id in merchant_month_nums:
                    result_str += f"\n    <month_orders>{merchant_month_nums[found_poi_id]}</month_orders>"
                
                result_str += "\n  </merchant>\n"
            result_str += "</merchant_statistics>\n\n"

        # 热门订单
        result_str += "这是附近的热门订单数据，不是用户自己的订单。如果用户需要推荐时，请参考这些数据。格式为<content></content>，下面是具体的数据：\n"
        for shop_name in merchants_dict:
            idx = 0
            for result_dict in merchants_dict[shop_name]:
                text = result_dict['content']
                result_str += f"<content>{text}</content>\n"
                idx += 1
                if idx == lion_config.MERCHANTS_SUP_NUM:
                    break
        result_str += "附近的热门订单数据到此结束。\n\n"

        # es召回的模糊匹配的食材信息
        result_str += "下面是相关的食材信息, 请参考这些数据。格式为<content></content>下面是具体的数据：\n"
        # for shop_name in es_merchants_dict:
        #     for result_dict in es_merchants_dict[shop_name]:
        #         text = result_dict['content']
        #         result_str += f"<content>{text}</content>\n
        result_str += f"<content>{es_result}</content>\n"
        
        # for _data in es_ignore_info:
        #     result_str += f"<content>{_data}</content>\n"
        result_str += "相关的分析信息到此结束。\n\n"
        return result_str
    except Exception as e:
        logger.error(f"系统， 用户{current_user_id} " + f"处理分析请求时出错: {str(e)}")
        return None


if __name__ == "__main__":
    es_result = ['# 文昌鸡分析报告\n\n附近有3家餐厅提供含文昌鸡的商品，共有4种不同的菜品。\n在所有订单中，有8个订单包含文昌鸡类菜品。\n\n热门用餐时段：\n- 夜宵（占62.5%）\n- 晚餐（占25.0%）\n- 午餐（占12.5%）\n\n价格区间分布：\n- 100元以上：占100.0%\n\n最贵的商品（前20个，按人均价格排序）：\n- 唯真COCO·椰子鸡火锅的w竹荪文昌鸡椰子鸡4人套餐（已含蘸料）（518.0元）\n- 唯真COCO·椰子鸡火锅的w原味椰子鸡（文昌鸡半只）（176.0元）\n- 椰语江南（望京凯德mall店）的风味椰子文昌鸡（168.0元）\n- 小初色·椰子鸡火锅（未来广场店）的原味椰子鸡文昌鸡（半只）（128.0元）\n\n晚餐时段：\n- 小初色·椰子鸡火锅（未来广场店）的原味椰子鸡文昌鸡（半只）:128.0元（售出5次）\n- 唯真COCO·椰子鸡火锅的w原味椰子鸡（文昌鸡半只）:176.0元（售出1次）\n\n夜宵时段：\n- 唯真COCO·椰子鸡火锅的w竹荪文昌鸡椰子鸡4人套餐（已含蘸料）:518.0元（售出1次）\n- 椰语江南（望京凯德mall店）的风味椰子文昌鸡:168.0元（售出1次）', '从20241107 11:45到20241113 10:10，煲仔皇煲仔饭(六佰本店)（城市编码：110100）销售了2份这样的订单：一次性餐具包（0.0元）、菌菇鲍粹煲+海风椰子鸡炖盅套餐（39.9元）等2个商品，订单平均总价39.9元。']

    # print(es_result[0])
    shop_dict, ignore_info = fill_shop_dict(es_result)
    print("shop_dict: ", shop_dict)
    print("ignore_info: ", ignore_info)