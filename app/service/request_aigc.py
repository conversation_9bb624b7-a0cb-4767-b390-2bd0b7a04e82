import base64
import json


import requests

from typing import Optional
import json


from service.tts import TtsClient, save_audio_to_wav
from utils.threaded_generator import ThreadedGenerator
from utils.squirrel import RedisClient, build_category_key
from utils.logger import logger
from utils.utils import timeit
from configs import lion_config
from service.COT.core import CoT


@timeit
def send(messages: list[dict['role': str, 'content': str]], tts_session_id: None|int, user_id: int = -1,
         generator: Optional[ThreadedGenerator] = None, model: str = "", model_type:str = "", token:str|None=None, pre_tools:list=None) -> Optional[str]:
    """调用美团AI接口获取回复"""
    MODLE_CONFIG = json.loads(lion_config.lion_client.get_value(f"weiwei.{model}"))
    try:
        logger.info("Chosen MOdel {}".format(model))
        logger.info("DEEPTHINK LIST {}".format(model_type))
        if model_type in lion_config.DEEPTHINKING_MODELS and (generator is not None):
            # CoT环节
            cot = CoT()
            cot_func = cot.Get_CoT(MODLE_CONFIG.get("weiwei.cot_func"))
            for cot_response in cot_func(messages, (generator is not None), tts_session_id, model, token):
                if pre_tools is not None:
                    cot_response = json.loads(cot_response)
                    cot_response["pre_tools"] = pre_tools
                    generator.send(json.dumps(cot_response, ensure_ascii=False))
                else:
                    generator.send(cot_response)
            generator.close()
        else:
            cot = CoT()
            base_func = cot.Get_Base(MODLE_CONFIG.get("weiwei.base_func"))
            if generator is not None:
                for base_response in base_func(messages, (generator is not None), tts_session_id, model, token):
                    if pre_tools is not None:
                        base_response = json.loads(base_response)
                        base_response["pre_tools"] = pre_tools
                        generator.send(json.dumps(base_response, ensure_ascii=False))
                    else:
                        generator.send(base_response)
                generator.close()
            else:
                logger.info("模型请求直接返回")
                return next(base_func(messages, (generator is not None), tts_session_id, model, token))
        return None
    except requests.Timeout:
        logger.error(f"用户{user_id} 请求超时")
    except requests.RequestException as e:
        logger.error(f"用户{user_id} 请求国外模型时，网络请求错误: {str(e)}")
    except Exception as e:
        logger.error(f"用户{user_id} 请求国外模型时，调用美团AI接口时出错: {str(e)}")

    return None
