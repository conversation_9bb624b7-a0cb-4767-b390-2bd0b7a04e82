import json
import time
from utils.logger import logger
from es import es_client
from configs import lion_config
from configs.local_config import DEFAULT_SHARE_CONTENT_GENERATION_PROMPT
import requests
import uuid
from service.ai_client import send_to_ai
from utils.merchant_food_search import search_merchant_food
from service.context import generate_meituan_link
from datetime import datetime

# ES索引名称
PLAZA_INDEX = "user_plaza_shares_test"
#PLAZA_INDEX_INFO = "plaza_shares_info_test"
user_graph_index = "weiwei_comment_by_user"
shop_graph_index = "weiwei_comment_by_shop"

def upload_to_plaza(formatted_time, title, content, mis_id, user_content=None, share_identifier=None, shop_name="", order_detail=None, money_detail=None, food_img_urls=None, shop_url=None, like_count=0, dislike_count=0, comment_count=0, comment_record=None):
    """上传用户分享内容到ES
    
    Args:
        formatted_time (str): 格式化的时间字符串
        title (str): 分享主题
        content (str): 分享内容
        mis_id (str): 美团内部ID
        user_content (str, optional): 用户自定义内容，默认与content相同
        share_identifier (str, optional): 分享标识符，用于后续查找和更新
        shop_name (str, optional): 商店名称
        order_detail (list, optional): 订单详情列表
        money_detail (list, optional): 价格详情列表
        food_img_urls (list, optional): 食物图片URL列表
        shop_url (str, optional): 商店URL
        like_count (int, optional): 点赞数，默认0
        dislike_count (int, optional): 点踩数，默认0
        comment_count (int, optional): 评论数，默认0
        comment_record (list or dict, optional): 评论记录，默认空列表
    Returns:
        bool: 是否上传成功
        str: 分享ID
    """
    try:
        # 如果未提供用户内容，使用系统生成的内容
        if user_content is None:
            user_content = content
            
        # 如果未提供订单详情和价格详情，初始化为空列表
        if order_detail is None:
            order_detail = []
        if money_detail is None:
            money_detail = []
        if food_img_urls is None:
            food_img_urls = []  
        if shop_url is None:
            shop_url = ""
        if comment_record is None:
            comment_record = {}
            
        # 自动计算评论数量
        if isinstance(comment_record, list):
            comment_count = len(comment_record)
        elif isinstance(comment_record, dict):
            comment_count = len(comment_record)
        else:
            comment_count = 0
            
        # 记录当前使用的ES系统
        logger.info(f"开始上传用户分享到ES，用户ID: {mis_id}")
        
        # 使用mis_id和uuid4生成share_id，替代之前使用索引数量的方式
        # 构造符合ES格式的数据
        max_retries = 5
        retry_count = 0
        share_id = None
        
        # 生成share_id并检查重复
        while retry_count < max_retries:
            # 生成新的share_id
            share_id = f"{mis_id}_share_{str(uuid.uuid4())}"
            
            # 检查share_id是否已存在
            existing_share = get_plaza_share_by_id(share_id, index=PLAZA_INDEX)
            if existing_share is None:
                # ID不存在，可以使用
                logger.info(f"生成新的分享ID: {share_id}")
                break
            else:
                # ID已存在，需要重新生成
                logger.warning(f"分享ID已存在，重新生成: {share_id}")
                retry_count += 1
                
        if retry_count >= max_retries:
            logger.error(f"无法生成唯一分享ID，已重试{max_retries}次")
            return False, None
        
        # 构建doc记录
        doc = {
            "time": formatted_time,
            "title": title,
            "content": content,
            "mis_id_keyword": mis_id,
            "user_content": user_content,
            # 初始化互动计数字段
            "like_count": like_count,
            "dislike_count": dislike_count,
            "comment_count": comment_count,  # 使用自动计算的评论数量
            # 初始化评论记录字段
            "comment_record": comment_record
        }
        
        # 添加订单相关信息（如果有）
        if shop_name:
            doc["shop_name"] = shop_name
        if order_detail:
            doc["order_detail"] = order_detail
        if money_detail:
            doc["money_detail"] = money_detail
        if food_img_urls:
            doc["food_img_urls"] = food_img_urls
        if shop_url:
            doc["shop_url"] = shop_url

        # 如果提供了分享标识符，添加到文档中
        if share_identifier:
            doc["share_identifier"] = share_identifier
            
        record = {
            "doc": doc,
            "doc_as_upsert": True
        }
        
        # 上传主要内容
        es_response = es_client.upsert_data(PLAZA_INDEX, share_id, record)
        
        if es_response:
            logger.info(f"用户分享上传成功，用户ID: {mis_id}，分享ID: {share_id}")
            return True, share_id
        else:
            logger.error(f"用户分享上传失败，用户ID: {mis_id}")
            return False, None
            
    except Exception as e:
        logger.error(f"上传用户分享到ES时发生错误: {str(e)}")
        return False, None

def search_plaza_shares(query_text=None, time_range=None, limit=10):
    """搜索分享内容
    
    Args:
        query_text (str, optional): 搜索关键词
        time_range (tuple, optional): 时间范围(开始时间, 结束时间)
        limit (int): 返回结果数量限制
        
    Returns:
        list: 符合条件的分享内容列表
    """
    try:
        query = {"bool": {"must": []}}
        
        # 添加文本搜索条件
        if query_text:
            query["bool"]["must"].append({
                "multi_match": {
                    "query": query_text,
                    "fields": ["title", "content"]
                }
            })
        
        # 添加时间范围条件
        if time_range and len(time_range) == 2:
            time_filter = {"range": {"time": {}}}
            if time_range[0]:
                time_filter["range"]["time"]["gte"] = time_range[0]
            if time_range[1]:
                time_filter["range"]["time"]["lte"] = time_range[1]
            query["bool"]["must"].append(time_filter)
        
        # 构建搜索请求
        search_body = {
            "query": query,
            "size": limit,
            "sort": [{"time": {"order": "desc"}}]  # 默认按时间倒序排列
        }
        
        logger.info(f"搜索分享内容: {json.dumps(search_body, ensure_ascii=False)}")
        search_result = es_client.search(PLAZA_INDEX, {"query": query, "size": limit})
        
        # 处理结果
        hits = search_result['hits']['hits']
        shares = []
        for hit in hits:
            share = hit['_source']
            share['id'] = hit['_id']
            shares.append(share)
        
        return shares
        
    except Exception as e:
        logger.error(f"搜索分享内容出错: {str(e)}")
        return []

def get_all_share_ids() -> list:
    """获取所有的share_id列表
    
    Returns:
        list: share_id列表，按创建时间降序排序
    """
    try:
        # 构建查询所有文档ID的请求
        query = {
            "query": {
                "match_all": {}  # 匹配所有文档
            },
            "_source": False,  # 不返回源文档内容，只需要ID
            "sort": [
                {
                    "time.keyword": {
                        "order": "desc"  # 按创建时间降序排序
                    }
                }
            ],
            "size": 10000  # 设置较大的数值以获取所有结果
        }
        
        logger.info("开始查询所有share_id")
        search_result = es_client.search(PLAZA_INDEX, query)
        
        if not search_result or 'hits' not in search_result:
            logger.warning("未找到任何share_id")
            return []
        
        # 直接提取文档ID
        share_ids = [hit['_id'] for hit in search_result['hits']['hits']]
        
        total = len(share_ids)
        logger.info(f"成功获取share_id列表，共{total}个")
        
        # 记录一些示例ID用于调试
        if share_ids:
            logger.info(f"示例share_id: {share_ids[:3]}")
        
        return share_ids
        
    except Exception as e:
        logger.error(f"获取share_id列表时出错: {str(e)}")
        return []
    
def get_plaza_share_by_id(share_id, master_mis_id=None, index=PLAZA_INDEX):
    """根据ID获取特定分享内容
    
    Args:
        share_id (str): 分享ID
        
    Returns:
        dict: 分享内容详情，包含所有字段，如果未找到返回None
    """
    try:
        # 验证share_id不为空
        if not share_id or not isinstance(share_id, str) or share_id.strip() == '':
            logger.warning(f"尝试查询空的分享ID")
            return None
            
        # 使用term查询精确匹配ID
        query = {
            "query": {
                "term": {
                    "_id": share_id
                }
            }
        }
        
        logger.info(f"查询分享ID: {share_id}")
        search_result = es_client.search(index, query)
        
        if not search_result:
            logger.warning(f"查询分享时ES返回为空，ID: {share_id}")
            return None
            
        hits = search_result.get('hits', {}).get('hits', [])
        
        if hits:
            source = hits[0]['_source']
            share = {
                "id": hits[0]['_id'],
                "time": source.get('time'),
                "title": source.get('title'),
                "content": source.get('content'),
                "mis_id_keyword": source.get('mis_id_keyword'),
                "user_content": source.get('user_content'),
                "share_identifier": source.get('share_identifier'),
                "shop_name": source.get('shop_name', ''),
                "order_detail": source.get('order_detail', []),
                "money_detail": source.get('money_detail', ''),
                "food_img_urls": source.get('food_img_urls', []),
                "shop_url": source.get('shop_url', []),
                # 添加互动计数字段
                "like_count": source.get('like_count', 0),
                "dislike_count": source.get('dislike_count', 0),
                "comment_count": source.get('comment_count', 0),
                "liked_users": source.get('liked_users', []),
                "disliked_users": source.get('disliked_users', []),
                # 添加评论记录字段
                "comment_record": source.get('comment_record', {}),
                "is_liked": source.get('is_liked', False),
                "is_disliked": source.get('is_disliked', False),
                "master_mis_id": master_mis_id
            }
            return share
        else:
            logger.info(f"未找到分享ID: {share_id}")
            return None
            
    except Exception as e:
        logger.error(f"获取分享内容出错: {str(e)}")
        return None

def get_plaza_shares_by_ids(share_ids: list, master_mis_id=None) -> list:
    """根据ID列表批量获取分享内容，按时间倒序排列
    
    Args:
        share_ids (list): share_id列表
        master_mis_id (str): 当前用户ID，用于判断是否点赞/点踩
        
    Returns:
        list: 分享内容列表，按时间倒序排列
    """
    try:
        if not share_ids:
            logger.warning("share_id列表为空")
            return []
            
        # 构建查询
        match_queries = [{"term": {"_id": term}} for term in share_ids]
        query = {
            "query": {
                "bool": {
                    "should": match_queries,
                    "minimum_should_match": 1
                }
            },
            "size": 10000
        }
        
        logger.info(f"批量查询分享内容，共{len(share_ids)}条")
        
        # 添加详细的错误检查
        try:
            search_result = es_client.search(PLAZA_INDEX, query)
            logger.debug(f"ES返回结果类型: {type(search_result)}")  # 打印返回结果类型
            logger.debug(f"ES返回结果: {search_result}")  # 打印完整返回结果
        except Exception as e:
            logger.error(f"ES查询出错: {str(e)}")
            return []

        # 如果search_result是列表，尝试处理第一个元素
        if isinstance(search_result, list):
            if not search_result:
                logger.warning("ES返回空列表")
                return []
            search_result = search_result[0]
            
        # 如果仍然不是字典，返回空列表
        if not isinstance(search_result, dict):
            logger.error(f"搜索结果格式错误，期望dict，实际是{type(search_result)}")
            return []

        # 获取hits，添加更多的类型检查
        hits = []
        if 'hits' in search_result:
            hits_container = search_result['hits']
            if isinstance(hits_container, dict) and 'hits' in hits_container:
                hits = hits_container['hits']
        
        if not hits:
            logger.warning("未找到任何匹配的分享内容")
            return []
            
        # 处理查询结果
        shares = []
        for hit in hits:
            try:
                if not isinstance(hit, dict):
                    logger.warning(f"跳过非字典格式的hit: {hit}")
                    continue
                    
                source = hit.get('_source', {})
                if not isinstance(source, dict):
                    logger.warning(f"跳过非字典格式的source: {source}")
                    continue
                    
                # 获取评论记录并计算未删除的评论数量
                comment_record = source.get('comment_record', [])
                if isinstance(comment_record, list):
                    comment_count = len([comment for comment in comment_record if not comment.get('is_deleted', False)])
                else:
                    comment_count = 0
                    comment_record = []

                # 获取点赞和点踩用户列表
                liked_users = source.get('liked_users', [])
                disliked_users = source.get('disliked_users', [])
                
                # 判断当前用户是否点赞/点踩
                is_liked = False
                is_disliked = False
                if master_mis_id:
                    is_liked = master_mis_id in liked_users
                    is_disliked = master_mis_id in disliked_users
                    
                share = {
                    "id": hit.get('_id'),
                    "time": source.get('time'),
                    "title": source.get('title'),
                    "content": source.get('content'),
                    "mis_id_keyword": source.get('mis_id_keyword'),
                    "user_content": source.get('user_content'),
                    "share_identifier": source.get('share_identifier'),
                    "shop_name": source.get('shop_name', ''),
                    "order_detail": source.get('order_detail', []),
                    "money_detail": source.get('money_detail', ''),
                    "food_img_urls": source.get('food_img_urls', []),
                    "shop_url": source.get('shop_url', []),
                    # 添加互动计数字段
                    "like_count": source.get('like_count', 0),
                    "dislike_count": source.get('dislike_count', 0),
                    "comment_count": comment_count,
                    "liked_users": liked_users,
                    "disliked_users": disliked_users,
                    # 添加评论记录字段
                    "comment_record": comment_record,
                    # 添加当前用户的点赞/点踩状态
                    "master_mis_id": master_mis_id,
                    "is_liked": is_liked,
                    "is_disliked": is_disliked
                }
                shares.append(share)
            except Exception as e:
                logger.warning(f"处理单条分享内容时出错: {str(e)}")
                continue
        
        # 按时间倒序排序
        shares.sort(key=lambda x: datetime.strptime(x.get('time', ''), '%Y-%m-%d %H:%M:%S'), reverse=True)
        
        logger.info(f"成功获取分享内容，共{len(shares)}条记录")
        return shares
        
    except Exception as e:
        logger.error(f"批量获取分享内容时出错: {str(e)}, 错误类型: {type(e)}")
        return []
    

def test_get_plaza_share(share_id, master_mis_id=None):
    """测试获取广场分享内容，返回前端格式的数据"""
    try:
        result = get_plaza_share_by_id(share_id, master_mis_id, index=PLAZA_INDEX)
        
        if result:
            # 获取评论记录并计算未删除的评论数量
            comment_record = result.get('comment_record', [])
            if isinstance(comment_record, list):
                # 过滤掉已删除的评论
                comment_count = len([comment for comment in comment_record if not comment.get('is_deleted', False)])
            elif isinstance(comment_record, dict):
                comment_count = len([comment for comment in comment_record.values() if not comment.get('is_deleted', False)])
            else:
                comment_count = 0
                comment_record = []
            
            # 获取点赞和点踩用户列表
            liked_users = result.get('liked_users', [])
            disliked_users = result.get('disliked_users', [])
            
            # 计算实际的点赞和点踩数量
            like_count = len(liked_users)
            dislike_count = len(disliked_users)
            
            # 判断当前用户是否点赞/点踩
            is_liked = False
            is_disliked = False
            if master_mis_id:
                is_liked = master_mis_id in liked_users
                is_disliked = master_mis_id in disliked_users

            # 更新评论数量到ES
            update_data = {
                "comment_count": comment_count,
                "comment_record": comment_record,
                "liked_users": liked_users,
                "disliked_users": disliked_users,
                "like_count": like_count,
                "dislike_count": dislike_count,
            }
            update_plaza_share_content(share_id, update_data)
            
            # 构建标准化的返回数据
            response_data = {
                "code": 200,
                "message": "success",
                "data": {
                    "id": result.get('id'),
                    "time": result.get('time'),
                    "title": result.get('title'),
                    "content": result.get('content'),
                    "mis_id_keyword": result.get('mis_id_keyword'),
                    "user_content": result.get('user_content'),
                    "share_identifier": result.get('share_identifier'),
                    "shop_name": result.get('shop_name', ''),
                    "order_detail": result.get('order_detail', []),
                    "money_detail": result.get('money_detail', ''),
                    "food_img_urls": result.get('food_img_urls', []),
                    "shop_url": result.get('shop_url', []),
                    # 添加互动计数字段
                    "like_count": like_count,
                    "dislike_count": dislike_count,
                    "comment_count": comment_count,
                    "liked_users": liked_users,
                    "disliked_users": disliked_users,
                    # 添加评论记录字段
                    "comment_record": comment_record,
                    "is_liked": is_liked,
                    "is_disliked": is_disliked,
                    "master_mis_id": master_mis_id
                }
            }
            
            # 使用logger打印响应数据
            logger.info("\n=== API 响应数据 ===")
            logger.info(f"状态码: {response_data['code']}")
            logger.info(f"消息: {response_data['message']}")
            logger.info("\n分享内容:")
            data = response_data['data']
            logger.info(f"分享ID: {data['id']}")
            logger.info(f"分享时间: {data['time']}")
            logger.info(f"分享标题: {data['title']}")
            logger.info(f"分享内容: {data['content']}")
            logger.info(f"用户ID: {data['mis_id_keyword']}")
            logger.info(f"用户内容: {data['user_content']}")
            logger.info(f"分享标识: {data['share_identifier']}")
            logger.info(f"店铺名称: {data['shop_name']}")
            logger.info(f"订单详情: {data['order_detail']}")
            logger.info(f"金额详情: {data['money_detail']}")
            logger.info(f"美食图片URL: {data['food_img_urls']}")
            logger.info(f"商店图片URL: {data['shop_url']}")
            # 添加互动计数字段的日志
            logger.info(f"点赞数: {data['like_count']}")
            logger.info(f"点踩数: {data['dislike_count']}")
            logger.info(f"评论数: {data['comment_count']}")
            logger.info(f"点赞用户: {data['liked_users']}")
            logger.info(f"点踩用户: {data['disliked_users']}")
            logger.info(f"评论记录: {data['comment_record']}")
            logger.info(f"当前用户: {master_mis_id}")
            logger.info(f"当前用户是否点赞: {data['is_liked']}")
            logger.info(f"当前用户是否点踩: {data['is_disliked']}")
            # 添加评论记录的日志
            
            logger.info("=== 响应数据结束 ===\n")
            
            return response_data
            
        else:
            error_response = {
                "code": 404,
                "message": "分享内容未找到",
                "data": None
            }
            logger.warning("\n=== API 错误响应 ===")
            logger.warning(f"状态码: {error_response['code']}")
            logger.warning(f"错误信息: {error_response['message']}")
            logger.warning("=== 错误响应结束 ===\n")
            
            return error_response
            
    except Exception as e:
        error_response = {
            "code": 500,
            "message": f"获取分享内容时出错: {str(e)}",
            "data": None
        }
        logger.error("\n=== API 错误响应 ===")
        logger.error(f"状态码: {error_response['code']}")
        logger.error(f"错误信息: {error_response['message']}")
        logger.error("=== 错误响应结束 ===\n")
        
        return error_response

def delete_plaza_share(share_id, mis_id):
    """删除分享内容
    
    Args:
        share_id (str): 分享ID
        mis_id (str): 操作用户的美团内部ID (用于权限验证)
        
    Returns:
        bool: 是否删除成功
    """
    try:
        # 使用 get_plaza_share_by_id 查询分享内容
        share = get_plaza_share_by_id(share_id, index=PLAZA_INDEX)
        
        # 检查分享是否存在
        if not share:
            logger.warning(f"未找到匹配的分享: ID={share_id}")
            return False
            
        # 检查权限
        if share.get('mis_id_keyword') != mis_id:
            logger.warning(f"权限不足，分享的发布者是 {share.get('mis_id_keyword')}，而不是 {mis_id}")
            return False
        
        # 权限验证通过，执行删除
        result = es_client.delete_doc(PLAZA_INDEX, share_id)
        if result:
            logger.info(f"成功删除分享: ID={share_id}")
            return True
        else:
            logger.error(f"删除分享失败: ID={share_id}")
            return False
            
    except Exception as e:
        logger.error(f"删除分享出错: {str(e)}")
        return False

# 新增函数：通过标识符搜索分享
def search_plaza_by_identifier(share_identifier, mis_id=None):
    """通过唯一标识符查找分享
    
    Args:
        share_identifier (str): 分享标识符，例如"店名+时间戳"或"标题+时间戳"
        mis_id (str, optional): 用户ID，如果提供则同时匹配用户
        
    Returns:
        dict: 找到的第一个分享内容，如果未找到返回None
    """
    try:
        query = {
            "bool": {
                "must": [
                    {"term": {"share_identifier.keyword": share_identifier}}
                ]
            }
        }
        
        # 如果提供了用户ID，添加用户匹配条件
        if mis_id:
            query["bool"]["must"].append({"term": {"mis_id_keyword": mis_id}})
            
        search_body = {
            "query": query,
            "size": 1  # 只返回第一个匹配的结果
        }
        
        logger.info(f"通过标识符搜索分享: {json.dumps(search_body, ensure_ascii=False)}")
        search_result = es_client.search(PLAZA_INDEX, search_body)
        
        hits = search_result['hits']['hits']
        if hits:
            share = hits[0]['_source']
            share['id'] = hits[0]['_id']
            logger.info(f"找到匹配的分享: ID={share['id']}")
            return share
        else:
            logger.info(f"未找到标识符为 {share_identifier} 的分享")
            return None
            
    except Exception as e:
        logger.error(f"通过标识符搜索分享出错: {str(e)}")
        return None

# 新增函数：更新分享内容
def update_plaza_share_content(share_id, update_data, mis_id=None):
    """更新分享内容的所有字段
    
    Args:
        share_id (str): 分享ID
        update_data (dict): 要更新的字段和值，可以包含以下字段：
            - user_content: 用户自定义内容
            - title: 分享标题
            - content: 分享内容
            - shop_name: 商店名称
            - order_detail: 订单详情
            - money_detail: 金额详情
            - food_img_urls: 食物图片URL
            - shop_url: 商店URL
            - like_count: 点赞数
            - dislike_count: 点踩数
            - comment_count: 评论数
            - comment_record: 评论记录
        mis_id (str, optional): 操作用户的ID (用于权限验证)
        
    Returns:
        bool: 是否更新成功
    """
    try:
        # 如果提供了用户ID，先验证权限
        if mis_id:
            share = get_plaza_share_by_id(share_id, index=PLAZA_INDEX)
            if not share or share.get('mis_id_keyword') != mis_id:
                logger.warning(f"未找到匹配的分享或权限不足: ID={share_id}, 用户ID={mis_id},真的id={share.get('mis_id_keyword')}")
                return False
        
        # 准备更新文档
        update_doc = {"doc": {}}
        
        # 允许更新的字段列表
        allowed_fields = [
            # 基本信息
           "title", "content"
        ]
        
        # 只更新允许的字段
        for field in allowed_fields:
            if field in update_data:
                update_doc["doc"][field] = update_data[field]
                
        # 如果更新了评论记录，自动更新评论数量
        # if "comment_record" in update_data:
        #     comment_record = update_data["comment_record"]
        #     if isinstance(comment_record, list):
        #         update_doc["doc"]["comment_count"] = len(comment_record)
        #     elif isinstance(comment_record, dict):
        #         # 如果是字典，需要先获取现有的评论记录
        #         existing_share = get_plaza_share_by_id(share_id)
        #         existing_comments = existing_share.get('comment_record', {}) if existing_share else {}
                
        #         # 合并现有评论和新评论
        #         if isinstance(existing_comments, dict):
        #             existing_comments.update(comment_record)
        #             update_doc["doc"]["comment_record"] = existing_comments
        #             update_doc["doc"]["comment_count"] = len(existing_comments)
        #         else:
        #             # 如果现有评论不是字典，直接使用新评论
        #             update_doc["doc"]["comment_record"] = comment_record
        #             update_doc["doc"]["comment_count"] = len(comment_record)
        #     else:
        #         update_doc["doc"]["comment_count"] = 0
        
        # 如果没有要更新的字段
        if not update_doc["doc"]:
            logger.warning(f"没有提供有效的更新字段: {update_data}")
            return False
        
        logger.info(f"更新分享内容: ID={share_id}, 更新字段: {list(update_doc['doc'].keys())}")
        result = es_client.update_doc(PLAZA_INDEX, share_id, update_doc)
        
        if result:
            logger.info(f"成功更新分享内容: ID={share_id}")
            return True
        else:
            logger.error(f"更新分享内容失败: ID={share_id}")
            return False
            
    except Exception as e:
        logger.error(f"更新分享内容出错: {str(e)}")
        return False

def search_plaza_contains_exact_phrase(search_phrase, time_range=None):
    """
    搜索包含精确词组的分享内容
    
    Args:
        search_phrase: 要搜索的词组
        time_range: 时间范围元组 (start_time, end_time)，end_time可为None表示"到现在"
    
    Returns:
        list: 符合搜索条件的分享列表
    """
    try:
        # 构建基本查询
        should_clauses = [
            {"match_phrase": {"title": search_phrase}},
            {"match_phrase": {"content": search_phrase}}
        ]
        
        query = {
            "bool": {
                "should": should_clauses,
                "minimum_should_match": 1
            }
        }
        
        # 添加时间过滤
        if time_range:
            start_time, end_time = time_range
            time_filter = {"range": {"time": {"gte": start_time}}}
            if end_time:  # 如果提供了结束时间
                time_filter["range"]["time"]["lte"] = end_time
            
            # 将时间过滤添加到查询中
            if "filter" not in query["bool"]:
                query["bool"]["filter"] = []
            query["bool"]["filter"].append(time_filter)
        
        # 构建搜索体
        search_body = {
            "query": query,
            "sort": [{"time": {"order": "desc"}}],
            "size": 10  # 限制结果数量
        }
        
        # 执行查询
        search_results = es_client.search(PLAZA_INDEX, search_body)
        
        # 处理结果
        if not search_results:
            logger.warning(f"ES返回为空，可能是查询失败: {search_phrase}")
            return []
        
        hits = search_results.get('hits', {}).get('hits', [])
        
        # 构建返回结果
        shares = []
        for hit in hits:
            share = hit['_source']
            share['id'] = hit['_id']
            shares.append(share)
        
        logger.info(f"精确短语搜索结果数量: {len(shares)}")
        return shares
    
    except Exception as e:
        logger.error(f"搜索分享内容时出错: {str(e)}")
        return []

def search_plaza_fuzzy(query_text, time_range=None, limit=10):
    """使用模糊搜索查找分享内容
    
    Args:
        query_text (str): 要搜索的文本
        time_range (tuple, optional): 时间范围(开始时间, 结束时间)
        limit (int): 返回结果数量限制
        
    Returns:
        list: 模糊匹配的分享内容列表
    """
    try:
        # 构建多字段查询
        multi_match_query = {
            "multi_match": {
                "query": query_text,
                "fields": ["title", "content", "order_detail", "user_content", "shop_name"],
                "type": "best_fields",
                "fuzziness": "AUTO"  # 自动模糊匹配
            }
        }
        
        # 构建bool查询，增加搜索灵活性
        bool_query = {
            "bool": {
                "should": [
                    multi_match_query,
                    # 添加wildcard查询以增加匹配几率
                    {"wildcard": {"title": f"*{query_text}*"}},
                    {"wildcard": {"content": f"*{query_text}*"}}
                ],
                "minimum_should_match": 1
            }
        }
        
        # 如果有时间范围，添加时间条件
        if time_range and len(time_range) == 2 and (time_range[0] or time_range[1]):
            time_filter = {"range": {"time": {}}}
            if time_range[0]:
                time_filter["range"]["time"]["gte"] = time_range[0]
            if time_range[1]:
                time_filter["range"]["time"]["lte"] = time_range[1]
            bool_query["bool"]["must"] = [time_filter]
        
        search_body = {
            "query": bool_query,
            "size": limit
        }
        
        logger.info(f"模糊搜索: {json.dumps(search_body, ensure_ascii=False)}")
        search_result = es_client.search(PLAZA_INDEX, search_body)
        
        # 增加调试日志
        logger.info(f"模糊搜索结果前200字符: {json.dumps(search_result, ensure_ascii=False)[:200]}...")
        
        # 处理结果
        if not search_result:
            logger.warning(f"ES返回为空，可能是查询失败: {query_text}")
            return []
        
        if 'hits' not in search_result:
            logger.warning(f"ES返回中缺少hits字段: {json.dumps(search_result, ensure_ascii=False)[:100]}...")
            return []
        
        hits = search_result['hits']['hits']
        logger.info(f"模糊搜索到的文档数: {len(hits)}")
        
        shares = []
        for hit in hits:
            share = hit['_source']
            share['id'] = hit['_id']
            shares.append(share)
        
        logger.info(f"模糊搜索结果数量: {len(shares)}")
        return shares
        
    except Exception as e:
        logger.error(f"模糊搜索分享内容出错: {str(e)}")
        return []

def search_plaza_phrase_and_subphrases(query_text:str, time_range=None, limit=10):
    """搜索包含精确短语或其主要子短语的分享内容
    
    Args:
        query_text (str): 要搜索的精确短语
        time_range (tuple, optional): 时间范围(开始时间, 结束时间)
        limit (int): 返回结果数量限制
        
    Returns:
        list: 包含完整短语或关键子短语的分享内容列表
    """
    try:
        # 生成子短语列表（简单实现，针对2-4字词组）
        subphrases = []
        if len(query_text) >= 4:  # 对于较长词组，提取可能的关键子短语
            # 取最后2-3个字，通常是较重要的部分
            subphrases.append(query_text[-2:])  # 最后2个字
            if len(query_text) >= 3:
                subphrases.append(query_text[-3:])  # 最后3个字
        
        # 构建bool查询
        should_clauses = [
            {"match_phrase": {"title": query_text}},
            {"match_phrase": {"content": query_text}},
            {"match_phrase": {"order_detail": query_text}},
            {"match_phrase": {"user_content": query_text}},
            {"match_phrase": {"shop_name": query_text}},
            {"match": {"share_id": query_text}},
            {"match": {"share_identifier": query_text}}
        ]
        
        # 添加子短语查询
        for subphrase in subphrases:
            should_clauses.append({"match_phrase": {"title": subphrase}})
            should_clauses.append({"match_phrase": {"content": subphrase}})
        
        bool_query = {
            "bool": {
                "should": should_clauses,
                "minimum_should_match": 1
            }
        }
        
        # 如果有时间范围，添加时间条件
        if time_range and len(time_range) == 2 and (time_range[0] or time_range[1]):
            time_filter = {"range": {"time": {}}}
            if time_range[0]:
                time_filter["range"]["time"]["gte"] = time_range[0]
            if time_range[1]:
                time_filter["range"]["time"]["lte"] = time_range[1]
            bool_query["bool"]["must"] = [time_filter]
        
        search_body = {
            "query": bool_query,
            "size": limit
        }
        
        logger.info(f"短语及子短语搜索: {json.dumps(search_body, ensure_ascii=False)}")
        search_result = es_client.search(PLAZA_INDEX, search_body)
        
        # 处理结果
        if not search_result or 'hits' not in search_result:
            logger.warning(f"短语及子短语搜索未返回有效结果: {query_text}")
            return []
        
        hits = search_result['hits']['hits']
        logger.info(f"短语及子短语搜索到的文档数: {len(hits)}")
        
        # 处理结果并添加匹配的短语信息
        shares = []
        for hit in hits:
            share = hit['_source']
            share['id'] = hit['_id']
            shares.append(share)
        
        logger.info(f"短语及子短语搜索结果数量: {len(shares)}")
        return shares
        
    except Exception as e:
        logger.error(f"短语及子短语搜索分享内容出错: {str(e)}")
        return []
    
def generate_share_content(history: list[dict], user_orders=None, intent=None, memory_content=None) -> tuple:
    """
    根据聊天历史和用户订单生成分享标题和内容
    
    Args:
        history: 聊天历史记录
        user_orders: 用户订单数据
        intent: 用户意图关键词
        
    Returns:
        tuple: (标题, 内容, shop_name, order_detail, money_detail)
    """
    try:
        # 提取最近的对话历史
        recent_history = history[-10:] if len(history) > 10 else history
        
        # 构建系统提示词
        system_prompt = lion_config.lion_client.get_value_with_default("weiwei.share_content_generation_prompt", DEFAULT_SHARE_CONTENT_GENERATION_PROMPT)
        
        # 构建用户消息
        user_message = "聊天历史：\n"
        for msg in recent_history:
            role = "用户" if msg["role"] == "user" else "小美喂喂" if msg["role"] == "assistant" else "系统"
            user_message += f"{role}: {msg['content']}\n\n"
        
        if user_orders:
            user_message += f"\n用户订单历史: {json.dumps(user_orders, ensure_ascii=False)}\n\n"
        
        if intent:
            user_message += f"用户意图关键词: {', '.join(intent) if isinstance(intent, list) else intent}\n\n"
        
        if memory_content:
            user_message += f"记忆内容: {memory_content}\n\n"
        
        user_message += "请根据以上信息生成适合分享的标题和内容，以JSON格式返回。"
        
        # 调用LLM生成内容
        share_model = lion_config.lion_client.get_value_with_default("weiwei.share_content_generation_model", "gpt-4o-mini")
        query = {
            "model": share_model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ],
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        response = send_to_ai(query)
        response_text = json.loads(response.text)["choices"][0]["message"]["content"]

        logger.info(f"生成分享内容结果: {response_text}")
        
        # 提取JSON部分
        import re
        json_pattern = r'({[\s\S]*})'
        match = re.search(json_pattern, response_text)
        
        # 初始化返回值
        title = "分享内容"
        content = "无内容"
        shop_name = ""
        order_detail = []
        money_detail = []
        food_names = []
        
        if match:
            result = json.loads(match.group(1))
            title = result.get("title", "分享内容")
            content = result.get("content", "无内容")
            shop_num = result.get("shop_num", 0)
            
            # 如果shop_num有效且存在订单信息，提取相关订单数据
            if shop_num > 0 and user_orders:
                # 获取订单数据中的商店列表
                shop_names = list(user_orders.keys())
                
                # 确保shop_num不超过可用的商店数量
                if 0 < shop_num <= len(shop_names):
                    selected_shop = shop_names[shop_num - 1]
                    shop_name = selected_shop
                    
                    # 获取该商店的订单详情
                    shop_data = user_orders.get(selected_shop, {})
                    shop_details = shop_data.get("details", [])
                    
                    # 如果有订单详情，提取第一条订单的内容(通常是最新的)
                    if shop_details:
                        order_items = shop_details[0].get("items", [])
                        
                        # 提取订单详情和价格
                        for item in order_items:
                            food_name = item.get("food_name", "")
                            count = item.get("count", 1)
                            price = item.get("price", 0.0)
                            
                            food_names.append(food_name)
                            order_detail.append(f"{food_name} x{count}")
                            money_detail.append(price)
        else:
            # 如果无法提取JSON，使用默认内容
            logger.warning("无法从AI响应中提取JSON格式内容，使用默认标题和内容")
            
            # 提取用户最后一条消息作为默认标题
            user_last_message = ""
            for msg in reversed(history):
                if msg["role"] == "user":
                    user_last_message = msg["content"]
                    break
            
            title = user_last_message[:20] + "..." if len(user_last_message) > 20 else user_last_message
            
            # 构建默认内容
            content = ""
            for i, msg in enumerate(recent_history[-4:]):
                role = "用户" if msg["role"] == "user" else "小美喂喂" if msg["role"] == "assistant" else "系统"
                if role != "系统":  # 跳过系统消息
                    content += f"{role}: {msg['content']}\n\n"

        # 搜索商品信息
        poi_ids, food_ids = search_merchant_food(shop_name, food_names)
        poi_id = ""
        for id in poi_ids:
            if id != "":
                poi_id = id
                break

        logger.info(f"搜索商品信息结果: poi_ids: {poi_ids}, food_ids: {food_ids}, poi_id: {poi_id}")

        shop_url = generate_meituan_link("meituan_app_waimai_merchant_link", poi_id=poi_id)
        logger.info(f"搜索商品信息结果: poi_ids: {poi_ids}, food_ids: {food_ids}, shop_url: {shop_url}")
        return title, content, shop_name, food_names, order_detail, money_detail, poi_id, food_ids, shop_url
    
    except Exception as e:
        logger.error(f"生成分享内容时出错: {str(e)}")
        # 返回默认内容
        return "聊天分享", "聊天内容", "", [], [], "", "", ""

# def casual_search(query_food:list[str]|str=None, shop_name:list[str]|str=None, limit:int=10):

def get_user_liked_shares(mis_id: str) -> list:
    """获取用户已点赞的帖子列表，按时间倒序排列
    
    Args:
        mis_id (str): 用户ID
        
    Returns:
        list: 已点赞的帖子列表，按时间倒序排列
    """
    try:
        # 获取所有分享ID
        all_share_ids = get_all_share_ids()
        if not all_share_ids:
            return []
            
        # 获取所有分享内容
        all_shares = get_plaza_shares_by_ids(all_share_ids, mis_id)
        
        # 筛选已点赞的帖子
        liked_shares = []
        for share in all_shares:
            if share.get('is_liked', False):
                # 获取帖子的完整信息
                full_share = test_get_plaza_share(share.get('id'), mis_id)
                if full_share and full_share.get('code') == 200:
                    liked_shares.append(full_share.get('data'))
        
        # 按时间倒序排序
        liked_shares.sort(key=lambda x: datetime.strptime(x.get('time', ''), '%Y-%m-%d %H:%M:%S'), reverse=True)
        
        return liked_shares
        
    except Exception as e:
        logger.error(f"获取用户已点赞帖子时出错: {str(e)}")
        return []

def get_user_disliked_shares(mis_id: str) -> list:
    """获取用户已点踩的帖子列表，按时间倒序排列
    
    Args:
        mis_id (str): 用户ID
        
    Returns:
        list: 已点踩的帖子列表，按时间倒序排列
    """
    try:
        # 获取所有分享ID
        all_share_ids = get_all_share_ids()
        if not all_share_ids:
            return []
            
        # 获取所有分享内容
        all_shares = get_plaza_shares_by_ids(all_share_ids, mis_id)
        
        # 筛选已点踩的帖子
        disliked_shares = []
        for share in all_shares:
            if share.get('is_disliked', False):
                # 获取帖子的完整信息
                full_share = test_get_plaza_share(share.get('id'), mis_id)
                if full_share and full_share.get('code') == 200:
                    disliked_shares.append(full_share.get('data'))
        
        # 按时间倒序排序
        disliked_shares.sort(key=lambda x: datetime.strptime(x.get('time', ''), '%Y-%m-%d %H:%M:%S'), reverse=True)
        
        return disliked_shares
        
    except Exception as e:
        logger.error(f"获取用户已点踩帖子时出错: {str(e)}")
        return []

def get_user_shared_shares(mis_id: str) -> list:
    """获取用户已分享的帖子列表，按时间倒序排列
    
    Args:
        mis_id (str): 用户ID
        
    Returns:
        list: 已分享的帖子列表，按时间倒序排列
    """
    try:
        # 获取所有分享ID
        all_share_ids = get_all_share_ids()
        if not all_share_ids:
            return []
            
        # 获取所有分享内容
        all_shares = get_plaza_shares_by_ids(all_share_ids, mis_id)
        
        # 筛选用户分享的帖子
        shared_shares = []
        for share in all_shares:
            if share.get('mis_id_keyword') == mis_id:
                # 获取帖子的完整信息
                full_share = test_get_plaza_share(share.get('id'), mis_id)
                if full_share and full_share.get('code') == 200:
                    shared_shares.append(full_share.get('data'))
        
        # 按时间倒序排序
        shared_shares.sort(key=lambda x: datetime.strptime(x.get('time', ''), '%Y-%m-%d %H:%M:%S'), reverse=True)
        
        return shared_shares
        
    except Exception as e:
        logger.error(f"获取用户已分享帖子时出错: {str(e)}")
        return []

def get_user_info_shares(mis_id: str) -> dict:
    """获取用户的所有互动信息（点赞、点踩、分享的帖子）
    
    Args:
        mis_id (str): 用户ID
        
    Returns:
        dict: 包含用户所有互动信息的字典和统计信息
    """
    try:
        # 分别获取各类帖子
        liked_shares = get_user_liked_shares(mis_id)
        disliked_shares = get_user_disliked_shares(mis_id)
        shared_shares = get_user_shared_shares(mis_id)
        
        # 计算各类帖子的数量
        stats = {
            "liked_count": len(liked_shares),
            "disliked_count": len(disliked_shares),
            "shared_count": len(shared_shares)
        }
        
        return {
            "stats": stats,
            "liked_shares": liked_shares,
            "disliked_shares": disliked_shares,
            "shared_shares": shared_shares
        }
        
    except Exception as e:
        logger.error(f"获取用户互动信息时出错: {str(e)}")
        return {
            "stats": {
                "liked_count": 0,
                "disliked_count": 0,
                "shared_count": 0
            },
            "liked_shares": [],
            "disliked_shares": [],
            "shared_shares": []
        }
def update_user_comment_graph(user_id:str, comment_id:str, is_delete:bool=False):
    """
    上传或者更新用户评论图谱
    
    Args:
        user_id (str): 用户ID
        comment_id (str): 评论ID
    """
    doc_id = f"{user_id}_comments"
    now_comments = get_plaza_share_by_id(doc_id, index=user_graph_index)

    if now_comments is not None:
        now_comments = now_comments.get("comments_id", [])


    if isinstance(now_comments, str):
        now_comments = [now_comments]
    logger.info(f"当前用户评论图谱: {now_comments}")
    
    try:
        if not is_delete:
            if now_comments is None:
                doc = {
                    "user_id": user_id,
                    "comments_id": [comment_id]
                }
                record = {
                    "doc": doc,
                    "doc_as_upsert": True
                }
                es_client.upsert_data(index_name=user_graph_index, doc_id=doc_id, doc=record)
                logger.info(f"初次上传用户评论图谱成功: {doc_id}")
            else:
                if comment_id not in now_comments:
                    now_comments.append(comment_id)
                doc = {
                    "comments_id": now_comments
                }
                update_doc = {"doc": doc}

                es_client.update_doc(index_name=user_graph_index, doc_id=doc_id, update_body=update_doc)
                logger.info(f"更新用户评论图谱成功: {doc_id}")
        else:
            now_comments = [comments for comments in now_comments if comments != comment_id]
            doc = {
                "comments_id": now_comments
            }
            update_doc = {"doc": doc}

            es_client.update_doc(index_name=user_graph_index, doc_id=doc_id, update_body=update_doc)
            logger.info(f"修改用户评论图谱成功: {doc_id}，删除相关条目: {comment_id}")

    except Exception as e:
        logger.error(f"更新用户评论图谱时出错: {str(e)}")


def update_shop_comment_graph(shop_name:str, shop_id:str, comment_id:str, is_delete:bool=False):
    """
    上传或者更新店铺评论图谱
    
    Args:
        shop_name (str): 店铺名称
        comment_id (str): 评论ID
    """
    doc_id = f"{shop_id}_comments"
    now_comments = get_plaza_share_by_id(doc_id, index=shop_graph_index)
    if now_comments is not None:
        now_comments = now_comments.get("comments_id", [])

    if isinstance(now_comments, str):
        now_comments = [now_comments]
    logger.info(f"当前店铺评论图谱: {now_comments}")
    
    try:
        if not is_delete:
            if now_comments is None:
                doc = {
                    "shop_name": shop_name,
                    "comments_id": [comment_id],
                    "shop_id": shop_id
                }
                record = {
                    "doc": doc,
                    "doc_as_upsert": True
                }
                es_client.upsert_data(index_name=shop_graph_index, doc_id=doc_id, doc=record)
                logger.info(f"初次上传店铺评论图谱成功: {doc_id}")
            else:
                if comment_id not in now_comments:
                    now_comments.append(comment_id)
                doc = {
                    "comments_id": now_comments
                }
                update_doc = {"doc": doc}   

                es_client.update_doc(index_name=shop_graph_index, doc_id=doc_id, update_body=update_doc)
                logger.info(f"更新店铺评论图谱成功: {doc_id}")
        else:
            now_comments = [comments for comments in now_comments if comments != comment_id]
            doc = {
                "comments_id": now_comments
            }   
            update_doc = {"doc": doc}

            es_client.update_doc(index_name=shop_graph_index, doc_id=doc_id, update_body=update_doc)
            logger.info(f"修改店铺评论图谱成功: {doc_id}，删除相关条目: {comment_id}")

    except Exception as e:
        logger.error(f"更新店铺评论图谱时出错: {str(e)}")

