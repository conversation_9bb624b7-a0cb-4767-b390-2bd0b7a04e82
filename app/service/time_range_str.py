"""
用于生成时间范围字符串的工具模块，主要用于ES查询中的时间过滤
"""

from datetime import datetime, timedelta

def get_days_ago_str(days: int) -> str:
    """
    获取指定天数前的日期时间字符串
    
    Args:
        days: 要回溯的天数
        
    Returns:
        str: 格式化的日期时间字符串，格式为'%Y-%m-%d %H:%M:%S'
    """
    now = datetime.now()
    past_date = now - timedelta(days=days)
    return past_date.strftime("%Y-%m-%d %H:%M:%S")

def get_time_range(days: int) -> tuple:
    """
    获取从指定天数前到现在的时间范围
    
    Args:
        days: 要回溯的天数
        
    Returns:
        tuple: 包含开始时间和结束时间的元组 (start_time, end_time)
               其中end_time为None，表示"到现在"
    """
    days_ago_str = get_days_ago_str(days)
    return (days_ago_str, None)  # 从N天前到现在

def get_custom_time_range(start_days_ago: int, end_days_ago: int = 0) -> tuple:
    """
    获取自定义时间范围，支持指定开始和结束的天数
    
    Args:
        start_days_ago: 开始时间的回溯天数
        end_days_ago: 结束时间的回溯天数，默认为0(当前时间)
        
    Returns:
        tuple: 包含开始时间和结束时间的元组 (start_time, end_time)
    """
    now = datetime.now()
    start_date = now - timedelta(days=start_days_ago)
    start_str = start_date.strftime("%Y-%m-%d %H:%M:%S")
    
    if end_days_ago == 0:
        return (start_str, None)
    else:
        end_date = now - timedelta(days=end_days_ago)
        end_str = end_date.strftime("%Y-%m-%d %H:%M:%S")
        return (start_str, end_str)