import base64
import hmac
import json
import threading
import uuid
import wave
import datetime
from abc import ABC, abstractmethod
from hashlib import sha1
from threading import Lock
import requests
from configs.config import CURRENT_ENV
from configs import lion_config
from utils.logger import logger
from utils.threaded_generator import ThreadedGenerator


class SynthesizeResponse:
    def __init__(self, errorCode, data, subtitle, sessionId, extendInfo):
        self.errorCode = errorCode
        self.data = data
        self.subtitle = subtitle
        self.sessionId = sessionId
        self.extendInfo = extendInfo

class CallbackAction(ABC):

    @abstractmethod
    def on_success(self, sessionId, data):
        pass

    @abstractmethod
    def on_fail(self, sessionId, errorCode):
        pass

class TtsCallback(CallbackAction):

    def __init__(self):
        self.data = b''
        self.errorCode = None

    def on_success(self, sessionId, data):
        logger.info(f"Received {len(data)} bytes of audio data")
        self.data += data

    def on_fail(self, sessionId, errorCode):
        self.errorCode = errorCode
        logger.info("ttsCallbackOnFail"  + str(errorCode))


def save_audio_to_wav(pcm_data, output_file, sample_rate=8000, channels=1, sample_width=2):
    """
    将 PCM 数据保存为 WAV 文件。
    :param pcm_data: PCM 数据（字节格式）
    :param output_file: 输出文件路径
    :param sample_rate: 采样率，默认 8000 Hz
    :param channels: 声道数，默认 1（单声道）
    :param sample_width: 采样宽度（字节数），默认 2（16 位）
    """
    with wave.open(output_file, "wb") as wav_file:
        wav_file.setnchannels(channels)          # 设置声道数
        wav_file.setsampwidth(sample_width)      # 设置采样宽度
        wav_file.setframerate(sample_rate)       # 设置采样率
        wav_file.writeframes(pcm_data)           # 写入 PCM 数据


def hmac_sha1(key, data):
    try:
        key_bytes = key.encode('utf-8')
        data_bytes = data.encode('utf-8')
        hmac_obj = hmac.new(key_bytes, data_bytes, sha1)
        raw_hmac = hmac_obj.digest()
        return base64.b64encode(raw_hmac).decode('utf-8')
    except Exception as ex:
        raise RuntimeError(str(ex))


def generate_signature(verb, uri, date, secret_key):
    string_to_sign = f"{verb} {uri}\n{date}"
    return hmac_sha1(secret_key, string_to_sign)


class TtsClient:
    _instance = None
    _lock = Lock()  # 保证线程安全

    def __init__(self):
        self.app_key = lion_config.TTS_APP_KEY
        self.secret_key = lion_config.TTS_SECRET
        self.AUTHORIZATION_FORMAT = "AIAUTH-V1 {}:{}"
        self.env = CURRENT_ENV

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            with cls._lock:  # 确保线程安全
                if cls._instance is None:  # 双重检查
                    cls._instance = cls()
        return cls._instance

    def generate_authorization(self, verb, uri, date, app_key, secret_key):
        signature = generate_signature(verb, uri, date, secret_key)
        return self.AUTHORIZATION_FORMAT.format(app_key, signature)

    def stream_text_synthesize(self, sessionId, index, text, voiceName, speed, volume, sampleRate, enableExtraVolume,
                             extendParams, tolerance, callback):
        url = self.get_host() + "/tts/v1/stream_text/submit"
        date = datetime.datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT")
        headers = {
            'Content-Type': 'application/json',
            'Date': date,
            'Authorization': self.generate_authorization("POST", "/tts/v1/stream_text/submit", date, self.app_key,
                                                         self.secret_key),
            'SessionID': sessionId
        }
        payload = {
            key: value for key, value in {
                "index": index,
                "text": text,
                "voice_name": voiceName,
                "speed": speed,
                "volume": volume,
                "sample_rate": sampleRate,
                "enable_extra_volume": enableExtraVolume,
                "extend_params": extendParams,
                "tolerance": tolerance,
                "src": "python-sdk"
            }.items() if value is not None
        }
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        if response.status_code == 200:
            errcode = response.json().get('errcode')
            if errcode == 0 and index == 1:
                threading.Thread(target=self.pull_audio_stream, args=(sessionId, payload, callback)).start()
            elif errcode != 0:
                callback.on_fail(sessionId, errcode)
        else:
            callback.on_fail(400403, None, None, sessionId)

    def pull_audio_stream(self, sessionId, payload, callback):
        url = self.get_host() + "/tts/v1/audio/stream"
        date = datetime.datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT")

        headers = {
            'Content-Type': 'application/json',
            'Date': date,
            'Authorization': self.generate_authorization("POST", "/tts/v1/audio/stream", date, self.app_key, self.secret_key),
            'SessionID': sessionId
        }

        with requests.post(url, headers=headers, data=json.dumps(payload), stream=True) as response:
            if response.status_code == 200:
                content_type = response.headers.get('Content-Type').split(';')[0].strip()
                if content_type != 'application/json':
                    for chunk in response.iter_content(chunk_size=3200):
                        if chunk:
                            callback.on_success(sessionId, chunk)
                else:
                    errcode = response.json().get('errcode')
                    callback.on_fail(sessionId, errcode)
            else:
                callback.on_fail(sessionId, 400403)

    def stream_synthesize_with_event_stream(self, text, voiceName, speed, volume, sampleRate, audioFormat, enableExtraVolume, extendParams,
                         generator: ThreadedGenerator):
        url = self.get_host() + "/tts/v1/stream"
        sessionId = str(uuid.uuid4())
        date = datetime.datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT")
        headers = {
            'Content-Type': 'application/json',
            'Date': date,
            'Authorization': self.generate_authorization("POST", "/tts/v1/stream", date, self.app_key, self.secret_key),
            'SessionID': sessionId
        }
        payload = {
            key: value for key, value in {
                "text": text,
                "voice_name": voiceName,
                "speed": speed,
                "volume": volume,
                "sample_rate": sampleRate,
                "audio_format": audioFormat,
                "enable_extra_volume": enableExtraVolume,
                "extend_params": extendParams,
                "src": "python-sdk"
            }.items() if value is not None
        }
        response = requests.post(url, headers=headers, data=json.dumps(payload), stream=True)
        # 处理响应结果
        if response.status_code == 200:
            content_type = response.headers.get('content-type').strip().split(';')[0]
            if content_type != 'application/json':
                for chunk in response.iter_content(chunk_size=3200):
                    if chunk:  # 过滤掉keep-alive的新块
                        generator.send_pcm_data(chunk)
            else:
                logger.info("streamSynthesizeOnFail, response is not audio")
                generator.close()
        else:
            logger.info("streamSynthesizeOnFail, response.status_code: " + str(response.status_code))
            generator.close()
        generator.close()


    def stream_synthesize_with_raw_pcm(self, text, voiceName, speed, volume, sampleRate, audioFormat, enableExtraVolume, extendParams):
        url = self.get_host() + "/tts/v1/stream"
        sessionId = str(uuid.uuid4())
        date = datetime.datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT")
        headers = {
            'Content-Type': 'application/json',
            'Date': date,
            'Authorization': self.generate_authorization("POST", "/tts/v1/stream", date, self.app_key, self.secret_key),
            'SessionID': sessionId
        }
        payload = {
            key: value for key, value in {
                "text": text,
                "voice_name": voiceName,
                "speed": speed,
                "volume": volume,
                "sample_rate": sampleRate,
                "audio_format": audioFormat,
                "enable_extra_volume": enableExtraVolume,
                "extend_params": extendParams,
                "src": "python-sdk"
            }.items() if value is not None
        }
        response = requests.post(url, headers=headers, data=json.dumps(payload), stream=True)
        # 处理响应结果
        if response.status_code == 200:
            content_type = response.headers.get('content-type').strip().split(';')[0]
            if content_type != 'application/json':
                for chunk in response.iter_content(chunk_size=3200):
                    if chunk:  # 过滤掉keep-alive的新块
                        yield chunk
            else:
                logger.info("streamSynthesizeOnFail, response is not audio")
        else:
            logger.info("streamSynthesizeOnFail, response.status_code: " + str(response.status_code))


    def call_synthesize(self, text, voiceName="meifanli", speed=50, volume=50, sampleRate=8000, audioFormat="mp3", enableExtraVolume=0, subtitleMode=0, extendParams=None):
        url = self.get_host() + "/tts/v1/synthesis"
        sessionId = str(uuid.uuid4())
        date = datetime.datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT")
        headers = {
            'Content-Type': 'application/json',
            'Date': date,
            'Authorization': self.generate_authorization("POST", "/tts/v1/synthesis", date, self.app_key, self.secret_key),
            'SessionID': sessionId
        }
        payload = {
            key: value for key, value in {
                "text": text,
                "voice_name": voiceName,
                "speed": speed,
                "volume": volume,
                "sample_rate": sampleRate,
                "audio_format": audioFormat,
                "enable_extra_volume": enableExtraVolume,
                "subtitle_mode": subtitleMode,
                "extend_params": extendParams,
                "src": "python-sdk"
            }.items() if value is not None
        }
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        if response.status_code == 200:
            content_type = response.headers.get('content-type').strip().split(';')[0]
            if content_type == 'application/json':
                return SynthesizeResponse(response.json().get('errcode'), None, None, sessionId, None)
            else:
                return SynthesizeResponse(0, response.content, None, sessionId, None)
        else:
            return SynthesizeResponse(400403, None, None, sessionId, None)

    def get_host(self):
        if self.env == "prod":
            return "https://aispeech.sankuai.com"
        else:
            return "http://speechplatform.ai.test.sankuai.com"

if __name__ == "__main__":
    client = TtsClient.get_instance()
    res1 = client.call_synthesize("测试一下，")
    res2 = client.call_synthesize("结束。")
    save_audio_to_wav(res1.data, f"/tmp/res1.wav", sample_rate=8000)
    save_audio_to_wav(res2.data, f"/tmp/res2.wav", sample_rate=8000)

    #call_back = TestTtsCallback()
    # session_id = str(uuid.uuid4())
    # call_tts_api(session_id, 1, "测试一下，", call_back)
    # call_tts_api(session_id, -2, "结束。", call_back)
    # call_stream_synthesize("测试一下，", call_back)
    # call_stream_synthesize("结束。", call_back)
    # if call_back.errorCode is not None:
    #     print(f"Error occurred: {call_back.errorCode}")
    # else:
    #     output_file = "~/Documents/temp/tts.wav"
    #     save_audio_to_wav(call_back.data, output_file, sample_rate=8000)