from utils.logger import logger
from utils.user_info.user_client import UserClient
from utils.squirrel import RedisClient, build_category_key
from configs import lion_config
import json
import requests
import random
import time
from typing import Optional
from service.ai_client import send_to_ai
from threading import Thread
from service.order import get_user_orders_v3, get_user_orders_v2
from es.es_client import get_num, search, delete_doc
from concurrent.futures import ThreadPoolExecutor
import asyncio
# 美团AIGC服务URL
AIGC_URL = 'https://aigc.sankuai.com/v1/openai/native/chat/completions'
PORTRAIT_INDEX = "user_portrait_data"
# ES服务URL
ES_URL = "http://127.0.0.1:8080/weiwei/es-upsert"
ES_SEARCH_URL = "http://127.0.0.1:8080/weiwei/es-search"  # ES搜索接口
# ES_URL = "https://xiaomeiai.meituan.com/weiwei/es-upsert"  # 线上服务器
# ES_SEARCH_URL = "https://xiaomeiai.meituan.com/weiwei/es-search"  # 线上服务器

def get_mt_userid(mis_id):
    """
    获取美团用户ID
    Args:
        mis_id: 美团内部ID
    Returns:
        mt_user_id: 美团用户ID
    """
    try:
        # 1. 从lion配置映射表获取
        misId_userId_map = lion_config.MIS_USER_ID_MAP
        try:
            misId_userId_map = json.loads(misId_userId_map)
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析错误: {e}")
            misId_userId_map = {}
        
        mt_user_id = misId_userId_map.get(mis_id)
        
        if not mt_user_id:
            # 2. 从Redis缓存获取
            redis = RedisClient.get_instance().get_client()
            key = build_category_key("mis2user", "m{0}", mis_id)
            if redis.exists(key):
                mt_user_id = redis.get(key)
            else:
                # 3. 从UserClient服务获取
                logger.info(f"缓存中不存在misID {mis_id}，查询美团用户ID")
                mt_user_id = UserClient.get_client().get_mt_userid_by_mis(mis_id)
                if mt_user_id:
                    redis.set(key, mt_user_id)
        
        return mt_user_id
    
    except Exception as e:
        logger.error(f"获取 mt_user_id 失败: {e}")
        return None

def decode_unicode_json(json_str):
    """解码JSON字符串，支持中文字符"""
    try:
        data = json.loads(json_str)
        if isinstance(data, dict) and 'data' in data:
            decoded_orders = []
            for order_str in data['data']:
                order = json.loads(order_str)
                decoded_orders.append(order)
            data['data'] = decoded_orders
        return data
    except Exception as e:
        logger.error(f"JSON解码错误: {str(e)}")
        return None

def upload_to_es(mis_id: str, mt_user_id: str, portrait: str) -> bool:
    """上传用户画像到ES
    Args:
        mis_id: 美团内部ID
        mt_user_id: 美团用户ID
        portrait: 用户画像内容
    Returns:
        bool: 是否上传成功
    """
    try:
        # 记录当前使用的ES系统类型
        if "127.0.0.1" in ES_URL or "localhost" in ES_URL:
            logger.info(f"当前使用本地ES系统：{ES_URL}")
        else:
            logger.info(f"当前使用线上ES系统：{ES_URL}")
            
        # 构造符合ES格式的数据
        record = {
            "index_name": "user_portrait_data",  # ES索引名
            "doc_id": f"{mis_id}_{mt_user_id}",  # 文档ID
            "doc": {
                "doc_as_upsert": True,
                "doc": {
                    "mis_id_keyword": mis_id,
                    "mis_id_text": mis_id,
                    "mt_user_id_keyword": mt_user_id,
                    "mt_user_id_text": mt_user_id,
                    "user_portrait_keyword": portrait,
                    "user_portrait_text": portrait
                }
            }
        }
        # 获取画像索引中的文档数量
        total_num_response = get_num("portrait_user_info")
        
        # 处理可能的返回值情况
        if total_num_response is None:
            # get_num 可能返回 None，设置默认值为 1
            total_num = 1
            logger.warning("无法获取画像索引文档数量，使用默认值 1")
        elif isinstance(total_num_response, dict) and 'count' in total_num_response:
            # 正常情况下返回 {"count": X}
            total_num = total_num_response['count'] + 1  # 添加新文档，所以+1
            logger.info(f"画像索引当前文档数量: {total_num_response['count']}，新文档编号: {total_num}")
        else:
            # 其他意外情况，设置默认值
            total_num = int(time.time())  # 用当前时间戳作为唯一ID
            logger.warning(f"无法解析画像索引文档数量，使用时间戳作为文档ID: {total_num}")
            
        info_record = {
            "index_name": "portrait_user_info",  # ES索引名
            "doc_id": f"画像{total_num}",  # 文档ID
            "doc": {
                "doc_as_upsert": True,
                "doc": {
                    "mis_id_keyword": mis_id,
                    "mis_id_text": mis_id,
                    "mt_user_id_keyword": mt_user_id,
                    "mt_user_id_text": mt_user_id,
                    "doc_name_keyword": f"画像{total_num}",
                }
            }
        }
        
        # 记录请求数据
        logger.info(f"开始上传用户画像到ES，用户ID: {mt_user_id}")
        logger.debug(f"上传数据格式: {json.dumps(record, ensure_ascii=False)}")
        
        # 设置请求头
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        # 发送请求
        response = requests.post(ES_URL, json=record, headers=headers)
        response_record = requests.post(ES_URL, json=info_record, headers=headers)
        # 记录响应状态
        logger.info(f"ES响应状态码: {response.status_code}")
        
        # 尝试解析响应内容
        try:
            if response.content:
                response_data = response.json()
                logger.debug(f"ES响应内容: {json.dumps(response_data, ensure_ascii=False)}")
                
                # 检查响应中的错误信息
                if isinstance(response_data, dict):
                    message = response_data.get('message')
                    status = response_data.get('status')
                    if message == "失败" or status:
                        logger.error(f"ES服务返回错误: {status}")
                        return False
        except ValueError as e:
            logger.warning(f"ES响应内容解析失败: {str(e)}, 响应内容: {response.text}")
        
        # 根据状态码处理结果
        if 200 <= response.status_code < 300:
            logger.info(f"用户画像上传成功，用户ID: {mt_user_id}")
            return True
        elif response.status_code == 429:
            logger.warning("请求过于频繁，需要限流")
            return False
        elif 400 <= response.status_code < 500:
            logger.error(f"客户端请求错误，状态码: {response.status_code}, 响应: {response.text}")
            return False
        elif 500 <= response.status_code < 600:
            logger.error(f"服务器错误，状态码: {response.status_code}, 响应: {response.text}")
            return False
        else:
            logger.error(f"未知错误，状态码: {response.status_code}, 响应: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        logger.error(f"请求ES服务时发生网络错误: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"上传用户画像到ES时发生错误: {str(e)}")
        return False

def search_user_portrait(mis_id: str, mt_user_id: str) -> dict:
    """从ES中搜索用户画像
    Args:
        mis_id: 美团内部ID
        mt_user_id: 美团用户ID
    Returns:
        dict: 搜索结果，如果未找到返回None
    """
    try:
        # 构造搜索条件，按照指定格式
        search_body = {
            "index_name": "user_portrait_data",
            "query": {
                "query": {
                    "bool": {
                        "should": [
                            {"term": {"mis_id_keyword": mis_id}},
                            {"term": {"mt_user_id_keyword": mt_user_id}}
                        ],
                        "minimum_should_match": 1
                    }
                },
                "size": 1
            }
        }
        
        logger.info(f"开始搜索用户画像，用户ID: {mt_user_id}")
        logger.debug(f"搜索条件: {json.dumps(search_body, ensure_ascii=False)}")
        
        headers = {'Content-Type': 'application/json'}
        response = requests.post(ES_SEARCH_URL, json=search_body, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            # 检查返回状态
            if result.get('status') != 0:
                logger.error(f"ES搜索返回错误状态: {result.get('message')}")
                return None
                
            # 获取hits
            hits = result.get('data', {}).get('hits', {}).get('hits', [])
            if hits:
                hit = hits[0]
                portrait = hit['_source'].get('user_portrait_text')
                logger.info(f"从ES找到已存在的用户画像，用户ID: {mt_user_id}")
                return {"portrait": portrait, "from_cache": True}
            else:
                logger.info(f"ES中未找到用户画像，用户ID: {mt_user_id}")
                return None
        else:
            logger.error(f"ES搜索失败，状态码: {response.status_code}，响应: {response.text}")
            return None
            
    except Exception as e:
        logger.error(f"搜索用户画像时发生错误: {e}")
        return None

def get_user_portrait(mis_id, orders, memory_content):
    """获取用户画像
    Args:
        mis_id: 美团内部ID
    Returns:
        dict: 包含用户画像的字典
    """
    try:
        # 获取用户ID
        mt_user_id = get_mt_userid(mis_id)
        if not mt_user_id:
            logger.error(f"无法获取用户ID，mis_id: {mis_id}")
            return {"error": "无法获取用户ID"}
            
        # 先从ES中搜索是否存在
        cached_portrait = search_user_portrait(mis_id, mt_user_id)
        if cached_portrait:
            logger.info(f"使用缓存的用户画像，用户ID: {mt_user_id}")
            Thread(target=generate_portrait, args=(orders, memory_content, mis_id, mt_user_id)).start()
            return cached_portrait
            
            
        # 生成用户画像
        portrait = generate_portrait(orders, memory_content, mis_id, mt_user_id)
        if not portrait:
            logger.error(f"生成用户画像失败，mis_id: {mis_id}")
            return {"error": "生成用户画像失败"}
            
        return {"portrait": portrait, "from_cache": False}
        
    except Exception as e:
        logger.error(f"获取用户画像时发生错误: {e}")
        return {"error": str(e)}

def generate_portrait(orders, memory_content:str="", mis_id:str="", mt_user_id:str=""):
    """生成用户画像"""
    try:
        # 准备发送给AI的数据
        messages = [{
            "role": "system",
            "content": "作为一个专业的用户画像分析师，请根据以下用户数据生成一份详细的用户画像分析报告。"
        }, {
            "role": "user",
            "content": f"""请根据以下用户信息生成用户画像分析报告：
        ### 用户订单数据:
        {json.dumps(orders, ensure_ascii=False, indent=2)}

        ### 用户对话的历史记忆:
        {memory_content}


        请从以下几个方面进行分析:
        1. 基础消费特征（平均客单价、消费频次、主要消费时段）
        2. 餐饮偏好（主要品类）
        3. 消费行为特征（价格敏感度等）
        4. 用户特征推测（可能的职业、年龄段等）
        5. 消费趋势
        6. 营销建议
        7. 最近可能的情感状态

        请尽可能精简表达，突出关键信息。"""
                }]
        
        # 调用美团AIGC服务
        data = {
            "model": lion_config.lion_client.get_value_with_default("weiwei.portrait_model", "gpt-4o-mini"),  # 使用配置中的模型
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 4000,
        }
        
        response = send_to_ai(data).text
        response = json.loads(response)

        if not response:
            return None
            
        # 解析AI响应
        try:
            portrait = response["choices"][0]["message"]["content"]
        except (KeyError, IndexError) as e:
            logger.error(f"解析AI响应失败: {e}")
            return None
        
        if mis_id and mt_user_id:
            try:
                upload_to_es(mis_id, mt_user_id, portrait)
            except Exception as e:
                logger.error(f"用户画像上传ES失败，但不影响返回结果，用户ID: {mt_user_id}")
                
        return portrait
        
    except Exception as e:
        logger.error(f"生成用户画像时发生错误: {e}")
        return None


async def cycle_update_portrait():
    while True:
        # file_num = get_num("portrait_user_info")
        # logger.info(f"用户画像自动更新系统 当前用户画像数目: {file_num}")
        # if file_num == None:
        #     await asyncio.sleep(60*60*20)
        #     continue
        # nums = set()
        # batch_size = 4
        # for i in range(batch_size):
        #     nums.add(random.randint(0, file_num-1))
        # query = {
        #     "query":{
        #         "bool":{
        #             "should":[
        #                 {"term":{"doc_name_keyword": f"画像{i}"}} for i in nums
        #             ],
        #             "minimum_should_match": 1
        #         }
        #     },
        #     "size": batch_size
        # }
        # result = search(PORTRAIT_INDEX, query)
        # hits = result['hits']['hits']
        # portrait_info = []
        # for hit in hits:
        #     portrait_info.append(hit['_source'])
        # tasks = []
        # async def get_orders(mis_id, mt_user_id):
        #     return mis_id, await get_user_orders_v3(mis_id, mt_user_id)
        
        # tasks = [get_orders(info["mis_id"], info["mt_user_id"]) for info in portrait_info]
        # results = await asyncio.gather(*tasks)
        # orders_dict = {mis_id: orders for mis_id, orders in results}
        # thread_pool = []
        # with ThreadPoolExecutor(max_workers=4) as executor:
        #     for info in portrait_info:
        #         thread_pool.append(executor.submit(get_user_portrait, orders_dict[info["mis_id"]], info["mis_id"], info["mt_user_id"]))
        # for task in thread_pool:
        #     task.future()
        # logger.info(f"用户画像自动更新完成 {info}")
        # await asyncio.sleep(60*60) # 每个小时更新4个人的用户画像
        logger.info("哈哈哈我在这里偷偷运行")
        await asyncio.sleep(30)

        
def update_portrait_by_mis_id(mis_id: str, mt_user_id: str, limit: int = lion_config.AI_ORDER_LIMIT, memory_content: str = ""):
    """根据美团内部ID更新用户画像，整合新的记忆内容

    Args:
        mis_id: 美团内部ID
        mt_user_id: 美团用户ID
        limit: 获取订单数量限制
        memory_content: 新的记忆内容

    Returns:
        bool: 是否成功更新画像
    """
    try:
        # 先查询现有画像
        existing_portrait = search_user_portrait(mis_id, mt_user_id)

        logger.info(f"现有用户画像: {existing_portrait}")
        logger.info(f"新的记忆内容: {memory_content}")
        
        if not existing_portrait:
            # 如果没有现有画像，直接生成新画像
            orders = get_user_orders_v2(mis_id, mt_user_id, limit)
            return get_user_portrait(mis_id, orders, memory_content)

        # 获取订单信息
        logger.info(f"获取订单信息")
        orders = {}
        # orders = get_user_orders_v3(mis_id, mt_user_id)
        
        # 准备发送给AI的数据
        messages = [{
            "role": "system",
            "content": """作为一个专业的用户画像分析师，请根据现有用户画像和新的记忆内容，生成一份更新的用户画像分析报告。
            
            如果新的记忆内容与现有画像的分析维度（基础消费特征、餐饮偏好、消费行为特征、用户特征推测、消费趋势、营销建议、最近情感状态）无关，
            或者与现有画像中的信息重复，请直接返回原画像内容。
            
            如果新的记忆内容提供了有价值的新信息，请在保持原有格式的基础上，整合新旧信息生成更新后的画像。
            请确保更新后的画像仍然包含所有原有的分析维度，并保持相同的格式和结构。"""
        }, {
            "role": "user",
            "content": f"""请根据以下信息更新用户画像：

            ### 现有用户画像:
            {existing_portrait.get('portrait', '')}

            ### 新的记忆内容:
            {memory_content}

            ### 用户订单数据:
            {json.dumps(orders, ensure_ascii=False, indent=2)}

            请分析新的记忆内容是否需要整合到现有画像中。如果需要，请在保持原有格式的基础上更新相关内容。
            如果新的记忆内容与现有分析维度无关或重复，请直接返回原画像。"""
        }]
        
        # 调用美团AIGC服务
        data = {
            "model": lion_config.lion_client.get_value_with_default("weiwei.portrait_model", "gpt-4o-mini"),
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 4000,
        }

        
        response = send_to_ai(data)
        response = response.text
        response = json.loads(response)
        logger.info(f"AI服务返回响应用户画像: {response['choices'][0]['message']['content']}")

        if not response:
            logger.error("AI服务返回空响应")
            return False
            
        # 解析AI响应
        try:
            new_portrait = response["choices"][0]["message"]["content"]
            
            # 上传新的画像到ES
            success = upload_to_es(mis_id, mt_user_id, new_portrait)
            if success:
                logger.info(f"成功更新用户画像，用户ID: {mt_user_id}")
                return True
            else:
                logger.error(f"上传更新后的用户画像失败，用户ID: {mt_user_id}")
                return False
                
        except (KeyError, IndexError) as e:
            logger.error(f"解析AI响应失败: {e}")
            return False
            
    except Exception as e:
        logger.error(f"更新用户画像时发生错误: {e}")
        return False

def delete_user_portrait(mis_id: str, mt_user_id: str) -> bool:
    """删除用户画像记录
    
    Args:
        mis_id: 美团内部ID
        mt_user_id: 美团用户ID
        
    Returns:
        bool: 删除操作是否成功
    """
    try:
        # 构造文档ID
        doc_id = f"{mis_id}_{mt_user_id}"
        
        logger.info(f"开始删除用户画像，mis_id: {mis_id}, mt_user_id: {mt_user_id}, doc_id: {doc_id}")
        
        # 从user_portrait_data索引中删除数据
        portrait_response = delete_doc(PORTRAIT_INDEX, doc_id)
        
        # 尝试查找相关的画像索引记录
        search_body = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"mis_id_keyword": mis_id}},
                        {"term": {"mt_user_id_keyword": mt_user_id}}
                    ]
                }
            },
            "size": 1000
        }
        
        result = search("portrait_user_info", search_body)
        if result and 'hits' in result and 'hits' in result['hits']:
            hits = result['hits']['hits']
            for hit in hits:
                info_doc_id = hit['_id']
                logger.info(f"删除相关画像索引记录，doc_id: {info_doc_id}")
                delete_doc("portrait_user_info", info_doc_id)
        
        if portrait_response:
            logger.info(f"用户画像删除成功，doc_id: {doc_id}")
            return True
        else:
            logger.warning(f"用户画像可能不存在或删除失败，doc_id: {doc_id}")
            return False
            
    except Exception as e:
        logger.error(f"删除用户画像时发生错误: {e}")
        return False

# if __name__ == "__main__":
    # update_portrait_by_mis_id("liulingfeng05", "3343136914", 5, "用户不喜欢海底捞")
    # 测试删除用户画像
    # result = delete_user_portrait("liulingfeng05", "3343136914")
    # print(f"删除用户画像结果: {result}")