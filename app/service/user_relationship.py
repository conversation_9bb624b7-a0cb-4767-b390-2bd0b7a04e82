from datetime import datetime
from utils.logger import logger
from service.share_to_plaza import PLAZA_INDEX
from service.share_to_plaza import get_plaza_shares_by_ids
from es import es_client

# ES索引名称
USER_RELATIONSHIP_INDEX = "user_relationships_test"



def follow_user(mis_id: str, guanzhu_misid: str) -> bool:
    """关注用户
    
    Args:
        mis_id: 关注者mis_id
        guanzhu_misid: 被关注者mis_id
        
    Returns:
        bool: 是否关注成功
    """
    try:
        # 1. 检查是否已经关注过该用户
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"mis_id": mis_id}}
                    ]
                }
            }
        }
        result = es_client.search(USER_RELATIONSHIP_INDEX, query)
        
        # 2. 处理关注者视角
        if result and result.get('hits', {}).get('total', {}).get('value', 0) > 0:
            # 已有记录，检查是否已经关注
            source = result.get('hits', {}).get('hits', [{}])[0].get('_source', {})
            guanzhu_list = source.get('guanzhu_misid', [])
            if not isinstance(guanzhu_list, list):
                guanzhu_list = []
                
            # 检查是否已经关注
            for guanzhu in guanzhu_list:
                if guanzhu.get('id') == guanzhu_misid:
                    return False
                    
            # 添加新的关注
            guanzhu_list.append({
                "id": guanzhu_misid,
                "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            
            update_data = {
                "doc": {
                    "guanzhu_misid": guanzhu_list
                },
                "doc_as_upsert": True
            }
        else:
            # 首次关注，创建新记录
            update_data = {
                "doc": {
                    "mis_id": mis_id,
                    "guanzhu_misid": [{
                        "id": guanzhu_misid,
                        "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }]
                },
                "doc_as_upsert": True
            }
            
        es_client.update_doc(USER_RELATIONSHIP_INDEX, mis_id, update_data)
        
        # 3. 处理被关注者视角
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"mis_id": guanzhu_misid}}
                    ]
                }
            }
        }
        result = es_client.search(USER_RELATIONSHIP_INDEX, query)
        
        if result and result.get('hits', {}).get('total', {}).get('value', 0) > 0:
            # 已有记录，检查是否已经是粉丝
            source = result.get('hits', {}).get('hits', [{}])[0].get('_source', {})
            fensi_list = source.get('fensi_misid', [])
            if not isinstance(fensi_list, list):
                fensi_list = []
                
            # 检查是否已经是粉丝
            for fensi in fensi_list:
                if fensi.get('id') == mis_id:
                    return True
                    
            # 添加新的粉丝
            fensi_list.append({
                "id": mis_id,
                "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            
            update_data = {
                "doc": {
                    "fensi_misid": fensi_list
                },
                "doc_as_upsert": True
            }
        else:
            # 首次被关注，创建新记录
            update_data = {
                "doc": {
                    "mis_id": guanzhu_misid,
                    "fensi_misid": [{
                        "id": mis_id,
                        "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }]
                },
                "doc_as_upsert": True
            }
            
        es_client.update_doc(USER_RELATIONSHIP_INDEX, guanzhu_misid, update_data)
        
        return True
        
    except Exception as e:
        logger.error(f"关注用户时出错: {str(e)}")
        return False

def unfollow_user(mis_id: str, guanzhu_misid: str) -> bool:
    """取消关注用户
    
    Args:
        mis_id: 关注者mis_id
        guanzhu_misid: 被关注者mis_id
        
    Returns:
        bool: 是否取消关注成功
    """
    try:
        # 检查是否已经关注（从关注者视角检查）
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"mis_id": mis_id}}
                    ]
                }
            }
        }
        result = es_client.search(USER_RELATIONSHIP_INDEX, query)
        if not result or result.get('hits', {}).get('total', {}).get('value', 0) == 0:
            return False
            
        # 检查是否在关注列表中
        source = result.get('hits', {}).get('hits', [{}])[0].get('_source', {})
        guanzhu_list = source.get('guanzhu_misid', [])
        if not isinstance(guanzhu_list, list):
            guanzhu_list = []
            
        is_following = False
        for guanzhu in guanzhu_list:
            if guanzhu.get('id') == guanzhu_misid:
                is_following = True
                break
                
        if not is_following:
            return False
            
        # 取消关注关系（关注者视角）
        update_data = {
            "script": {
                "source": "ctx._source.guanzhu_misid.removeIf(item -> item.id == params.guanzhu_misid)",
                "lang": "painless",
                "params": {
                    "guanzhu_misid": guanzhu_misid
                }
            }
        }
        es_client.update_doc(USER_RELATIONSHIP_INDEX, mis_id, update_data)
        
        # 取消关注关系（被关注者视角）
        update_data = {
            "script": {
                "source": "ctx._source.fensi_misid.removeIf(item -> item.id == params.mis_id)",
                "lang": "painless",
                "params": {
                    "mis_id": mis_id
                }
            }
        }
        es_client.update_doc(USER_RELATIONSHIP_INDEX, guanzhu_misid, update_data)
        
        return True
        
    except Exception as e:
        logger.error(f"取消关注用户时出错: {str(e)}")
        return False

def get_following(mis_id: str, offset: int = 0, batch_size: int = 10) -> dict:
    """获取用户的关注列表
    
    Args:
        mis_id: 用户mis_id
        offset: 起始位置
        batch_size: 每页数量
        
    Returns:
        dict: 包含总数、分页信息和关注列表的完整结果
    """
    try:
        # 构建查询，查询所有关注关系
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"mis_id": mis_id}}
                    ]
                }
            }
        }
        
        # 执行查询
        result = es_client.search(USER_RELATIONSHIP_INDEX, query)
        if not result:
            logger.error(f"ES查询返回为空，mis_id: {mis_id}")
            return {
                "mis_id": mis_id,
                "total": 0,
                "offset": offset,
                "batch_size": batch_size,
                "has_more": False,
                "following": []
            }
            
        hits = result.get('hits', {}).get('hits', [])
        
        # 处理结果
        following = []
        for hit in hits:
            source = hit.get('_source', {})
            guanzhu_list = source.get('guanzhu_misid', [])
            if not isinstance(guanzhu_list, list):
                guanzhu_list = []
                
            for guanzhu in guanzhu_list:
                if guanzhu and guanzhu.get('id') and guanzhu.get('create_time'):
                    following.append({
                        "mis_id": guanzhu.get('id'),
                        "create_time": guanzhu.get('create_time')
                    })
        
        # 按时间排序
        following.sort(key=lambda x: datetime.strptime(x.get('create_time', ''), '%Y-%m-%d %H:%M:%S'), reverse=True)
        
        # 返回完整结果，使用实际关注数量作为total
        return {
            "mis_id": mis_id,
            "total": len(following),
            "offset": offset,
            "batch_size": batch_size,
            "has_more": offset + batch_size < len(following),
            "following": following[offset:offset + batch_size]
        }
        
    except Exception as e:
        logger.error(f"获取关注列表时出错: {str(e)}")
        return {
            "mis_id": mis_id,
            "total": 0,
            "offset": offset,
            "batch_size": batch_size,
            "has_more": False,
            "following": []
        }

def get_followers(mis_id: str, offset: int = 0, batch_size: int = 10) -> dict:
    """获取用户的粉丝列表
    
    Args:
        mis_id: 用户mis_id
        offset: 起始位置
        batch_size: 每页数量
        
    Returns:
        dict: 包含总数、分页信息和粉丝列表的完整结果
    """
    try:
        # 构建查询，查询所有粉丝关系
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"mis_id": mis_id}}
                    ]
                }
            }
        }
        
        # 执行查询
        result = es_client.search(USER_RELATIONSHIP_INDEX, query)
        if not result:
            logger.error(f"ES查询返回为空，mis_id: {mis_id}")
            return {
                "mis_id": mis_id,
                "total": 0,
                "offset": offset,
                "batch_size": batch_size,
                "has_more": False,
                "followers": []
            }
            
        hits = result.get('hits', {}).get('hits', [])
        
        # 处理结果
        followers = []
        for hit in hits:
            source = hit.get('_source', {})
            fensi_list = source.get('fensi_misid', [])
            if not isinstance(fensi_list, list):
                fensi_list = []
                
            for fensi in fensi_list:
                if fensi and fensi.get('id') and fensi.get('create_time'):
                    followers.append({
                        "mis_id": fensi.get('id'),
                        "create_time": fensi.get('create_time')
                    })
        
        # 按时间排序
        followers.sort(key=lambda x: datetime.strptime(x.get('create_time', ''), '%Y-%m-%d %H:%M:%S'), reverse=True)
        
        # 返回完整结果，使用实际粉丝数量作为total
        return {
            "mis_id": mis_id,
            "total": len(followers),
            "offset": offset,
            "batch_size": batch_size,
            "has_more": offset + batch_size < len(followers),
            "followers": followers[offset:offset + batch_size]
        }
        
    except Exception as e:
        logger.error(f"获取粉丝列表时出错: {str(e)}")
        return {
            "mis_id": mis_id,
            "total": 0,
            "offset": offset,
            "batch_size": batch_size,
            "has_more": False,
            "followers": []
        }
def update_unread_count(mis_id: str, count_type: str) -> bool:
    """更新用户的未读消息数量
    
    Args:
        mis_id: 用户ID
        count_type: 消息类型，可选值：'like', 'comment'
        
    Returns:
        bool: 是否更新成功
    """
    try:
        # 只处理点赞和评论类型的消息
        if count_type not in ['like', 'comment']:
            return False
            
        # 构建查询条件
        query = {
            "query": {
                "term": {
                    "mis_id": mis_id
                }
            }
        }
        
        # 获取用户关系记录
        result = es_client.search(USER_RELATIONSHIP_INDEX, query)
        if not result or not result.get('hits', {}).get('hits'):
            return False
            
        # 获取文档ID和当前值
        doc = result['hits']['hits'][0]['_source']
        doc_id = result['hits']['hits'][0]['_id']
        
        # 更新对应字段的计数，使用get()方法安全获取当前值
        if count_type == 'like':
            current_count = doc.get('unread_like', 0)  # 如果字段不存在，默认为0
            doc['unread_like'] = current_count + 1
        else:  # comment
            current_count = doc.get('unread_comment', 0)  # 如果字段不存在，默认为0
            doc['unread_comment'] = current_count + 1
        
        # 直接更新文档
        success = es_client.update_doc(
            USER_RELATIONSHIP_INDEX,
            doc_id,
            {"doc": doc}
        )
        return success
        
    except Exception as e:
        logger.error(f"更新未读消息数量时出错: {str(e)}")
        return False

def get_unread_counts(mis_id: str) -> dict:
    """获取用户的未读消息数量
    
    Args:
        mis_id: 用户ID
        
    Returns:
        dict: 包含未读点赞和评论数量的字典
    """
    try:
        # 构建查询条件
        query = {
            "query": {
                "term": {
                    "mis_id": mis_id
                }
            }
        }
        
        # 获取用户关系记录
        result = es_client.search(USER_RELATIONSHIP_INDEX, query)
        if not result or not result.get('hits', {}).get('hits'):
            return {
                "unread_like_count": 0,
                "unread_comment_count": 0
            }
            
        # 获取未读消息数量
        doc = result['hits']['hits'][0]['_source']
        return {
            "unread_like_count": doc.get('unread_like'),
            "unread_comment_count": doc.get('unread_comment')
        }
        
    except Exception as e:
        logger.error(f"获取未读消息数量时出错: {str(e)}")
        return {
            "unread_like_count": 0,
            "unread_comment_count": 0
        }

def mark_as_read(mis_id: str, count_type: str) -> bool:
    """将用户的未读消息标记为已读
    
    Args:
        mis_id: 用户ID
        count_type: 消息类型，可选值：'like', 'comment'
        
    Returns:
        bool: 是否标记成功
    """
    try:
        # 只处理点赞和评论类型的消息
        if count_type not in ['like', 'comment']:
            return False
            
        # 构建查询条件
        query = {
            "query": {
                "term": {
                    "mis_id": mis_id
                }
            }
        }
        
        # 获取用户关系记录
        result = es_client.search(USER_RELATIONSHIP_INDEX, query)
        if not result or not result.get('hits', {}).get('hits'):
            return False
            
        # 获取文档ID
        doc_id = result['hits']['hits'][0]['_id']
        
        # 构建更新脚本
        script = {
            "script": {
                "source": f"ctx._source.{count_type}_count = 0",
                "lang": "painless"
            }
        }
        
        # 更新ES中的未读消息数量
        success = es_client.update_doc(USER_RELATIONSHIP_INDEX, doc_id, script)
        return success
        
    except Exception as e:
        logger.error(f"标记消息为已读时出错: {str(e)}")
        return False

def clear_unread_count(mis_id: str, count_type: str) -> bool:
    """清除用户的未读消息数量
    
    Args:
        mis_id: 用户ID
        count_type: 消息类型，可选值：'like', 'comment', 'all'
        
    Returns:
        bool: 是否清除成功
    """
    try:
        # 构建查询条件
        query = {
            "query": {
                "term": {
                    "mis_id": mis_id
                }
            }
        }
        
        # 获取用户关系记录
        result = es_client.search(USER_RELATIONSHIP_INDEX, query)
        if not result or not result.get('hits', {}).get('hits'):
            return True  # 没有记录也算成功
            
        # 获取现有记录
        doc = result['hits']['hits'][0]['_source']
        doc_id = result['hits']['hits'][0]['_id']
        
        # 根据类型清除未读计数
        if count_type == 'all':
            doc['unread_like'] = 0
            doc['unread_comment'] = 0
        elif count_type == 'like':
            doc['unread_like'] = 0
        elif count_type == 'comment':
            doc['unread_comment'] = 0
        else:
            return False
            
        # 更新文档
        success = es_client.update_doc(
            USER_RELATIONSHIP_INDEX,
            doc_id,
            {"doc": doc}
        )
        
        return success
        
    except Exception as e:
        logger.error(f"清除未读消息数量时出错: {str(e)}")
        return False

def get_following_shares(mis_id: str, offset: int = 0, batch_size: int = 10) -> dict:
    """获取用户关注的人的所有帖子
    
    Args:
        mis_id: 用户mis_id
        offset: 起始位置
        batch_size: 每页数量
        
    Returns:
        dict: 包含总数、分页信息和帖子列表的完整结果
    """
    try:
        # 1. 获取关注列表
        following_result = get_following(mis_id)
        if not following_result or not following_result.get('following'):
            logger.info(f"用户 {mis_id} 没有关注任何人")
            return {
                "total": 0,
                "total_count": 0,
                "offset": offset,
                "batch_size": batch_size,
                "has_more": False,
                "shares": []
            }
        
        # 2. 获取所有关注人的mis_id
        following_mis_ids = [user.get('mis_id') for user in following_result.get('following', [])]
        
        # 3. 构建ES查询，获取这些用户的所有帖子
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"terms": {"mis_id_keyword": following_mis_ids}}
                    ]
                }
            },
            "sort": [
                {"time.keyword": {"order": "desc"}}  # 按时间倒序排序
            ],
            "from": offset,
            "size": batch_size
        }
        
        # 4. 执行查询
        search_result = es_client.search(PLAZA_INDEX, query)
        
        if not search_result:
            logger.warning(f"未找到关注用户的帖子")
            return {
                "total": 0,
                "total_count": 0,
                "offset": offset,
                "batch_size": batch_size,
                "has_more": False,
                "shares": []
            }
        
        # 5. 处理搜索结果
        share_ids = []
        total = 0
        if isinstance(search_result, dict) and 'hits' in search_result:
            hits = search_result['hits']
            if 'hits' in hits:
                share_ids = [hit['_id'] for hit in hits['hits'] if '_id' in hit]
                total = hits.get('total', {}).get('value', 0)
        
        # 6. 获取帖子详情
        shares = get_plaza_shares_by_ids(share_ids, mis_id) if share_ids else []
        
        return {
            "total": len(shares),
            "total_count": total,
            "offset": offset,
            "batch_size": batch_size,
            "has_more": offset + batch_size < total,
            "shares": shares
        }
        
    except Exception as e:
        logger.error(f"获取关注用户帖子时出错: {str(e)}")
        return {
            "total": 0,
            "total_count": 0,
            "offset": offset,
            "batch_size": batch_size,
            "has_more": False,
            "shares": []
        }
