import json

from octo_rpc import NonMeshClient, load
import os
import requests
from typing import Dict, List, Union
from utils.logger import logger
from configs.config import CURRENT_ENV
from my_mysql.entity import vector_record
from concurrent.futures import ThreadPoolExecutor

current_dir = os.path.dirname(os.path.abspath(__file__))
vex = load(os.path.join(current_dir, "vex.thrift"))

client = NonMeshClient(
    service=vex.Vex,  # echo是前面load时赋值的变量，PyEcho是.thrift文件中的service名字
    service_name="com.meituan.horus.vexv4.Vex",
    appkey="com.sankuai.dialogstudio.xiaomei.toolexecute",  # 客户端的appkey，例如 "com.sankuai.a.client"
    remote_appkey="com.sankuai.horus.vex.manager",  # 服务端的appkey，例如 "com.sankuai.a.server"
)

group_name = "v1"
table_name = "ai_agent" if CURRENT_ENV == "prod" else "ai_agent_test"
dimension = "custom_768"


def delete(global_id: int):
    try:
        delete_request = vex.VexDeleteRequest()
        delete_request.imagesetName = "xiaomeiAI"
        delete_request.tableName = table_name
        delete_request.globalId = global_id
        delete_response = client.deleteImage2(delete_request)
        logger.info(f"删除响应: {delete_response}")
        vector_record.delete_vector(global_id)
        return delete_response
    except Exception as e:
        logger.error(f"发生错误: {str(e)}")
        raise


def add(text: str) -> dict:
    try:
        # 先检查是否有一样的，避免重复插入
        result:dict[str, dict] = search(text, 3)  # 这里的search返回dict，其中key是内容，value是globalID和score

        if text in list(result.keys()):
            response = {
                "code": 400,
                "message": "数据已存在",
                "text": text,
                "globalId": result[text]["globalId"]
            }
            return response
        # 构建请求
        vector = embedding(text)
        if vector == None:
            return {
                "code": 401,
                "message": "embedding失败",
                "globalID": -1
            }
        add_request = vex.VexAddRequest()
        add_request.imagesetName = "xiaomeiAI"
        add_request.tableName = table_name
        add_request.url = text
        add_request.label = group_name
        add_request.groupName = group_name

        feature = vex.VexFeature()
        feature.vector = vector
        feature_map = {dimension: feature}
        add_request.featureMap = feature_map

        add_response = client.add(add_request)
        logger.info(f"vex添加响应: {add_response} {text}")
        if add_response.globalId is not None and add_response.globalId != 0:
            try:
                vector_record.insert_vector(add_response.globalId, text) # 插入sql服务器
                return {
                    "code": 0,
                    "text": text,
                    "globalId": add_response.globalId
                }
            except Exception as e:
                logger.error(f"插入sql服务器失败，信息内容为{text} error: {str(e)}")
                return {
                    "code": 403,
                    "message": "插入sql服务器失败",
                    "text": text,
                    "globalID": add_response.globalId
                }
        else:
            return {
                "code": 402,
                "message": "vex插入失败",
                "globalID": -1
            }
        
    except Exception as e:
        logger.error(f"发生错误: {str(e)}")
        raise

def process_search_request(idx, vector, text:str, table_name, topk, group_name, client)->list[dict]:
    search_request = vex.VexSearchRequest()
    search_request.imagesetName = "xiaomeiAI"
    search_request.tableName = table_name
    search_request.url = text
    search_request.topk = topk
    search_request.groupName = group_name

    feature = vex.VexFeature()
    feature.vector = vector
    feature_map = {dimension: feature}
    search_request.featureMap = feature_map

    search_response = client.search(search_request)  
    result = []
    if search_response is None or search_response.items is None:
        return result
    vectors = get_feature([item.globalId for item in search_response.items])
    for item, vector in zip(search_response.items, vectors):
        result.append({
            "value": item.url,
            "score": float(item.similarity),
            "globalId": item.globalId,
            "vector": vector
        })
    return result

def search(text: Union[str, List[str]], topk: int = 5) -> list[dict]:
    try:
        
        if isinstance(text, str):
            # text2tensor
            vector:List[float]|None = embedding(text)
            if vector == None:
                return {}
            search_request = vex.VexSearchRequest()
            search_request.imagesetName = "xiaomeiAI"
            search_request.tableName = table_name
            search_request.url = text
            search_request.topk = topk
            search_request.groupName = group_name

            feature = vex.VexFeature()
            feature.vector = vector
            feature_map = {dimension: feature}
            search_request.featureMap = feature_map

            search_response = client.search(search_request)
            result = []
            if search_response is None or search_response.items is None:
                return result
            for item in search_response.items:
                result.append({
                    "value": item.url,
                    "score": float(item.similarity),
                    "globalId": item.globalId
                })
            result.sort(key=lambda x: x["score"], reverse=True)
            return result
        elif isinstance(text, list):
            vectors:List[float]|None = embedding(text)
            if vectors == None:
                return []
            results = []
            with ThreadPoolExecutor(max_workers=4) as executor:
                futures = [executor.submit(process_search_request, idx, vector, text[idx], table_name, topk, group_name, client) for idx, vector in enumerate(vectors)]
                for future in futures:
                    result = future.result()
                    if result and len(result) != 0:
                        results.extend(result)
            results.sort(key=lambda x: x["score"], reverse=True)
            return results
        else:
            logger.error(f"Vex Search Parameters Error. Expected a string or a list of strings, but got {type(text)}")
            return []
        
    except Exception as e:
        logger.error(f"Search发生错误: {str(e)}")
        return []


# 将来可能用于算rank
def search_plus(text: Union[str, List[str]], topk: int = 5) -> list[dict]:
    try:
        
        if isinstance(text, str):
            # text2tensor
            text_embeddings:List[float]|None = embedding(text)
            if text_embeddings == None:
                return {}
            search_request = vex.VexSearchRequest()
            search_request.imagesetName = "xiaomeiAI"
            search_request.tableName = table_name
            search_request.url = text
            search_request.topk = topk
            search_request.groupName = group_name

            feature = vex.VexFeature()
            feature.vector = text_embeddings
            feature_map = {dimension: feature}
            search_request.featureMap = feature_map

            search_response = client.search(search_request)
            result = []
            if search_response is None or search_response.items is None:
                return result
            vectors = get_feature([item.globalId for item in search_response.items])
            for item, vec in zip(search_response.items, vectors):
                result.append({
                    "value": item.url,
                    "score": float(item.similarity),
                    "globalId": item.globalId,
                    "vector": vec
                })
            result.sort(key=lambda x: x["score"], reverse=True)
            return result, [text_embeddings]
        elif isinstance(text, list):
            text_embeddings:List[float]|None = embedding(text)
            if text_embeddings == None:
                return []
            results = []
            for idx, _ in enumerate(text_embeddings):
                search_request = vex.VexSearchRequest()
                search_request.imagesetName = "xiaomeiAI"
                search_request.tableName = table_name
                search_request.url = text[idx]
                search_request.topk = topk
                search_request.groupName = group_name

                feature = vex.VexFeature()
                feature.vector = text_embeddings[idx]
                feature_map = {dimension: feature}
                search_request.featureMap = feature_map

                search_response = client.search(search_request)  
                result = []
                if search_response is None or search_response.items is None:
                    
                    continue
                vectors = get_feature([item.globalId for item in search_response.items])
                for item, vec in zip(search_response.items, vectors):
                    result.append({
                        "value": item.url,
                        "score": float(item.similarity),
                        "globalId": item.globalId,
                        "vector": vec
                    })
                results.extend(result)
            results.sort(key=lambda x: x["score"], reverse=True)
            return results, text_embeddings
        else:
            logger.error(f"Vex Search Parameters Error. Expected a string or a list of strings, but got {type(text)}")
            return []
        
    except Exception as e:
        logger.error(f"发生错误: {str(e)}")
        return []


def get_feature(global_ids: list) -> list[float]|None:
    global table_name
    search_response = client.getImagesFeature("xiaomeiAI", table_name, global_ids)
    if search_response.code != 0 or len(search_response.imageMap) == 0:
        return None

    
    ret_tensor = []
    globalid_2_vector = search_response.imageMap
    for idx, id in enumerate(global_ids):
        
        vector = globalid_2_vector[id].featureMap["custom_768"].vector
        # logger.info(f"向量: {vector}")
        ret_tensor.append(vector)
    # logger.info(f"vex召回结果: {ret_tensor}")
    return ret_tensor


def embedding(text: Union[str, List[str]]) -> List[float]|None:
    url = 'https://aigc.sankuai.com/v1/openai/native/embeddings'
    headers = {
        'Authorization': 'Bearer 1663502478995767378',
        'Content-Type': 'application/json'
    }
    data = {
        'model': 'text-embedding-miffy-002'
    }
    if isinstance(text, str):
        data['input'] = text
        try:
            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()  # 检查HTTP请求是否成功
            result = response.json()
            return result.get('data')[0].get('embedding')
        except requests.exceptions.RequestException as e:
            logger.error(f"请求embedding接口失败: {e}")
            return None
        except ValueError as e:
            logger.error(f"解析响应失败: {e}")
            return None
    elif isinstance(text, list):
        total_num = len(text)
        return_tensors = []
        for idx in range((total_num // 4) + 1):
            data['input'] = text[idx * 4:(idx + 1) * 4]
            if len(data['input']) == 0:
                break
            try:    
                response = requests.post(url, headers=headers, json=data)
                # logger.info(f"请求响应: {response.text}")
                response.raise_for_status()  # 检查HTTP请求是否成功
                result = response.json()
                return_tensors += [data.get('embedding') for data in result.get('data')]
            except Exception as e:
                logger.error(f"请求embedding接口失败: {e}")
                return None
        return return_tensors
    else:
        logger.error(f"Vex Embedding Parameters Error. Expected a string or a list of strings, but got {type(text)}")
        return {"status": "error",
                "message": f"Vex Embedding Parameters Error. Expected a string or a list of strings, but got {type(text)}"}


if __name__ == "__main__":
    print(add("一条新数据1"))
    print(search("一条新数据1", 3))
