#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
美团外卖POI服务Thrift客户端
使用Thrift接口访问美团外卖POI查询服务
"""

from octo_rpc import NonMeshClient, load
import os
from typing import Dict, List, Set, Optional, Union, Any
import logging
from configs.config import APP_KEY

# 设置日志
logger = logging.getLogger(__name__)

# 加载thrift定义
current_dir = os.path.dirname(os.path.abspath(__file__))
wm_poi = load(os.path.join(current_dir, "wm_poi_query.thrift"))

# 创建Thrift客户端
client = NonMeshClient(
    service=wm_poi.WmPoiQueryThriftService,
    service_name="com.sankuai.meituan.waimai.poi.thrift.WmPoiQueryThriftService",
    appkey=APP_KEY,  # 客户端的appkey
    remote_appkey="com.sankuai.waimai.poiquery",  # 服务端的appkey
)

# 常用字段列表
ALL_FIELDS = {
    "wmPoiId", "poiName", "latitude", "longitude", "address", "phone", 
    "status", "businessStatus", "businessTime", "pictureUrl", 
    "avgPrice", "avgScore", "deliveryType", "minDeliveryAmount", 
    "deliveryFee", "deliveryTime", "extraInfo"
}

class WmPoiThriftClient:
    """
    美团外卖POI Thrift查询客户端
    对应 waimai_service_poiquery_client
    """
    
    @staticmethod
    def get_poi_by_id(poi_id: int, fields: Optional[Set[str]] = None) -> Optional[Dict[str, Any]]:
        """
        根据POI ID获取商户信息
        
        Args:
            poi_id: 商户ID
            fields: 需要查询的字段，默认为所有字段
            
        Returns:
            商户详细信息
        """
        if fields is None:
            fields = ALL_FIELDS
            
        try:
            # 调用Thrift接口
            poi_info = client.getWmPoiAggreByWmPoiIdWithSpecificField(poi_id, fields)
            
            # 将Thrift对象转换为字典
            if poi_info:
                return WmPoiThriftClient._convert_poi_to_dict(poi_info)
            return None
        except wm_poi.WmServerException as e:
            logger.error(f"获取POI信息失败: 错误码={e.code}, 错误信息={e.message}")
            return None
        except Exception as e:
            logger.error(f"获取POI信息失败: {str(e)}")
            return None
    
    @staticmethod
    def get_poi_list_by_ids(poi_ids: List[int], fields: Optional[Set[str]] = None) -> List[Dict[str, Any]]:
        """
        批量获取POI信息
        
        Args:
            poi_ids: 商户ID列表
            fields: 需要查询的字段，默认为所有字段
            
        Returns:
            商户信息列表
        """
        if fields is None:
            fields = ALL_FIELDS
            
        if not poi_ids:
            return []
            
        # 限制单次查询数量不超过300
        if len(poi_ids) > 300:
            logger.warning(f"POI ID列表过长，已截断为前300个: {len(poi_ids)} -> 300")
            poi_ids = poi_ids[:300]
            
        try:
            # 调用Thrift接口
            poi_list = client.mgetWmPoiAggreByWmPoiIdWithSpecificField(poi_ids, fields)
            
            # 将Thrift对象列表转换为字典列表
            if poi_list:
                return [WmPoiThriftClient._convert_poi_to_dict(poi) for poi in poi_list]
            return []
        except wm_poi.WmServerException as e:
            logger.error(f"批量获取POI信息失败: 错误码={e.code}, 错误信息={e.message}")
            return []
        except Exception as e:
            logger.error(f"批量获取POI信息失败: {str(e)}")
            return []
    
    @staticmethod
    def search_poi(
        keyword: Optional[str] = None,
        latitude: Optional[float] = None,
        longitude: Optional[float] = None,
        radius: int = 3000,
        limit: int = 20,
        fields: Optional[Set[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        搜索商户信息
        
        Args:
            keyword: 搜索关键词
            latitude: 纬度
            longitude: 经度
            radius: 搜索半径(米)
            limit: 返回结果数量限制
            fields: 需要查询的字段，默认为所有字段
            
        Returns:
            商户列表
        """
        if fields is None:
            fields = ALL_FIELDS
            
        if not keyword and (latitude is None or longitude is None):
            logger.warning("搜索POI时需要提供关键词或经纬度信息")
            return []
            
        try:
            # 调用Thrift接口
            # 如果未提供关键词，使用空字符串
            keyword = keyword or ""
            # 如果未提供经纬度，使用默认值0
            lat = latitude or 0.0
            lng = longitude or 0.0
            
            poi_list = client.searchWmPoiAggre(keyword, lat, lng, radius, limit, fields)
            
            # 将Thrift对象列表转换为字典列表
            if poi_list:
                return [WmPoiThriftClient._convert_poi_to_dict(poi) for poi in poi_list]
            return []
        except wm_poi.WmServerException as e:
            logger.error(f"搜索POI失败: 错误码={e.code}, 错误信息={e.message}")
            return []
        except Exception as e:
            logger.error(f"搜索POI失败: {str(e)}")
            return []
    
    @staticmethod
    def _convert_poi_to_dict(poi: 'wm_poi.WmPoiAggre') -> Dict[str, Any]:
        """
        将Thrift对象转换为字典
        
        Args:
            poi: Thrift POI对象
            
        Returns:
            POI信息字典
        """
        result = {}
        
        # 添加基本字段
        if hasattr(poi, 'wmPoiId'):
            result['wmPoiId'] = poi.wmPoiId
        if hasattr(poi, 'poiName'):
            result['poiName'] = poi.poiName
        if hasattr(poi, 'latitude'):
            result['latitude'] = poi.latitude
        if hasattr(poi, 'longitude'):
            result['longitude'] = poi.longitude
        if hasattr(poi, 'address'):
            result['address'] = poi.address
        if hasattr(poi, 'phone'):
            result['phone'] = poi.phone
        if hasattr(poi, 'status'):
            result['status'] = poi.status
        if hasattr(poi, 'businessStatus'):
            result['businessStatus'] = poi.businessStatus
        if hasattr(poi, 'businessTime'):
            result['businessTime'] = poi.businessTime
        if hasattr(poi, 'pictureUrl'):
            result['pictureUrl'] = poi.pictureUrl
        if hasattr(poi, 'avgPrice'):
            result['avgPrice'] = poi.avgPrice
        if hasattr(poi, 'avgScore'):
            result['avgScore'] = poi.avgScore
        if hasattr(poi, 'deliveryType'):
            result['deliveryType'] = poi.deliveryType
        if hasattr(poi, 'minDeliveryAmount'):
            result['minDeliveryAmount'] = poi.minDeliveryAmount
        if hasattr(poi, 'deliveryFee'):
            result['deliveryFee'] = poi.deliveryFee
        if hasattr(poi, 'deliveryTime'):
            result['deliveryTime'] = poi.deliveryTime
        if hasattr(poi, 'extraInfo') and poi.extraInfo:
            result['extraInfo'] = dict(poi.extraInfo)
            
        return result


# 使用示例
def example_usage():
    """
    使用示例
    """
    # 获取单个POI信息
    poi_info = WmPoiThriftClient.get_poi_by_id(123456)
    if poi_info:
        print(f"POI信息: {poi_info}")
    
    # 批量获取POI信息
    poi_list = WmPoiThriftClient.get_poi_list_by_ids([123456, 789012])
    if poi_list:
        print(f"POI列表: {poi_list}")
    
    # 搜索POI
    search_result = WmPoiThriftClient.search_poi(
        keyword="奶茶",
        latitude=39.9087,
        longitude=116.3975,
        radius=2000,
        limit=10
    )
    if search_result:
        print(f"搜索结果: {search_result}")


if __name__ == "__main__":
    # 仅作为示例，实际使用时请替换为真实的POI ID
    example_usage()