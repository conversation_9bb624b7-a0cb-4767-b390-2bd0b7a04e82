namespace java com.sankuai.meituan.waimai.poi.thrift
namespace py takeout.thrift

/**
 * 美团外卖门店查询服务Thrift接口定义
 */

// 异常定义
exception WmServerException {
    1: i32 code
    2: string message
}

// 门店聚合信息结构
struct WmPoiAggre {
    1: required i64 wmPoiId                // 外卖门店ID
    2: optional string poiName             // 门店名称
    3: optional double latitude            // 纬度
    4: optional double longitude           // 经度
    5: optional string address             // 地址
    6: optional string phone               // 电话
    7: optional i32 status                 // 状态
    8: optional i32 businessStatus         // 营业状态
    9: optional string businessTime        // 营业时间
    10: optional string pictureUrl         // 图片URL
    11: optional i32 avgPrice              // 人均价格
    12: optional double avgScore           // 平均评分
    13: optional i32 deliveryType          // 配送类型
    14: optional i32 minDeliveryAmount     // 最低配送金额
    15: optional i32 deliveryFee           // 配送费
    16: optional i32 deliveryTime          // 配送时间
    17: optional map<string, string> extraInfo  // 额外信息
    // 可以根据需要添加更多字段
}

// 门店查询服务接口
service WmPoiQueryThriftService {
    /**
     * 查询批量门店信息
     * @param wmPoiIdList        外卖门店ID列表，单批次小于300个。
     * @param fieldsNeeded       需要查询的门店字段, 只能包含WmPoiFieldQueryConstant常量类中包含的字段, 录入非法字段将抛出异常。
     * @throws WmServerException -
     * @return List<WmPoiAggre>  门店信息列表，不指定的字段不会在WmPoiAggre返回
     */
    list<WmPoiAggre> mgetWmPoiAggreByWmPoiIdWithSpecificField(1: list<i64> wmPoiIdList, 2: set<string> fieldsNeeded) throws (1: WmServerException wmException)
    
    /**
     * 根据单个门店ID查询门店信息
     * @param wmPoiId           外卖门店ID
     * @param fieldsNeeded      需要查询的门店字段
     * @throws WmServerException -
     * @return WmPoiAggre       门店信息
     */
    WmPoiAggre getWmPoiAggreByWmPoiIdWithSpecificField(1: i64 wmPoiId, 2: set<string> fieldsNeeded) throws (1: WmServerException wmException)
    
    /**
     * 根据条件搜索门店
     * @param keyword           搜索关键词
     * @param latitude          纬度
     * @param longitude         经度
     * @param radius            搜索半径(米)
     * @param limit             返回结果数量限制
     * @param fieldsNeeded      需要查询的门店字段
     * @throws WmServerException -
     * @return list<WmPoiAggre> 门店信息列表
     */
    list<WmPoiAggre> searchWmPoiAggre(1: string keyword, 2: double latitude, 3: double longitude, 4: i32 radius, 5: i32 limit, 6: set<string> fieldsNeeded) throws (1: WmServerException wmException)
}
