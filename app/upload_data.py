from service.share_to_plaza import update_plaza_share_content, delete_plaza_share, upload_to_plaza
# 更新多个字段

# update_data = {
#     "like_count": 10,
#     "dislike_count": 2,
#     "comment_count": 5,
#     "comment_record": {
#         "comment1": {
#             "content": "评论内容",
#             "mis_id": "user123",
#             "create_time": "2024-01-01 12:00:00"
#         }
#     }
# }
# update_plaza_share_content("liulingfeng05_share_55", update_data, "liulingfeng05")

# 上传分享内容
formatted_time = "2025-04-08 11:00:00"
title = "测试分享"
content = "这是一条测试分享内容，最近在安妮意大利餐厅（望京店）点外卖，A2什锦蔬菜扒和P9安妮比萨都非常好吃，量足而且价格也很亲民，总共只需106元！强烈推荐给喜欢意大利美食的朋友们！"
mis_id = "wangyingqing"
user_content = "用户自定义内容"
share_identifier = "安妮意大利餐厅外卖：美味又实惠"
shop_name = "测试商店"
order_detail = ["订单1"]
money_detail = 100.0
food_img_urls = ["http://example.com/image1.jpg", "http://example.com/image2.jpg"]
shop_url = ["http://example.com/shop1"]

# 评论记录
comment_record = [
    {
        "comment_content": "这是一条测试评论",
        "comment_id": "comment_1",
        "comment_mis_id": "wangyingqing",
        "comment_parent_id": "liulingfeng05",
        "create_time": "2025-04-08T12:01:00.000Z",
        "is_deleted": False
    },
    {
        "comment_content": "这是一条回复评论",
        "comment_id": "comment_2",
        "comment_mis_id": "liulingfeng05",
        "comment_parent_id": "wangyingqing",
        "create_time": "2025-04-08T12:02:00.000Z",
        "is_deleted": False
    }
]

# 上传到广场
upload_to_plaza(
    formatted_time=formatted_time,
    title=title,
    content=content,
    mis_id=mis_id,
    user_content=user_content,
    share_identifier=share_identifier,
    shop_name=shop_name,
    order_detail=order_detail,
    money_detail=money_detail,
    food_img_urls=food_img_urls,
    shop_url=shop_url,
    comment_record=comment_record
)