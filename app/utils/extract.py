import re
import regex
from typing import Optional, Tuple, Union
from datetime import datetime, timedelta
import json
from utils.logger import logger
from collections import defaultdict

# chat_with_ai_v2中调用
def process_ingredient_data(data:str):
    """精简食材数据，只保留最关键的信息"""
    try:
        # 如果是字符串，先尝试直接解析
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试找到合法的JSON部分
                start = data.find("{")
                end = data.rfind("}") + 1
                if start >= 0 and end > start:
                    json_str = data[start:end]
                    # 清理可能导致解析失败的字符
                    json_str = json_str.replace("\n", "").replace("\r", "").strip()
                    try:
                        data = json.loads(json_str)
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析错误已被处理: {str(e)}")
                        return "数据格式错误"
                else:
                    return "数据格式错误"

        result = []

        # 提取最受欢迎的菜品（只取前5个）
        if "最受欢迎的菜品" in data:
            top_dishes = []
            for dish in data["最受欢迎的菜品"][:10]:
                top_dishes.append(f"{dish['name']}(热度{dish['heat']})")
            if top_dishes:
                result.append(f"热门菜品：{', '.join(top_dishes)}")

        # 提取最受欢迎的口味（如果有）
        if "dish_analysis" in data and "最受欢迎的口味" in data["dish_analysis"]:
            flavors = data["dish_analysis"]["最受欢迎的口味"]
            if flavors:
                result.append(f"流行口味：{', '.join(flavors)}")

        return "；".join(result) if result else "无数据"

    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误已被处理: {str(e)}")
        logger.error(f"原始数据: {data[:200]}...")  # 只打印前200个字符
        return "数据格式错误"
    except Exception as e:
        logger.error(f"处理食材数据时出错: {str(e)}")
        if isinstance(data, str):
            return data[:1200]  # 如果处理失败，返回前1200个字符的原始数据
        return "处理错误"

# 新增判断逻辑中需要用到价格区间
def extract_price_with_currency(text: str) -> Optional[Tuple[str, float]]:
    """
    提取价格和货币符号
    
    Args:
        text (str): 包含价格的文本
        
    Returns:
        tuple or None: (货币符号, 价格) 或 None
    """
    # 类型检查

        
    if not text:
        return '¥', 0.
    
    if not isinstance(text, str):
        return '¥', 0.
    # 支持多种货币符号
    pattern = r'([¥$€£])([0-9,]+\.?[0-9]*)'
    
    try:
        match = re.search(pattern, text)
        if match:
            currency = match.group(1)
            price_str = match.group(2).replace(',', '')
            return currency, float(price_str)
    except (ValueError, AttributeError) as e:
        print(f"提取价格时出错: {e}")
    
    return '¥', 0.

def parse_order_time(time_str: str) -> Optional[datetime]:
    """解析订单时间字符串
    
    Args:
        time_str (str): 时间字符串
        
    Returns:
        datetime or None: 解析后的时间对象
    """
    try:
        return datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        try:
            return datetime.strptime(time_str, "%Y-%m-%d %H:%M")
        except ValueError:
            return None

# 1.14新逻辑中用到，用于提取最近的订单，用于做推荐
def get_recent_orders(orders: dict, days:int=3) -> list:
    """获取最近3天的订单列表
    
    Args:
        orders (dict): 所有订单数据
        
    Returns:
        list: 最近的订单列表
    """
    recent_orders = []
    three_days_ago = datetime.now() - timedelta(days=days)
    
    try:
        for poi_orders in orders.values():
            for order in poi_orders['details']:
                try:
                    # 处理可能的时间格式
                    order_time = parse_order_time(order['o_time'])
                    if order_time and order_time > three_days_ago:
                        # 添加商家信息到订单中
                        order['poi_name'] = poi_orders['poi_name']
                        recent_orders.append(order)
                except ValueError as e:
                    print(f"跳过时间格式错误的订单: {order['o_time']} - {str(e)}")
                    continue
        
        # 按时间排序并限制数量
        recent_orders.sort(
            key=lambda x: parse_order_time(x['o_time']) or datetime.min, 
            reverse=True
        )
        return recent_orders[:2]  # 最多返回2个订单
        
    except Exception as e:
        print(f"获取最近订单时出错: {str(e)}")
        return []

# 1.14新逻辑中用到，用于解析模型返回的请求
def parse_moredata_request(response: Union[str, dict]) -> Optional[dict]:
    """解析更多数据请求
    
    Args:
        response: AI的响应
        
    Returns:
        dict or None: 解析后的请求数据
    """
    try:
        if isinstance(response, str):
            start = response.find('{')
            end = response.rfind('}') + 1
            if start >= 0 and end > start:
                json_str = response[start:end]
                return json.loads(json_str)
        return response if isinstance(response, dict) else None
    except Exception as e:
        print(f"解析数据请求时出错: {str(e)}")
        return None

# TODO: 现在用户评价功能已经移除，后续可能需要恢复
def extract_recommendations(text):
    """从AI回复中提取推荐的菜品列表"""
    recommendations = []
    try:
        lines = text.split('\n')
        for i, line in enumerate(lines):
            if line.strip().startswith(str(len(recommendations) + 1) + '.') and '**' in line:
                # 提取**之间的内容
                parts = line.split('**')
                for j in range(1, len(parts), 2):  # 每两个**之间的内容
                    dish = parts[j].strip()
                    if dish and not dish.endswith('：'):  # 排除类别标记
                        recommendations.append({
                            'index': len(recommendations) + 1,
                            'name': dish,
                            'full_text': line.strip()
                        })
    except Exception as e:
        print(f"提取推荐菜品时出错: {str(e)}")
    return recommendations

# TODO: 现在用户评价功能已经移除，后续可能需要恢复
def parse_user_choice(user_input, last_response):
    """解析用户的选择"""
    try:
        # 获取上一次AI推荐的菜品列表
        recommendations = extract_recommendations(last_response)
        if not recommendations:
            return None
            
        # 用户输入数字
        if user_input.isdigit():
            index = int(user_input)
            for rec in recommendations:
                if rec['index'] == index:
                    return rec
                    
        # 用户说"第x个"
        if "第" in user_input and "个" in user_input:
            try:
                number_text = user_input[user_input.index("第")+1:user_input.index("个")]
                number = chinese_to_number(number_text)
                if number:
                    for rec in recommendations:
                        if rec['index'] == number:
                            return rec
            except:
                pass
                    
        # 检查用户输入是否包含任何推荐的菜品名称
        for rec in recommendations:
            if rec['name'] in user_input:
                return rec
                
    except Exception as e:
        print(f"解析用户选择时出错: {str(e)}")
    return None


# TODO: 现在用户评价功能已经移除，后续可能需要恢复
def chinese_to_number(chinese):
    """将中文数字转换为阿拉伯数字"""
    chinese_numbers = {
        '一': 1, '二': 2, '两': 2, '三': 3, '四': 4, '五': 5,
        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
        '1': 1, '2': 2, '3': 3, '4': 4, '5': 5,
        '6': 6, '7': 7, '8': 8, '9': 9
    }
    
    if not chinese:
        return None
        
    # 处理特殊情况
    if chinese == '十':
        return 10
        
    # 如果是阿拉伯数字字符串
    if chinese.isdigit():
        return int(chinese)
        
    # 处理中文数字
    if chinese in chinese_numbers:
        return chinese_numbers[chinese]
        
    return None

# TODO: 现在用户评价功能已经移除，后续可能需要恢复
def parse_user_response(input_messages:list[dict['content':str]])->str:
    for i in range(len(input_messages)-1, -1, -1):
        if input_messages[i]['role'] == 'user':
            original_content = input_messages[i]['content']
            return original_content

def extract_json_response(response:str)->dict:
    # 找到第一个 { 和最后一个 } 之间的内容
    start = response.find('{')
    end = response.rfind('}') + 1
    if start >= 0 and end > start:
        json_str = response[start:end]
        # 尝试解析JSON
        response = json.loads(json_str)
        return response
    else:
        logger.error("系统"+ f"提取JSON响应时出错: {response}")
        return response
    

def extract_all_json(text: str) -> list:
    """从字符串中提取所有JSON数据
    
    Args:
        text (str): 包含JSON数据的字符串
        
    Returns:
        list: 包含所有解析出的JSON对象的列表
    """
    results = []
    stack = []
    start = -1
    
    try:
        for i, char in enumerate(text):
            if char == '{':
                if not stack:  # 找到第一个开始的花括号
                    start = i
                stack.append(char)
            elif char == '}':
                if stack:
                    stack.pop()
                    if not stack:  # 找到匹配的结束花括号
                        json_str = text[start:i+1]
                        try:
                            json_obj = json.loads(json_str)
                            results.append(json_obj)
                        except json.JSONDecodeError:
                            logger.error(f"解析JSON数据时出错: {json_str}")
                            continue
        return results
        
    except Exception as e:
        logger.error(f"提取JSON数据时出错: {str(e)}")
        return []

def get_poi_name_from_text(text:str)->str:
    logger.info(f'从 {text} 中提取店名')
    pattern = r"\*\*(.*?)\*\*"
    match = re.search(pattern, text)
    if match:
        result = match.group(1)  # 结果: "春莱·老挝咖啡·泰式奶茶（望京店）"
        result = result.replace("*", "").strip()
        return result
    return None

def get_merchant_name_from_text(text):
    try:
        # 更新正则表达式模式以匹配店名
        shop_pattern = r"\d{8} \d{2}:\d{2}，(.*?）)\s*（"
        # text = text.split("，")[1].split
        # 使用正则表达式提取店名
        shop_match = re.search(shop_pattern, text)
        
        if shop_match:
            shop_name = shop_match.group(1)
            return shop_name
        return None
    except Exception as e:
        logger.error(f"提取店名时出错: {str(e)}")
        return None

# 用于移除句子中的Emoji表情
def remove_emojis(text):
    # 使用正则表达式匹配 emoji
    # 保留字母、数字、标点、空白字符、中文和数学符号，只过滤emoji
    emoji_pattern = regex.compile(r'[^\w\s\p{P}\p{Sm}\u4e00-\u9fff]', flags=regex.UNICODE)

    # 删除匹配到的 emoji
    return emoji_pattern.sub(r'', text) 

def fill_shop_dict(texts: list[dict]|dict|list[str], need_vector:bool=True)->list[dict, list]:
    shop_dict = defaultdict(list)
    ignore_info = []
    if isinstance(texts, dict):
        texts = list(texts.keys())
        for text in texts:
            shop_name = get_merchant_name_from_text(text)
            if shop_name:
                shop_dict[shop_name].append(text)
            else:
                ignore_info.append(text)
    elif isinstance(texts, list): # 通常情况
        for merchant_info_dict in texts:
            if isinstance(merchant_info_dict, str): # es召回
                text = merchant_info_dict # 获取其文本信息
                shop_name = get_merchant_name_from_text(text)
                if shop_name:
                    shop_dict[shop_name].append({
                        "content": text,
                    })
                else:
                    ignore_info.append(text)
            elif isinstance(merchant_info_dict, dict): # vex召回
                text = merchant_info_dict['value'] # 获取其文本信息
                shop_name = get_merchant_name_from_text(text)
                if shop_name:
                    if need_vector:
                        shop_dict[shop_name].append({
                            "content": text,
                            "vector": merchant_info_dict["vector"]
                        })
                    else:
                        shop_dict[shop_name].append({
                            "content": text,
                        })
                else:
                    ignore_info.append(text)
    else:
        pass
    return shop_dict, ignore_info

if __name__ == "__main__":
    # text = "这是一些文本 {'name': 'test1'} 还有一些文本 {'name': 'test2', 'data': {'key': 'value'}}"
    # json_list = extract_all_json(text)
    # print(json_list)
    text = """
你好！现在是午餐时间，我来帮你推荐一些适合的外卖吧！根据你所在的时间段和附近的热门订单数据，我为你挑选了几家评价高、配送快的餐厅和菜品："""
    shop_name = get_poi_name_from_text(text)
    print(shop_name)