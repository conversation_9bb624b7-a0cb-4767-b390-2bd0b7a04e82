import os
import json
from typing import List, Dict, Any
from datetime import datetime
from utils.logger import logger
from configs.config import MERCHANT_DATA_LIMIT
from utils.extract import process_ingredient_data
from joblib import dump, load


# 使用joblib保存字典
def save_dict_joblib(dictionary, filepath):
    dump(dictionary, filepath, compress=3)  # compress=3 表示压缩级别

# 使用joblib加载字典
def load_dict_joblib(filepath):
    return load(filepath)

# 使用json保存字典
def save_dict_json(dictionary, filepath):
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(dictionary, f, ensure_ascii=False, indent=2)

# 使用json加载字典
def load_dict_json(filepath):
    with open(filepath, 'r', encoding='utf-8') as f:
        return json.load(f)

# 功能：用户输入中提到的食材，从文件中读取数据，返回食材信息
# 输入：用户输入，食材目录
def get_ingredient_data(user_input, directory_path):
    
    found_ingredients = []
    ingredient_data_list = []

    # 获取所有可用的食材列表
    available_ingredients = [
        f[:-5] for f in os.listdir(directory_path) if f.endswith(".json")
    ]
    available_ingredients.sort(key=len, reverse=True)

    # 在用户输入中查找食材名
    remaining_input = user_input
    for ingredient in available_ingredients:
        if ingredient in remaining_input:
            found_ingredients.append(ingredient)
            remaining_input = remaining_input.replace(ingredient, "")

    if not found_ingredients:
        return ""
        
    for ingredient_name in found_ingredients:
        # 如果没有缓存，从文件读取
        json_file_path = os.path.join(directory_path, f"{ingredient_name}.json")
        
        if os.path.exists(json_file_path):
            ingredient_data = read_file(json_file_path)
            
            # 处理并精简数据
            processed_data = process_ingredient_data(ingredient_data)

            ingredient_data_list.append(
                {"name": ingredient_name, "data": processed_data}
            )
        else:
            logger.warning(f"未找到食材 {ingredient_name} 的数据文件。")
    
    if not ingredient_data_list:
        return ""

    ingredients_info = []
    for item in ingredient_data_list:
        ingredients_info.append(f"{item['name']}：{item['data']}")
    
    return f"\n\n以下是参考资料，本次沟通涉及到的几种食材在美团外卖的热门菜品和流行口味：\n{' '.join(ingredients_info)}"


# TODO: 现在商家数据只能写死，后续肯定要动态读取当天的
def get_merchant_data():
    """获取商家数据"""
    try:
        merchant_file = 'weiwei/data/merchant_stable_20250106.json'
        if os.path.exists(merchant_file):
            with open(merchant_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if 'merchants' in data:
                    return data['merchants'][:MERCHANT_DATA_LIMIT]  # 只返回前200家
        return []
    except Exception as e:
        logger.error(f"读取商家数据时出错: {str(e)}")
        return []


# TODO: 现在商家数据只能写死，后续肯定要动态读取当天的
def get_rising_merchant_data():
    """获取表现突出的商家数据"""
    try:
        merchant_file = 'weiwei/data/merchant_rising_20250106.json'
        if os.path.exists(merchant_file):
            with open(merchant_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if 'merchants' in data:
                    return data['merchants'][:MERCHANT_DATA_LIMIT]  # 只返回前200家
        return []
    except Exception as e:
        logger.error(f"读取表现突出商家数据时出错: {str(e)}")
        return []

def load_orders_data(file_path: str) -> List[Dict[str, Any]]:
    """加载订单数据并转换格式
    
    Args:
        file_path (str): JSON文件路径
        
    Returns:
        list: 订单列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 转换数据格式
        orders = []
        for poi_id, poi_info in data.items():
            if not isinstance(poi_info, dict):
                continue
                
            for order in poi_info.get('details', []):
                try:
                    # 安全地获取和转换价格
                    total_amount = sum(
                        float(item.get('f_price', 0)) 
                        for item in order.get('items', [])
                        if isinstance(item, dict)
                    )
                    
                    order_info = {
                        'poi_id': poi_id,
                        'merchant_name': poi_info.get('poi_name', '未知商家'),
                        'city_id': poi_info.get('cityId', ''),
                        'order_time': order.get('o_time', ''),
                        'items': order.get('items', []),
                        'total_amount': total_amount,
                        'rating': float(order.get('rating', 0))
                    }
                    orders.append(order_info)
                except (ValueError, TypeError) as e:
                    print(f"处理订单数据时出错: {str(e)}")
                    continue
                    
        return orders
    except FileNotFoundError:
        print(f"文件不存在: {file_path}")
        return []
    except json.JSONDecodeError:
        print(f"JSON格式错误: {file_path}")
        return []
    except Exception as e:
        print(f"加载数据文件 {file_path} 时出错: {str(e)}")
        return []

def read_file(file_path: str) -> str:
    """读取指定文件的内容
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        str: 文件内容
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except FileNotFoundError:
        print(f"文件不存在: {file_path}")
        return ""
    except Exception as e:
        print(f"读取文件时出错: {str(e)}")
        return ""

# TODO: 现在用户评价功能已经移除，后续可能需要恢复
def save_user_review(user_id: int, review: str, order_info: Dict[str, Any]) -> None:

    """保存用户评价记录
    
    Args:
        user_id (int): 用户ID
        review (str): 评价内容
        order_info (dict): 订单信息，包含商家和商品信息
    """
    try:
        # 创建评价记录目录
        review_dir = 'user_reviews'
        os.makedirs(review_dir, exist_ok=True)
        
        # 构建评价文件路径
        review_file = os.path.join(review_dir, f"user_reviews_{user_id}.json")
        
        # 获取当前时间作为评价发布时间
        review_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 准备新的评价记录
        new_review = {
            'review_time': review_time,  # 评价发布时间
            'review_content': review,
            'merchant_name': order_info.get('poi_name', '未知商家'),
            'items': order_info.get('items', []),
            'order_id': order_info.get('order_id', ''),
        }
        
        # 读取现有评价记录
        reviews = []
        if os.path.exists(review_file):
            try:
                with open(review_file, 'r', encoding='utf-8') as f:
                    reviews = json.load(f)
                    if not isinstance(reviews, list):
                        reviews = []
            except json.JSONDecodeError:
                print(f"读取评价文件时出错，将创建新文件")
                reviews = []
        
        # 添加新评价
        reviews.append(new_review)
        
        # 保存评价记录
        with open(review_file, 'w', encoding='utf-8') as f:
            json.dump(reviews, f, ensure_ascii=False, indent=2)
            
        print(f"评价已保存到: {review_file}")
        
    except Exception as e:
        print(f"保存评价记录时出错: {str(e)}")

# TODO: 现在用户评价功能已经移除，后续可能需要恢复
def load_user_reviews(user_id: int) -> List[Dict[str, Any]]:
    """读取用户的评价记录
    
    Args:
        user_id (int): 用户ID
        
    Returns:
        List[Dict[str, Any]]: 评价记录列表，每个评价是一个字典，包含以下字段：
            - review_time: 评价时间
            - review_content: 评价内容
            - merchant_name: 商家名称
            - items: 商品列表
            - order_id: 订单ID
    """
    try:
        # 构建评价文件路径
        review_dir = 'user_reviews'
        review_file = os.path.join(review_dir, f"user_reviews_{user_id}.json")
        
        # 检查文件是否存在
        if not os.path.exists(review_file):
            print(f"未找到用户 {user_id} 的评价记录")
            return []
            
        # 读取评价记录
        try:
            with open(review_file, 'r', encoding='utf-8') as f:
                reviews = json.load(f)
                
            # 验证数据格式
            if not isinstance(reviews, list):
                print(f"评价记录格式错误: {review_file}")
                return []
                
            # 验证每条评价的数据完整性
            valid_reviews = []
            required_fields = {'review_time', 'review_content', 'merchant_name', 'items', 'order_id'}
            
            for review in reviews:
                if not isinstance(review, dict):
                    continue
                    
                # 检查必要字段是否存在
                if not all(field in review for field in required_fields):
                    continue
                    
                valid_reviews.append(review)
                
            if len(valid_reviews) < len(reviews):
                print(f"发现 {len(reviews) - len(valid_reviews)} 条无效评价记录")
                
            return valid_reviews
            
        except json.JSONDecodeError:
            print(f"评价文件格式错误: {review_file}")
            return []
            
    except Exception as e:
        print(f"读取评价记录时出错: {str(e)}")
        return []
