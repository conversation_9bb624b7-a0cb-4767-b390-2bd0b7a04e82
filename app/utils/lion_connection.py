# -*- coding: utf-8 -*-
import hashlib
import hmac
from base64 import encodebytes
from datetime import datetime
from typing import Any
from urllib import parse

import requests

from utils.logger import logger

class LionClient(object):
    def __init__(self, client_id: str,
                 client_secret: str,
                 env: str,
                 app_name: str):
        '''
        Args:
            client_id:      从lion那边申请到的 ba id （虚拟账号）
            client_secret:  从lion那边申请到的 ba token （lion为虚拟账号生成的密码 ）
            app_name:       项目名，e.g. com.sankuai.dialogstudio.xiaomei.toolexecute
            env:            环境 [dev,test,ppe,staging,prod]

        '''
        self.client_id = client_id
        self.client_secret = client_secret
        self.app_name = app_name
        self.env = env
        self.domain = self.get_domain()  # 根据指定环境获取 url

    def post_auth(self, client_id: str, client_secret: str, url: str):
        '''
        鉴权
        :return:
        '''
        gmt_time = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
        string2sign = "GET %s\n%s" % (parse.urlparse(url).path, gmt_time)
        signature = encodebytes(hmac.new(str.encode(client_secret), str.encode(string2sign), hashlib.sha1).digest())
        return dict(Date=gmt_time, Authorization="MWS " + client_id + ":" + bytes.decode(signature).replace("\n", ""))

    def get_domain(self):
        '''
        根据不同的环境获取 url
            线上：http://lion.vip.sankuai.com，包含环境 env = prod、staging；
            线下：http://lion-api.inf.test.sankuai.com，包含环境 env = dev、test；
        '''
        if self.env in ['prod', 'staging']:
            return 'http://lion.vip.sankuai.com'
        else:
            return 'http://lion-api.inf.test.sankuai.com'

    def get_value_from_lion(self, lion_key: str) -> str:
        '''
        根据 key 从 lion 中获取配置的 value
        Args:
            lion_key: 创建时的 key，e.g.galileo_prompt.summary"
        '''

        url = self.domain + '/v3/configs/envs/{}/appkeys/{}/keys/{}'.format(self.env, self.app_name, lion_key)
        try:
            auth = self.post_auth(self.client_id, self.client_secret, url)
            response = requests.get(url, headers=auth, timeout=5)
            result = response.json().get('result')
            if result is None:
                logger.error(f"Lion API result is None for key: {lion_key}")
                return ""
            return result.get('value', "")
        except Exception as e:
            logger.error(f"get_value_from_lion failed for key: {lion_key}")
            logger.exception(e)
            return ""

    def get_value(self, lion_key: str) -> str:
        '''
        获取单个lion配置value
        Args:
            lion_key: 创建时的 key，e.g.galileo_prompt.summary"
        :return:
        '''
        return self.get_value_from_lion(lion_key)

    def get_value_with_default(self, lion_key: str, default_value: Any) -> str:
        value = self.get_value_from_lion(lion_key)
        if value is None or value == "":
            return default_value
        return value

    def get_value_with_cell(self, lion_key: str, cell: str) -> str:
        '''
        根据 key 从 lion 中获取配置的 value
        Args:
            lion_key: 创建时的 key，e.g.galileo_prompt.summary"
            cell: set名
        '''

        url = self.domain + '/v3/configs/envs/{}/appkeys/{}/keys/{}'.format(self.env, self.app_name, lion_key)
        if cell is not "":
            url += "?set=" + cell
        try:
            auth = self.post_auth(self.client_id, self.client_secret, url)
            response = requests.get(url, headers=auth, timeout=5)
            result = response.json().get('result')
            return result['value']
        except Exception as e:
            logger.error(f"get_value_from_lion failed.")
            logger.exception(e)
            return ""
        

if __name__ == "__main__":
    lion_client = LionClient(client_id="xiaomei_toolexecute_admin",
                             client_secret="XFDCTVU8SC",
                             env="dev",
                             app_name="xiaomei_toolexecute")
    lion_client.get_value("hahahacanugetme")
