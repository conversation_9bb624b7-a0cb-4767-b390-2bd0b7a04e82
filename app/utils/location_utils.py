import math
from typing import Dict, List, Tuple, Optional
from utils.logger import logger



# 商家位置索引名称
MERCHANT_LOCATION_INDEX = "maindb_v1"  # 修改为正确的索引名称

def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """计算两点之间的距离（单位：米）# 使用Haversine公式计算球面距离
    
    Args:
        lat1: 第一个点的纬度
        lon1: 第一个点的经度
        lat2: 第二个点的纬度
        lon2: 第二个点的经度
        
    Returns:
        两点之间的距离，单位为米
    """
    R = 6371000  # 地球半径（米）
    
    # 将经纬度转换为弧度
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)
    
    # 差值
    dlat = lat2_rad - lat1_rad
    dlon = lon2_rad - lon1_rad
    
    # Haversine公式
    a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
    distance = R * c
    
    return round(distance, 2)  # 保留2位小数

def get_merchant_location(poi_id: str) -> Optional[Dict[str, float]]:
    """从ES获取商家位置信息
    
    Args:
        poi_id: 商家ID
        
    Returns:
        包含经纬度的字典，格式为 {'latitude': float, 'longitude': float}
        查询失败或没有位置信息时返回None
    """
    from app.es.es_client import search
    try:
        query = {
            "query": {
                "term": {
                    "poi_id": poi_id
                }
            }
        }
        
        result = search(MERCHANT_LOCATION_INDEX, query)
        
        if not result or not result.get('hits', {}).get('hits', []):
            logger.warning(f"商家{poi_id}未找到位置信息")
            return None
            
        merchant_info = result['hits']['hits'][0]['_source']
        
        # 根据提供的索引结构获取经纬度
        if 'position' in merchant_info:
            position = merchant_info['position']
            return {
                'latitude': position.get('latitude'),
                'longitude': position.get('longitude')  # 注意这里是'longitude'而不是'longitude'
            }
        else:
            logger.warning(f"商家{poi_id}位置信息不完整")
            return None
            
    except Exception as e:
        logger.error(f"获取商家{poi_id}位置信息失败: {str(e)}")
        return None

def batch_get_merchant_locations(poi_ids: List[str]) -> Dict[str, Dict[str, float]]:
    """批量获取商家位置信息
    
    Args:
        poi_ids: 商家ID列表
        
    Returns:
        字典，格式为 {poi_id: {'latitude': float, 'longitude': float}}
    """
    from app.es.es_client import search
    try:
        if not poi_ids:
            return {}
            
        query = {
            "query": {
                "terms": {
                    "poi_id": poi_ids
                }
            },
            "size": len(poi_ids)  # 确保返回所有结果
        }
        
        result = search(MERCHANT_LOCATION_INDEX, query)
        
        if not result or not result.get('hits', {}).get('hits', []):
            return {}
            
        locations = {}
        for hit in result['hits']['hits']:
            source = hit['_source']
            poi_id = source.get('poi_id')
            
            if 'position' in source and poi_id:
                position = source['position']
                locations[poi_id] = {
                    'latitude': position.get('latitude'),
                    'longitude': position.get('longitude')  # 注意这里是'longitude'而不是'longitude'
                }
                
        return locations
        
    except Exception as e:
        logger.error(f"批量获取商家位置信息失败: {str(e)}")
        return {}

def calculate_user_merchant_distance(user_lat: float, user_lon: float, poi_id: str) -> Optional[float]:
    """计算用户到商家的距离
    
    Args:
        user_lat: 用户纬度
        user_lon: 用户经度
        poi_id: 商家ID
        
    Returns:
        距离，单位为米；如果无法获取商家位置则返回None
    """
    merchant_location = get_merchant_location(poi_id)
    
    if not merchant_location:
        return None
        
    return calculate_distance(
        user_lat, 
        user_lon, 
        merchant_location['latitude'], 
        merchant_location['longitude']
    )

def batch_calculate_distances(user_lat: float, user_lon: float, poi_ids: List[str]) -> Dict[str, float]:
    """批量计算用户到多个商家的距离
    
    Args:
        user_lat: 用户纬度
        user_lon: 用户经度
        poi_ids: 商家ID列表
        
    Returns:
        字典，格式为 {poi_id: distance}，距离单位为米
    """
    merchant_locations = batch_get_merchant_locations(poi_ids)
    
    distances = {}
    for poi_id, location in merchant_locations.items():
        try:
            distance = calculate_distance(
                user_lat,
                user_lon,
                location['latitude'],
                location['longitude']
            )
            distances[poi_id] = distance
        except (KeyError, TypeError) as e:
            logger.warning(f"计算商家{poi_id}距离失败: {str(e)}")
            
    return distances

def get_merchants_by_distance(user_lat: float, user_lon: float, poi_ids: List[str], max_distance: float = None) -> List[Dict]:
    """获取商家距离信息并按距离排序
    
    Args:
        user_lat: 用户纬度
        user_lon: 用户经度
        poi_ids: 商家ID列表
        max_distance: 最大距离限制（米），None表示不限制
        
    Returns:
        按距离排序的商家列表，包含poi_id和distance
    """
    distances = batch_calculate_distances(user_lat, user_lon, poi_ids)
    
    result = [
        {'poi_id': poi_id, 'distance': distance}
        for poi_id, distance in distances.items()
        if max_distance is None or distance <= max_distance
    ]
    
    # 按距离升序排序
    result.sort(key=lambda x: x['distance'])
    
    return result

# 测试函数
def test_distance_calculation(user_lat: float, user_lon: float, poi_id: str) -> Dict:
    """测试距离计算功能
    
    Args:
        user_lat: 用户纬度
        user_lon: 用户经度
        poi_id: 商家ID
        
    Returns:
        测试结果
    """
    try:
        merchant_location = get_merchant_location(poi_id)
        
        if not merchant_location:
            return {
                'success': False,
                'message': f"无法获取商家{poi_id}的位置信息"
            }
            
        distance = calculate_distance(
            user_lat,
            user_lon,
            merchant_location['latitude'],
            merchant_location['longitude']
        )
        
        return {
            'success': True,
            'user_location': {
                'latitude': user_lat,
                'longitude': user_lon
            },
            'merchant_location': merchant_location,
            'distance': distance,
            'unit': '米'
        }
        
    except Exception as e:
        logger.error(f"测试距离计算失败: {str(e)}")
        return {
            'success': False,
            'message': str(e)
        } 