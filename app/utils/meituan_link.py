import logging
from typing import Dict, List, Any, Optional

from utils.logger import logger
from service.context import check_food_with_merchant_name, check_food_with_merchant_id, check_merchant, generate_meituan_link, generate_food_img_url
from utils.extract import get_poi_name_from_text, remove_emojis
from configs import lion_config
from service.merchant_service import MerchantService

# 创建商家服务实例
merchant_service = MerchantService()

def add_meituan_link(
    content: str,
    temp_line: str,
    content_lines: List[str],
    accumulated_content: str,
    temporary_merchant_info: Dict[str, Any],
    meituan_link: List[Dict[str, Any]] = None,
    token:str|None = None
) -> tuple[str, str, List[Dict[str, Any]]]:
    """
    为回复添加美团购物链接
    
    Args:
        content: 当前内容
        temp_line: 临时行
        content_lines: 内容行列表
        accumulated_content: 累积内容
        temporary_merchant_info: 临时商家信息
        meituan_link: 美团链接列表
        
    Returns:
        tuple: (temp_line, accumulated_content, meituan_link)
    """
    if meituan_link is None:
        meituan_link = []
        
    try:
        if '\n' in content:
            enter_pos = temp_line.find('\n')
            last_line = temp_line[:enter_pos+1].strip()
            next_line = temp_line[enter_pos+1:]
            # logger.info(f"开始搜查内容: {last_line}, 多余的内容为: [{next_line}], enter_pos = {enter_pos}")
            
            if "推荐商品" in last_line or "推荐菜品" in last_line or \
                "推荐饮料" in last_line or "推荐菜品" in last_line or "推荐套餐" in last_line: # 如果最后一句是推荐食物
                temporary_merchant_info = {"id":None, "name":None}
                # logger.info(f"开始构造购物链接：   引发构造过程的内容为{last_line}")
                try:
                    try:
                        wait_shop_name = content_lines[-1].strip()
                    except IndexError:
                        wait_shop_name = content_lines[-1].strip()
                    wait_shop_name = get_poi_name_from_text(wait_shop_name)
                except IndexError:
                    wait_shop_name = None
                    
                if wait_shop_name == None:
                    content_lines.append(temp_line[:enter_pos+1])
                    temp_line = next_line
                    return temp_line, accumulated_content, meituan_link
                    
                # logger.info(f"提取得到需要查询的店铺名称为:{wait_shop_name}")
                shop_info = check_merchant(wait_shop_name)
                if shop_info != None:
                    # 检查商家状态，过滤掉非营业商家
                    merchant_id = shop_info["poi_id"]
                    merchant_status = merchant_service.check_merchant_status(merchant_id)
                    
                    # 如果商家不在营业或者未上线，跳过该商家
                    if not merchant_status.get("is_open", False) or not merchant_status.get("is_online", False):
                        logger.info(f"商家[{shop_info['poi_name_text']}]({merchant_id})不在营业状态，跳过。状态: {merchant_status}")
                        # 不添加任何提示，直接跳过此商家
                        content_lines.append(temp_line[:enter_pos+1])
                        temp_line = next_line
                        return temp_line, accumulated_content, meituan_link
                    
                    # 商家可用，继续正常流程
                    logger.info(f"商家[{shop_info['poi_name_text']}]({merchant_id})状态正常，可以推荐。状态: {merchant_status}")
                    temporary_merchant_info["id"] = shop_info["poi_id"]
                    temporary_merchant_info["name"] = shop_info["poi_name_text"]
                    try:
                        wait_food_name = last_line.split("：")[1]
                    except Exception as e:
                        # logger.info(f"替换失败：   内容为{last_line}")
                        wait_food_name = last_line
                        
                    # logger.info(f"提取得到的食物为: {wait_food_name}")
                    food_result = check_food_with_merchant_name(wait_food_name, shop_info["poi_name_keyword"])
                    if food_result != None:
                        meituan_link.append(food_result)
                        # logger.info(f"提取菜品成功： 菜品信息为: {food_result}")
                        merchant_name = food_result["poi_name_keyword"]
                        food_name = food_result["food_name_text"]
                        spu_id = food_result["spu_id"]
                        poi_id = food_result["poi_id"]
                        food_id = food_result["food_id"] # 也是skuid
                        food_url = generate_meituan_link("meituan_app_add_order_link", poi_id=poi_id, spu_id=spu_id)
                        
                        if token != None:
                            img_url = generate_food_img_url(token, poi_id, food_id)
                        else:
                            logger.info(f"生成图片链接失败： 未获取到token")
                            img_url = None
                        logger.info(f"生成的图片链接为: {img_url}")

                        food_pos = temp_line.find("：")
                        if food_pos != -1:
                            last_line = temp_line[:food_pos+1] + " " + food_name + f" [来一单]({food_url})"
                        else:
                            last_line = temp_line.rstrip() + f" [来一单]({food_url})"
                        
                        content_lines.append(last_line)
                        wait_food_name = remove_emojis(wait_food_name).strip()
                        logger.info(f"替换的食物信息为: [ {wait_food_name} ], 替换为: [ {food_name} ], 此时的信息为: [ {accumulated_content} ]")
                        parts = accumulated_content.rsplit(wait_food_name, 1)
                        if len(parts) > 1:
                            accumulated_content = parts[0] + f"[{food_name}]({food_url})![商品图片]({img_url})" + parts[1] if img_url != None else parts[0] + f"[{food_name}]({food_url})" + parts[1]
                        temp_line = next_line
                    else:
                        content_lines.append(temp_line[:enter_pos+1])
                        temp_line = next_line
                else:
                    content_lines.append(temp_line[:enter_pos+1])
                    temp_line = next_line
            else:
                content_lines.append(temp_line[:enter_pos+1])
                temp_line = next_line
                
            # 递归处理剩余内容
            if "\n" in temp_line:
                temp_line, accumulated_content, meituan_link = add_meituan_link(
                    content, 
                    temp_line, 
                    content_lines, 
                    accumulated_content, 
                    temporary_merchant_info,
                    meituan_link,
                    token
                )
    except Exception as e:
        logger.error(f"处理内容时出错：错误信息为: {str(e)}")
        
    return temp_line, accumulated_content, meituan_link


