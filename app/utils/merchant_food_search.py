from es import es_client
from utils.logger import logger

def search_merchant_food_single(shop_name: str, food_name: str) -> tuple[str, str]:
    """
    精确ES搜索商品信息
    - shop_name包含于poi_name_text中 
    - food_name包含于food_name_text中
    
    Args:
        shop_name (str): 店铺名称
        food_name (str): 食物名称
        
    Returns:
        tuple[str, str]: 一个包含 (poi_id, food_id) 的元组。如果未找到匹配项，则两个值都将为空字符串。
    """
    try:
        # 构造精确匹配的查询
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "poi_name_keyword": shop_name
                            }
                        },
                        {
                            "term": {
                                "food_name_keyword": food_name
                            }
                        }
                    ]
                }
            },
            "size": 1  # 我们只需要一个匹配
        }
        
        # 执行搜索
        logger.info(f"Searching merchant_food with shop_name: {shop_name}, food_name: {food_name}")
        result = es_client.search("merchant_food", query)
        
        # 检查是否获取到命中
        if not result or 'hits' not in result or not result['hits']['hits']:
            logger.info(f"No matches found for shop: {shop_name}, food: {food_name}")
            return "", ""
            
        # 获取第一个命中
        hit = result['hits']['hits'][0]
        source = hit.get('_source', {})
        
        # 提取 poi_id 与 food_id
        poi_id = source.get('poi_id', '')
        food_id = source.get('food_id', '')
        
        logger.info(f"Found match - poi_id: {poi_id}, food_id: {food_id}")
        return poi_id, food_id
        
    except Exception as e:
        logger.error(f"Error searching merchant food: {str(e)}")
        return "", ""

def search_merchant_food(shop_name: str, food_names: list[str]) -> tuple[list[str], list[str]]:
    """
    精确ES搜索商品信息
    - shop_name包含于poi_name_text中 
    - food_name包含于food_name_text中
    - 一次操作多个食品名
    """
    try:
        poi_ids = []
        food_ids = []
        for food_name in food_names:
            poi_id, food_id = search_merchant_food_single(shop_name, food_name)
            poi_ids.append(poi_id)
            food_ids.append(food_id)
        return poi_ids, food_ids

    except Exception as e:
        logger.error(f"Error searching merchant food: {str(e)}")
        return [], []

def search_merchant_food_fuzzy(shop_name: str, food_names: list[str]) -> tuple[str, str]:
    """
    模糊ES搜索商品信息
    - shop_name包含于poi_name_text中 或
    - food_name包含于food_name_text中

    Args:
        shop_name (str): 店铺名称
        food_names (list[str]): 食物名称列表
        
    Returns:
        tuple[str, str]: 一个包含 (poi_id, food_id) 的元组。如果未找到匹配项，则两个值都将为空字符串。
    """
    try:
        # 创建一个列表来保存所有 'should' 子句
        should_clauses = [
            # 检查 shop_name 是否在 poi_name_text 中
            {
                "match": {
                    "poi_name_text": shop_name
                }
            }
        ]
        
        # 为每个食物名称添加一个条件，检查它是否在 food_name_text 中
        for food_name in food_names:
            should_clauses.append({
                "match": {
                    "food_name_text": food_name
                }
            })
        
        # 构造查询，使用 'should' 逻辑（或）
        query = {
            "query": {
                "bool": {
                    "should": should_clauses,
                    "minimum_should_match": 1  # 至少一个条件必须匹配
                }
            },
            "size": 10  # 获取多个结果，以防不同食物匹配
        }
        
        # 执行搜索
        logger.info(f"Fuzzy searching merchant_food with shop_name: {shop_name}, food_names: {food_names}")
        result = es_client.search("merchant_food", query)
        
        # 检查是否获取到命中
        if not result or 'hits' not in result or not result['hits']['hits']:
            logger.info(f"No fuzzy matches found for shop: {shop_name}, foods: {food_names}")
            return "", ""
            
        # 获取第一个命中
        hit = result['hits']['hits'][0]
        source = hit.get('_source', {})
        
        # 提取 poi_id 与 food_id
        poi_id = source.get('poi_id', '')
        food_id = source.get('food_id', '')
        
        logger.info(f"Found fuzzy match - poi_id: {poi_id}, food_id: {food_id}")
        return (poi_id, food_id)
        
    except Exception as e:
        logger.error(f"Error fuzzy searching merchant food: {str(e)}")
        return "", ""
    
if __name__ == "__main__":
    # 测试精确匹配
    # print("Exact match result:", search_merchant_food_single("安妮意大利餐厅（望京店）", "P9安妮比萨"))
    # 检查多项食物搜索匹配
    print("Exact match result:", search_merchant_food("安妮意大利餐厅（望京店）", ["P9安妮比萨", "A2什锦蔬菜扒"]))
    # 测试模糊匹配
    print("Fuzzy match result:", search_merchant_food_fuzzy("安妮意大利餐厅（望京店）", ["P9安妮比萨", "A2什锦蔬菜扒"]))
