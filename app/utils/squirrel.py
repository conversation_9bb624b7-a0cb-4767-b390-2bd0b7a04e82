import os, sys
from threading import Lock
from squirrel.squirrelClient import SquirrelClient
from utils.logger import logger

from configs.config import CURRENT_ENV, APP_KEY


def build_category_key(category, template, *params):
    try:
        key = "{category}.{template}_0".format(
            category=category, template=template.format(*params)
        )
        return key
    except IndexError as e:
        logger.error("failed to build category key, the category template is: {0}".format(template))
        raise RuntimeError("failed to build category key, reason is: {0}".format(e))


class RedisClient:
    _instance = None
    _lock = Lock()  # 保证线程安全
    sq_client = None

    def __init__(self):
        # 只在第一次初始化时调用 setup
        if self.sq_client is None:
            self.setup()

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            with cls._lock:  # 确保线程安全
                if cls._instance is None:  # 双重检查
                    cls._instance = cls()
        return cls._instance

    def get_client(self):
        """
        获取 Redis 客户端。
        """
        if self.sq_client is None:
            raise Exception("Redis client is not initialized")
        return self.sq_client

    # 客户端初始化配置
    def setup(self):
        cluster_name = "redis-xiaomei-weiwei_product" if CURRENT_ENV == "prod" else "redis-xiaomei-weiwei_qa"
        # 获取启动脚本的路径
        project_root = os.path.dirname(os.path.abspath(__file__))
        proxy_path = project_root + "/squirrel-proxy.conf"
        self.sq_client = SquirrelClient(
            proxy_config_path=proxy_path,
            cluster_name=cluster_name,
            print_debug_log=True,
            restart_t_healthy=True,
            appkey=APP_KEY,
        )

    def zcount(self, key):
        return self.sq_client.zcount(key)

    def zrange(self, key, start, end, withscores=False):
        return self.sq_client.zrange(key, start, end, withscores)

