import base64
import queue


class ThreadedGenerator:
    def __init__(self):
        self.queue = queue.Queue()
        self.res_dict = None

    def __iter__(self):
        return self

    def __next__(self):
        item = self.queue.get()
        if item is StopIteration: raise item
        return item

    def send(self, data):
        if data:
            self.queue.put('data: ' + data.replace("\n", "%0A") + "\n\n")

    def rawsend(self, data):
        if data:
            self.queue.put(data)

    def send_pcm_data(self, pcm_data):
        encoded_data = base64.b64encode(pcm_data).decode('utf-8')
        self.queue.put('data: ' + encoded_data + '\n\n')

    def set_res_dict(self, res_dict):
        self.res_dict = res_dict

    def close(self):
        self.queue.put(StopIteration)
