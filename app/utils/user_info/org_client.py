import json

from octo_rpc import NonMeshClient, load
from threading import Lock
from configs.config import CURRENT_ENV
from configs import lion_config
from utils.logger import logger
import os


# 加载thrift服务定义
current_dir = os.path.dirname(os.path.abspath(__file__))
org_service = load(os.path.join(current_dir, "org_service.thrift"))

class ORGClient:
    """
    员工信息查询客户端
    实现queryByCondition接口调用
    """
    _instance = None
    _lock = Lock()  # 保证线程安全
    client = None

    def __init__(self):
        if self.client is None:
            self.setup()

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            with cls._lock:  # 确保线程安全
                if cls._instance is None:  # 双重检查
                    cls._instance = cls()
        return cls._instance

    def setup(self):
        # 设置环境变量
        os.environ['APP_ENV'] = CURRENT_ENV
        os.environ['OCTO_MNS_ENV'] = CURRENT_ENV
        os.environ['OCTO_APPKEY'] = 'com.sankuai.friday.xiaomei.weiwei'

        try:
            # 初始化NonMeshClient
            self.client = NonMeshClient(
                service=org_service.TOrchInvokeRpcService,  # 服务定义
                service_name="com.sankuai.bas.meta.orch.sdk.service.TOrchInvokeRpcService",  # 服务名称
                server_port=8412,
                appkey="com.sankuai.friday.xiaomei.weiwei",  # 本地appkey
                remote_appkey="com.sankuai.bas.meta.orch",  # 远程服务的appkey
                env=CURRENT_ENV,  # 环境
                filter_by_service_name=True,
                timeout=1000  # 超时时间1000ms
            )
            logger.info("ORGClient初始化成功")
        except Exception as e:
            logger.error(f"ORGClient初始化失败: {str(e)}")
            raise

    def get_emp_by_mis(self, mis_id: str):
        """
        根据条件查询员工信息

        Args:
            mis_id (str): 用户ID，必填

        Returns:
            dict: 查询结果，包含员工信息列表
        """
        try:
            # 构建查询参数
            #{ "clientId":"mtupm", "objectCode":"org_employee", "method":3, "reqJSON":"{\"pageNum\":1,\"pageSize\":20,\"searchCondition\":\"wdy_emp_id = 1000\",\"sortList\":[\"+wdy_emp_id\"]}"}
            query_param = org_service.BasicInvokeReq()
            query_param.clientId = lion_config.ORG_CLIENT_ID
            query_param.objectCode = lion_config.ORG_OBJECT_CODE
            query_param.method = 3
            query_condition_dict = {
                "searchCondition": 'mis=\"' + mis_id + '\"',
                "pageNum": 1,
                "pageSize": 1
            }
            req_json = json.dumps(query_condition_dict)
            logger.info(f"查询参数: {req_json}")
            query_param.reqJSON = req_json

            # 调用接口
            result = self.client.basicInvoke(query_param)
            logger.info(f"查询结果: {result}")

            # 解析结果
            result_json = json.loads(result)
            if len(result_json.get("pageList")) == 0:
                logger.warning("查询结果为空")
                return {
                    'status': '-1',
                    'message': '查询结果为空',
                    'data': []
                }

            result_list = result_json.get("pageList")
            return result_list[0]

        except Exception as e:
            error_msg = f"查询员工信息失败: {str(e)}"
            logger.error(error_msg)
            return {
                'status': '-1',
                'message': error_msg,
                'data': None
            }

if __name__ == "__main__":
    client = ORGClient.get_instance()
    client.get_emp_by_mis("zhouyuwen04")