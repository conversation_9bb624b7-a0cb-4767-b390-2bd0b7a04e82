namespace java com.sankuai.bas.meta.orch.sdk.service

/**
 * @TypeDoc(
 *     description = "调用入参"
 * )
 */
struct BasicInvokeReq {
        /**
          * @FieldDoc(
          *     description = "clientId 分发平台申请后获得",
          *     example = {"xxsdasdas"}
          * )
          */
        1: optional string clientId;
        /**
          * @FieldDoc(
          *     description = "模型的code",
          *     example = {"user"}
          * )
          */
        2: optional string objectCode;
        /**
          * @FieldDoc(
          *     description = "1 主键单查 2 主键批查 3 索引查询 4:数据写入",
          *     example = {1}
          * )
          */
        3: optional i32 method;
        /**
          * @FieldDoc(
          *     description = "报文的内容，详细见wiki",
          *     example = {"json的格式"}
          * )
          */
        4: optional string reqJSON;
}

exception OrchException {
    1:string code;         // 异常码
    2:string message;   // 异常提示
}

/**
 * Created by wangkaiyan on 2021/3/22.
 * 分发平台的网关的数据调用Service
 */
service TOrchInvokeRpcService{
    /**
     * 单模型的增删改查的基本调用
     * @param invokeReq
     * @return
     * @throws OrchException
     */
     string basicInvoke(1: required BasicInvokeReq invokeReq) throws (1: OrchException orchException);
}