#!/usr/bin/env python3
import json
import os
from octo_rpc import NonMeshClient, load
from threading import Lock
from configs.config import CURRENT_ENV
from utils.logger import logger

def setup_log_dirs():
    """设置必要的日志目录"""
    user_home = os.path.expanduser("~")
    cache_dir = os.path.join(user_home, '.cache', 'api_test')
    log_dir = os.path.join(cache_dir, 'logs')
    cat_dir = os.path.join(cache_dir, 'cat')
    sdk_dir = os.path.join(cache_dir, 'sdk-info')

    # 创建目录
    os.makedirs(log_dir, exist_ok=True)
    os.makedirs(cat_dir, exist_ok=True)
    os.makedirs(sdk_dir, exist_ok=True)

    # 设置环境变量
    os.environ['CAT_HOME'] = cat_dir
    os.environ['OCTO_LOG_DIR'] = log_dir
    os.environ['OCTO_LOG_UPLOAD_ENABLED'] = 'false'
    os.environ['OCTO_SDK_LOG_DIR'] = sdk_dir

    logger.info(f"日志目录设置完成: {log_dir}")

# 初始化日志目录
setup_log_dirs()
# 加载thrift服务定义
current_dir = os.path.dirname(os.path.abspath(__file__))
sqt_service = load(os.path.join(current_dir, "sqt_service.thrift"))

class SQTClient:
    """
    员工信息查询客户端
    实现queryByCondition接口调用
    """
    _instance = None
    _lock = Lock()  # 保证线程安全
    client = None

    def __init__(self):
        if self.client is None:
            self.setup()

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            with cls._lock:  # 确保线程安全
                if cls._instance is None:  # 双重检查
                    cls._instance = cls()
        return cls._instance

    def setup(self):
        logger.info(f"初始化SQTClient... env={CURRENT_ENV}")

        # 设置环境变量
        os.environ['APP_ENV'] = CURRENT_ENV
        os.environ['OCTO_MNS_ENV'] = CURRENT_ENV
        os.environ['OCTO_APPKEY'] = 'com.sankuai.friday.xiaomei.weiwei'

        try:
            # 初始化NonMeshClient
            self.client = NonMeshClient(
                service=sqt_service.ISqtStaffInfoThriftService,  # 服务定义
                service_name="com.meituan.bep.sqt.staff.client.thrift.service.ISqtStaffInfoThriftService",  # 服务名称
                appkey="com.sankuai.friday.xiaomei.weiwei",  # 本地appkey
                remote_appkey="com.sankuai.bep.canyin.sqtstaff",  # 远程服务的appkey
                env=CURRENT_ENV,  # 环境
                filter_by_service_name=True,
                timeout=1000  # 超时时间1000ms
            )
            logger.info("SQTClient初始化成功")
        except Exception as e:
            logger.error(f"SQTClient初始化失败: {str(e)}")
            raise

    def query_by_condition(self, ent_id: str, ent_emp_ids: list = None):
        """
        根据条件查询员工信息

        Args:
            ent_id (str): 企业ID，必填
            ent_emp_ids (list, optional): 企业员工ID列表

        Returns:
            dict: 查询结果，包含员工信息列表
        """
        try:
            # 构建查询参数
            query_param = sqt_service.StaffQueryParam()
            query_param.type = None  # 设置查询类型为企业员工ID查询
            query_param.phones = None
            query_param.emails = None
            query_param.serialNums = None
            query_param.staffIds = None
            query_param.entId = int(ent_id)  # 转换为整数
            query_param.entStaffNums = None
            query_param.entEmpIds = [str(emp_id) for emp_id in ent_emp_ids] if ent_emp_ids else None  # 确保是字符串列表
            query_param.isValid = True  # 只返回有效未删除的员工
            query_param.id = None

            logger.info(f"开始查询员工信息:")
            logger.info(f"- 企业ID: {ent_id}")
            logger.info(f"- 企业员工IDs: {ent_emp_ids}")
            # logger.info(f"- 查询类型: StaffQueryTypeEnum.ENT_EMP_ID")

            # 调用接口
            result = self.client.queryByCondition(query_param)

            # 解析结果
            if result:
                # 将返回结果转换为字典
                response = {
                    'status': str(result.status),
                    'message': result.message,
                    'data': []
                }

                # 处理员工信息列表
                if result.data:
                    for staff in result.data:
                        staff_dict = {
                            'id': staff.id,  # 主键ID
                            'entId': staff.entId,  # 企业信息Id
                            'entStaffNum': staff.entStaffNum,  # 员工在企业中工号
                            'email': staff.email,  # 邮箱信息
                            'name': staff.name,  # 员工姓名
                            'phone': staff.phone,  # 手机号
                            'serialNum': staff.serialNum,  # 员工对外序列号
                            'department': staff.department,  # 员工部门
                            'level': staff.level,  # 员工职级信息
                            'division': staff.division,  # 部门拼接信息
                            'city': staff.city,  # 所属城市
                            'gender': staff.gender,  # 性别
                            'genderDesc': staff.genderDesc,  # 性别中文描述
                            'staffStatus': staff.staffStatus,  # 员工是否在职
                            'displayName': staff.displayName,  # 用于展示的姓名
                            'isValid': staff.isValid,  # 员工状态
                            'createdTime': staff.createdTime,  # 创建时间
                            'updateTime': staff.updateTime  # 更新时间
                        }
                        response['data'].append(staff_dict)

                logger.info(f"查询成功，返回 {len(response['data'])} 条记录")
                if response['data']:
                    logger.info("员工信息:")
                    for staff in response['data']:
                        logger.info(f"- 姓名: {staff.get('name')} ({staff.get('displayName')})")
                        logger.info(f"  部门: {staff.get('department')}")
                        logger.info(f"  职级: {staff.get('level')}")
                        logger.info(f"  工号: {staff.get('entStaffNum')}")
                        # logger.info(f"  状态: {'在职' if staff.get('staffStatus') == 2 else '离职'}")
                logger.info(f"查询结果: {response}")
                return response
            else:
                logger.warning("查询结果为空")
                return {
                    'status': '-1',
                    'message': '查询结果为空',
                    'data': []
                }

        except Exception as e:
            error_msg = f"查询员工信息失败: {str(e)}"
            logger.error(error_msg)
            return {
                'status': '-1',
                'message': error_msg,
                'data': None
            }


if __name__ == "__main__":
    try:
        # 创建客户端实例
        client = SQTClient.get_instance()

        # 示例查询
        result = client.query_by_condition(
            ent_id="617",  # 测试环境企业ID
            ent_emp_ids=["21084065"]  # 示例员工ID，需要替换为实际的员工ID
        )

        # 打印结果
        print("\n查询结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))

    except Exception as e:
        print(f"\n发生错误: {str(e)}")