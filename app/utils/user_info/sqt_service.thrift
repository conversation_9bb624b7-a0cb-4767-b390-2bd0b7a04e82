namespace java com.meituan.bep.sqt.staff.client.thrift.service

/**
 * 查询类型枚举
 */
enum StaffQueryTypeEnum {
    ENT_EMP_ID = 1    // 根据企业员工ID查询
}

/**
 * 员工信息查询参数
 */
struct StaffQueryParam {
    1: optional StaffQueryTypeEnum type,
    2: optional list<string> phones,
    3: optional list<string> emails,
    4: optional list<string> serialNums,
    5: optional list<i32> staffIds,
    6: required i32 entId,
    7: optional list<string> entStaffNums,
    8: optional list<string> entEmpIds,
    9: optional bool isValid,
    10: optional i64 id
}

/**
 * 自定义属性基础DTO
 */
struct CustomAttrBaseDTO {
    1: optional i32 id,
    2: optional i64 createdTime,
    3: optional string createdBy,
    4: optional i64 updateTime,
    5: optional string updatedBy
}

/**
 * 员工信息DTO
 */
struct SqtStaffInfoDTO {
    1: optional i32 id,
    2: optional i64 createdTime,
    3: optional string createdBy,
    4: optional i64 updateTime,
    5: optional string updatedBy,
    6: optional i32 entId,              // 企业信息Id
    7: optional string entStaffNum,     // 员工在企业中工号
    8: optional string email,           // 邮箱信息
    9: optional string name,            // 员工姓名
    10: optional string phone,          // 手机号
    11: optional string serialNum,      // 员工对外序列号
    12: optional string department,     // 员工部门
    13: optional i32 authorizerId,      // 授权人
    14: optional i32 isValid,           // 员工状态
    15: optional string level,          // 员工职级信息
    16: optional string division,       // 部门拼接信息
    17: optional i32 isConsent,         // 是否签署知情同意书
    18: optional string city,           // 所属城市
    19: optional i32 invoiceId,         // 员工使用发票id
    29: optional i32 parentId,          // 直接汇报领导id
    31: optional i32 gender,            // 性别
    32: optional i32 staffStatus,       // 员工是否在职
    33: optional i32 staffCityId,       // 员工所在城市id
    34: optional string entEmpId,       // 企业员工id
    36: optional string parentPath,     // 上级汇报链
    37: optional i32 isSettle,          // 是否获取结清证明
    38: optional i64 lastSettleDate,    // 最后一次获取结清证明的时间
    39: optional string cityId,         // 国标城市代码
    40: optional string mtLocationId,   // 美团标准城市代码
    44: optional i64 resignTime,        // 离职时间
    45: optional string genderDesc,     // 性别中文描述
    100: optional string foreignName,   // 外文名
    101: optional string displayName,   // 用于展示的姓名
    102: optional list<CustomAttrBaseDTO> customAttr  // 自定义属性
}

/**
 * 查询结果
 */
struct SqtStaffInfoQueryListResult {
    1: required i32 status,              // 接口返回码
    2: required string message,          // 接口返回信息
    3: optional list<SqtStaffInfoDTO> data  // 接口返回实际数据
}

/**
 * 员工信息查询服务
 */
service ISqtStaffInfoThriftService {
    /**
     * 根据条件查询员工信息
     */
    SqtStaffInfoQueryListResult queryByCondition(1: StaffQueryParam param)
}