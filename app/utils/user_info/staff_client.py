#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import os
from octo_rpc.client import NonMeshClient
from octo_rpc import load
from threading import Lock
from thriftpy2.thrift import TApplicationException
from utils.logger import logger
from configs.config import CURRENT_ENV

class StaffClient:
    _instance = None
    _lock = Lock()  # 保证线程安全
    client = None

    def __init__(self):
        if self.client is None:
            self.setup()

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            with cls._lock:  # 确保线程安全
                if cls._instance is None:  # 双重检查
                    cls._instance = cls()
        return cls._instance

    def setup(self):
        try:
            # 加载thrift服务定义
            current_dir = os.path.dirname(os.path.abspath(__file__))
            staff_service = load(os.path.join(current_dir, "staff_service.thrift"))
            # 初始化NonMeshClient
            self.client = NonMeshClient(
                service=staff_service.StaffUnionBindingQueryThriftService,  # 服务定义
                service_name="com.meituan.bep.sqt.c.login.core.client.service.StaffUnionBindingQueryThriftService",
                # 服务名称
                appkey="com.sankuai.friday.xiaomei.weiwei",  # 本地appkey
                remote_appkey="com.sankuai.sqt.clogin.core",  # 远程服务的appkey
                env=CURRENT_ENV,  # 环境
                filter_by_service_name=True,
                timeout=1000,  # 超时时间1000ms
            )
            logger.info("StaffClient初始化成功")
        except Exception as e:
            logger.error(f"StaffClient初始化失败: {str(e)}")
            raise

    def query_by_staff_id_and_user_type(self, staff_id):
        """
        根据员工ID和用户类型查询绑定关系

        Args:
            staff_id: 企业版员工ID，支持int或str类型

        Returns:
            dict: 包含查询结果的字典，主要字段：
                - status: 接口返回码
                - message: 接口返回信息
                - data: 包含userId的绑定关系信息
        """
        try:
            # 转换staff_id为整数
            staff_id = int(staff_id)
            user_type = 1  # 固定传1

            # 记录请求参数
            logger.info(f"开始查询绑定关系 - staff_id: {staff_id}, user_type: {user_type}")

            # 调用远程服务
            response = self.client.queryByStaffIdAndUserType(staff_id, user_type)

            # 记录返回结果
            logger.info(f"查询结果:  {response}")

            # 检查状态码
            if response.status != 0:
                logger.error(f"查询失败 - status: {response.status}, message: {response.message}")
                return None

            # 提取并返回结果
            result = {
                'status': response.status,
                'message': response.message,
                'data': {
                    'id': response.data.id if response.data else None,
                    'entId': response.data.entId if response.data else None,
                    'staffId': response.data.staffId if response.data else None,
                    'bindUnionId': response.data.bindUnionId if response.data else None,
                    'userId': response.data.userId if response.data else None,
                    'userType': response.data.userType if response.data else None,
                    'mobile': response.data.mobile if response.data else None,
                    'appKey': response.data.appKey if response.data else None,
                    'isValid': response.data.isValid if response.data else None,
                    'createdTime': response.data.createdTime if response.data else None,
                    'createdBy': response.data.createdBy if response.data else None,
                    'updateTime': response.data.updateTime if response.data else None,
                    'updatedBy': response.data.updatedBy if response.data else None
                } if response.data else None
            }

            return result

        except ValueError as e:
            logger.error(f"参数错误: {str(e)}")
            return None
        except TApplicationException as e:
            logger.error(f"调用失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"运行出错: {str(e)}")
            return None

if __name__ == "__main__":
    try:
        # 创建客户端实例
        client = StaffClient.get_instance()

        # 测试查询
        staff_id = 937921  # 示例staffId
        result = client.query_by_staff_id_and_user_type(staff_id)

        # 打印结果
        print(json.dumps(result, indent=2, ensure_ascii=False))

    except Exception as e:
        logger.error(f"运行出错: {str(e)}")