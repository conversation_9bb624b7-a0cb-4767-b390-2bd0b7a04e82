namespace java com.meituan.bep.sqt.c.login.core.client.service

/**
 * 基础响应结构
 */
struct BaseResponse {
    1: required i32 status,              // 接口返回码
    2: required string message,          // 接口返回信息
    3: optional StaffUnionBindingDTO data    // 接口返回实际数据
}

/**
 * 员工绑定关系DTO
 * 对应Java的StaffUnionBindingDTO类
 */
struct StaffUnionBindingDTO {
    1: optional i64 id,                  // 主键ID
    2: optional i64 entId,               // 企业ID
    3: optional i64 staffId,             // 企业版员工ID
    4: optional i64 bindUnionId,         // 绑定关系ID
    5: optional i64 userId,              // 用户ID
    6: optional i32 userType,            // 用户类型 @see com.meituan.bep.sqt.c.login.core.common.enums.UserTypeEnum
    7: optional string mobile,           // 手机号
    8: optional string appKey,           // 应用标识
    9: optional i32 isValid,             // 是否有效
    10: optional i64 createdTime,        // 创建时间
    11: optional string createdBy,       // 创建人
    12: optional i64 updateTime,         // 更新时间
    13: optional string updatedBy        // 更新人
}

/**
 * 员工绑定查询服务
 */
service StaffUnionBindingQueryThriftService {
    /**
     * 根据员工ID和用户类型查询绑定关系
     * @param staffId - 企业版员工ID
     * @param userType - 用户类型，固定传1
     * @return BaseResponse<StaffUnionBindingDTO> - 绑定关系信息
     */
    BaseResponse queryByStaffIdAndUserType(1: i64 staffId, 2: i32 userType)
}