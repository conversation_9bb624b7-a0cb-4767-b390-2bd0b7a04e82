#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import sys
from utils.user_info.staff_client import StaffClient
from utils.user_info.org_client import ORGClient
from utils.user_info.sqt_client import SQTClient
from threading import Lock
from configs.config import CURRENT_ENV
from utils.logger import logger

class UserClient:
    """
    主客户端类，用于串联多个接口调用
    """
    _instance = None
    _lock = Lock()  # 保证线程安全

    def __init__(self):
        self.is_prod = None
        self.env = None
        self.staff_client = None
        self.sqt_client = None
        if self.staff_client is None or self.sqt_client is None:
            self.setup()

    @classmethod
    def get_client(cls):
        if cls._instance is None:
            with cls._lock:  # 确保线程安全
                if cls._instance is None:  # 双重检查
                    cls._instance = cls()
        return cls._instance

    def setup(self):
        self.env = CURRENT_ENV
        self.is_prod = (CURRENT_ENV == 'prod')

        # 初始化各个客户端
        self.sqt_client = SQTClient.get_instance()
        self.staff_client = StaffClient.get_instance()

        logger.info(f"MainClient初始化完成，环境: {self.env}")

    def get_mt_userid_by_mis(self, mis_id):
        """
        通过MIS ID获取美团用户ID的完整流程

        Args:
            mis_id (str): MIS ID

        Returns:
            dict: 包含获取到的所有信息
        """
        try:
            # 1. 通过MIS ID获取emp_id
            logger.info(f"步骤1: 通过MIS ID({mis_id})获取emp_id")
            emp_info = ORGClient.get_instance().get_emp_by_mis(mis_id)
            if not emp_info or 'emp_id' not in emp_info:
                raise ValueError(f"未能获取到员工ID，MIS ID: {mis_id}")
            emp_id = emp_info['emp_id']
            logger.info(f"获取到emp_id: {emp_id}")

            # 2. 通过emp_id获取 staff_id
            logger.info(f"步骤2: 通过emp_id({emp_id})获取 staff_id")
            ent_id = 27840 if self.is_prod else 617

            sqt_result = self.sqt_client.query_by_condition(
                ent_id=str(ent_id),
                ent_emp_ids=[str(emp_id)]
            )

            if not sqt_result or not sqt_result.get('data'):
                raise ValueError(f"未能获取到员工信息，emp_id: {emp_id}")

            staff_info = sqt_result['data'][0]
            staff_id = staff_info.get('id')
            if not staff_id:
                raise ValueError(f"未能获取到企业员工编号，emp_id: {emp_id}")

            logger.info(f"获取到 staff_id: {staff_id}")

            # 3. 通过entStaffNum获取mt_userid
            logger.info(f"步骤3: 通过staff_id({staff_id})获取mt_userid")
            staff_result = self.staff_client.query_by_staff_id_and_user_type(staff_id)

            if not staff_result:
                raise ValueError(f"未能获取到绑定关系，staff_id: {staff_id}")

            mt_userid = staff_result.get("data").get('userId')
            if not mt_userid:
                raise ValueError(f"未能获取到美团用户ID，staff_id: {staff_id}")
            logger.info(f"获取到mt_userid: {mt_userid}")
            return mt_userid, (sqt_result, staff_result)

        except Exception as e:
            logger.error(f"处理失败: {str(e)}")
            raise

if __name__ == "__main__":
    try:
        # 创建客户端实例
        client = UserClient.get_client()  # 使用测试环境

        # 测试MIS ID
        mis_id = "liulingfeng05"  # 示例MIS ID

        # 获取信息
        result = client.get_mt_userid_by_mis(mis_id)

        # 打印结果
        logger.info("\n查询结果:")
        logger.info(json.dumps(result, indent=2, ensure_ascii=False))

    except Exception as e:
        logger.error(f"运行出错: {str(e)}")