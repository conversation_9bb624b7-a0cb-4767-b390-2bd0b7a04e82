#!/bin/bash
# 检查ASR日志的脚本

echo "=== ASR分片功能日志检查 ==="

# 日志文件路径（请根据实际情况修改）
LOG_FILE="/var/log/xiaomei-weiwei/app.log"  # 或者你的实际日志路径

echo "检查日志文件: $LOG_FILE"

if [ ! -f "$LOG_FILE" ]; then
    echo "❌ 日志文件不存在，请检查路径"
    echo "💡 常见日志位置:"
    echo "   - /var/log/xiaomei-weiwei/"
    echo "   - ./logs/"
    echo "   - 容器内: docker logs <container_name>"
    exit 1
fi

echo -e "\n🔍 查找ASR相关日志..."

# 查找分片相关日志
echo -e "\n📊 分片处理日志:"
grep -n "split into.*chunks" "$LOG_FILE" | tail -10

echo -e "\n⚠️ 验证失败日志:"
grep -n "call_asr_api validation failed" "$LOG_FILE" | tail -10

echo -e "\n🎯 分片调用日志:"
grep -n "chunk_[0-9]" "$LOG_FILE" | tail -10

echo -e "\n✅ 成功处理日志:"
grep -n "chunks_count" "$LOG_FILE" | tail -10

echo -e "\n❌ 400005错误日志:"
grep -n "400005" "$LOG_FILE" | tail -10

echo -e "\n📈 音频信息日志:"
grep -n "audio_size=.*duration=" "$LOG_FILE" | tail -10

echo -e "\n💡 使用方法:"
echo "1. 发送测试请求后，运行此脚本查看日志"
echo "2. 关注 'split into X chunks' 表示分片成功"
echo "3. 关注 'chunks_count' 表示合并成功"
echo "4. 如果没有相关日志，检查服务是否重启"

echo -e "\n🔄 实时监控日志:"
echo "tail -f $LOG_FILE | grep -E '(split into|chunk_|chunks_count|validation failed|400005)'"
