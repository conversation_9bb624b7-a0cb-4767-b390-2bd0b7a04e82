#!/usr/bin/env python3
"""
创建测试音频数据
"""
import base64
import json

def create_test_audio_data(duration_seconds, sample_rate=16000):
    """
    创建指定时长的测试音频数据
    :param duration_seconds: 音频时长（秒）
    :param sample_rate: 采样率
    :return: 音频数据（字节）
    """
    # 16位单声道PCM数据
    bytes_per_second = sample_rate * 2  # 16位 = 2字节
    total_bytes = int(bytes_per_second * duration_seconds)
    
    # 创建简单的测试音频数据（静音）
    audio_data = b'\x00' * total_bytes
    return audio_data

def create_audio_param(sample_rate=16000, audio_format='pcm', index=1):
    """创建音频参数"""
    return {
        'sampleRate': sample_rate,
        'format': audio_format,
        'index': index
    }

def encode_audio_param(audio_param):
    """编码音频参数为base64"""
    return base64.b64encode(json.dumps(audio_param).encode()).decode()

# 创建不同长度的测试数据
test_cases = [
    ("短音频", 0.5, "应该被拒绝（太短）"),
    ("正常音频", 30, "应该正常处理"),
    ("长音频", 90, "应该自动分片为2段"),
    ("超长音频", 180, "应该自动分片为3段"),
    ("极长音频", 300, "应该自动分片为5段")
]

print("=== 测试音频数据生成 ===")
for name, duration, expected in test_cases:
    audio_data = create_test_audio_data(duration)
    audio_param = create_audio_param()
    encoded_param = encode_audio_param(audio_param)
    
    print(f"\n{name} ({duration}秒):")
    print(f"  数据大小: {len(audio_data)} 字节")
    print(f"  预期结果: {expected}")
    print(f"  audio-param: {encoded_param}")
    print(f"  数据前10字节: {audio_data[:10]}")

print("\n=== 使用方法 ===")
print("1. 复制上面的 audio-param 值")
print("2. 使用对应长度的音频数据")
print("3. 调用 /weiwei/stream/asr 接口")
print("4. 观察日志中的分片信息")
