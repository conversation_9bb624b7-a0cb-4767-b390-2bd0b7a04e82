build:
  tools:
    python_with_virtualenv_and_buildout: 3.10 #3.10
  run:
    workDir: ./
    cmd:
      - sh build.sh
  target:
    distDir: ./
    files:
      - ./

autodeploy:
  hulkos: centos7
  tools:
    python_with_virtualenv_and_buildout: 3.10 #3.10
  targetDir: /opt/meituan/xiaomei-weiwei
  run: sh install.sh
  check: sh check.sh `hostname`:8080/monitor/alive
  checkRetry: 10
  checkInterval: 20s