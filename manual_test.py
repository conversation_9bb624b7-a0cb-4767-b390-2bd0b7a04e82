#!/usr/bin/env python3
"""
手动测试ASR修复 - 不依赖任何外部库
"""

def calculate_audio_duration(audio_data, sample_rate, channels=1, sample_width=2):
    """计算音频数据的时长（秒）"""
    if not audio_data:
        return 0
    
    # 计算样本数
    bytes_per_sample = channels * sample_width
    total_samples = len(audio_data) // bytes_per_sample
    
    # 计算时长
    duration = total_samples / sample_rate
    return duration

def validate_audio_data(audio_data, audio_param):
    """验证音频数据是否符合要求"""
    ASR_MIN_DURATION = 0.1  # 最小时长100毫秒
    ASR_MAX_DURATION = 60.0  # 最大时长60秒
    ASR_MAX_SIZE = 10 * 1024 * 1024  # 最大音频数据大小10MB
    
    if not audio_data:
        return False, "音频数据为空"
    
    # 获取音频参数
    sample_rate = audio_param.get('sampleRate', 16000)
    
    # 计算音频时长
    duration = calculate_audio_duration(audio_data, sample_rate)
    
    # 检查时长限制
    if duration < ASR_MIN_DURATION:
        return False, (f"音频时长过短: {duration:.2f}秒，"
                       f"最小需要{ASR_MIN_DURATION}秒")
    
    if duration > ASR_MAX_DURATION:
        return False, (f"音频时长过长: {duration:.2f}秒，"
                       f"最大允许{ASR_MAX_DURATION}秒")
    
    # 检查音频数据大小
    if len(audio_data) > ASR_MAX_SIZE:
        return False, (f"音频数据过大: {len(audio_data)}字节，"
                       f"最大允许{ASR_MAX_SIZE}字节")
    
    return True, None

# 手动测试用例
print("=== ASR修复测试 ===")

# 测试1: 空音频
print("\n1. 测试空音频:")
result = validate_audio_data(b'', {'sampleRate': 16000})
print(f"   结果: {result}")

# 测试2: 过短音频 (50字节 ≈ 0.0016秒)
print("\n2. 测试过短音频:")
short_data = b'\x00' * 50
duration = calculate_audio_duration(short_data, 16000)
result = validate_audio_data(short_data, {'sampleRate': 16000})
print(f"   时长: {duration:.4f}秒")
print(f"   结果: {result}")

# 测试3: 正常音频 (32000字节 = 1秒)
print("\n3. 测试正常音频:")
normal_data = b'\x00' * 32000
duration = calculate_audio_duration(normal_data, 16000)
result = validate_audio_data(normal_data, {'sampleRate': 16000})
print(f"   时长: {duration:.2f}秒")
print(f"   结果: {result}")

# 测试4: 过长音频 (2000000字节 ≈ 62.5秒)
print("\n4. 测试过长音频:")
long_data = b'\x00' * 2000000
duration = calculate_audio_duration(long_data, 16000)
result = validate_audio_data(long_data, {'sampleRate': 16000})
print(f"   时长: {duration:.1f}秒")
print(f"   结果: {result}")

# 测试5: 不同采样率
print("\n5. 测试不同采样率:")
test_cases = [
    (8000, 16000, "8kHz"),
    (16000, 32000, "16kHz"), 
    (44100, 88200, "44.1kHz")
]

for sample_rate, data_size, desc in test_cases:
    data = b'\x00' * data_size
    duration = calculate_audio_duration(data, sample_rate)
    print(f"   {desc}: {data_size}字节 = {duration:.2f}秒")

print("\n=== 测试完成 ===")
print("\n修复说明:")
print("- 现在会在调用ASR API前验证音频数据")
print("- 音频时长必须在0.1-60秒之间")
print("- 音频大小不能超过10MB")
print("- 提供详细的错误信息帮助调试")
