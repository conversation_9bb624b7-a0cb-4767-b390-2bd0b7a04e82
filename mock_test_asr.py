#!/usr/bin/env python3
"""
模拟测试ASR分片功能（不需要真实的ASR服务）
"""

# 复制核心函数进行测试
def calculate_audio_duration(audio_data, sample_rate, channels=1, sample_width=2):
    """计算音频数据的时长（秒）"""
    if not audio_data:
        return 0
    
    bytes_per_sample = channels * sample_width
    total_samples = len(audio_data) // bytes_per_sample
    duration = total_samples / sample_rate
    return duration

def split_audio_data(audio_data, sample_rate, channels=1, sample_width=2, max_duration=60.0):
    """将长音频数据分割成多个片段"""
    if not audio_data:
        return []
    
    # 计算每个片段的字节数
    bytes_per_second = sample_rate * channels * sample_width
    max_bytes_per_chunk = int(bytes_per_second * max_duration * 0.9)  # 留10%余量
    
    chunks = []
    offset = 0
    chunk_index = 1
    
    while offset < len(audio_data):
        # 计算当前片段的结束位置
        end_offset = min(offset + max_bytes_per_chunk, len(audio_data))
        
        # 确保在样本边界上分割
        bytes_per_sample = channels * sample_width
        end_offset = (end_offset // bytes_per_sample) * bytes_per_sample
        
        chunk_data = audio_data[offset:end_offset]
        if chunk_data:
            chunks.append({
                'data': chunk_data,
                'index': chunk_index,
                'offset': offset,
                'size': len(chunk_data),
                'duration': calculate_audio_duration(chunk_data, sample_rate, channels, sample_width)
            })
            chunk_index += 1
        
        offset = end_offset
    
    return chunks

def validate_audio_data(audio_data, audio_param):
    """验证音频数据是否符合要求"""
    ASR_MIN_DURATION = 0.1  # 最小时长100毫秒
    ASR_MAX_DURATION = 60.0  # 最大时长60秒
    ASR_MAX_SIZE = 10 * 1024 * 1024  # 最大音频数据大小10MB
    
    if not audio_data:
        return False, "音频数据为空", False

    # 获取音频参数
    sample_rate = audio_param.get('sampleRate', 16000)

    # 计算音频时长
    duration = calculate_audio_duration(audio_data, sample_rate)

    # 检查时长限制
    if duration < ASR_MIN_DURATION:
        return False, (f"音频时长过短: {duration:.2f}秒，"
                       f"最小需要{ASR_MIN_DURATION}秒"), False

    # 检查音频数据大小
    if len(audio_data) > ASR_MAX_SIZE:
        return False, (f"音频数据过大: {len(audio_data)}字节，"
                       f"最大允许{ASR_MAX_SIZE}字节"), False

    # 如果时长超过限制，建议分片处理
    if duration > ASR_MAX_DURATION:
        return True, (f"音频时长较长: {duration:.2f}秒，"
                     f"将自动分片处理"), True

    return True, None, False

def mock_asr_call(chunk_data, chunk_info):
    """模拟ASR API调用"""
    # 模拟识别结果
    duration = chunk_info['duration']
    text = f"这是第{chunk_info['index']}段音频的识别结果，时长{duration:.1f}秒。"
    
    return {
        'errcode': 0,
        'data': {
            'text': text,
            'status': 3
        }
    }

def test_audio_processing(test_name, duration_seconds, sample_rate=16000):
    """测试音频处理流程"""
    print(f"\n=== {test_name} ({duration_seconds}秒) ===")
    
    # 创建测试音频数据
    bytes_per_second = sample_rate * 2  # 16位单声道
    total_bytes = int(bytes_per_second * duration_seconds)
    audio_data = b'\x00' * total_bytes
    audio_param = {'sampleRate': sample_rate, 'format': 'pcm'}
    
    print(f"📊 音频信息:")
    print(f"   大小: {len(audio_data)} 字节")
    print(f"   时长: {duration_seconds} 秒")
    
    # 验证音频数据
    is_valid, message, should_split = validate_audio_data(audio_data, audio_param)
    
    print(f"🔍 验证结果:")
    print(f"   有效: {is_valid}")
    print(f"   需要分片: {should_split}")
    print(f"   消息: {message}")
    
    if not is_valid:
        print("❌ 验证失败，处理结束")
        return
    
    if should_split:
        # 分片处理
        chunks = split_audio_data(audio_data, sample_rate)
        print(f"🔄 分片结果: {len(chunks)}个片段")
        
        all_texts = []
        for chunk in chunks:
            print(f"   片段{chunk['index']}: {chunk['size']}字节, {chunk['duration']:.2f}秒")
            
            # 模拟ASR调用
            result = mock_asr_call(chunk['data'], chunk)
            if result['errcode'] == 0:
                text = result['data']['text']
                all_texts.append(text)
                print(f"     识别结果: {text}")
        
        # 合并结果
        combined_text = ''.join(all_texts)
        print(f"✅ 最终结果: {combined_text}")
        print(f"📈 统计信息: {len(chunks)}个片段，总时长{duration_seconds}秒")
        
    else:
        # 单个音频处理
        print("✅ 单个音频处理（无需分片）")
        mock_result = mock_asr_call(audio_data, {'index': 1, 'duration': duration_seconds})
        print(f"📝 识别结果: {mock_result['data']['text']}")

def main():
    """主测试函数"""
    print("🚀 ASR分片功能模拟测试")
    print("=" * 50)
    
    test_cases = [
        ("短音频测试", 0.05),    # 应该被拒绝
        ("正常音频测试", 30),     # 正常处理
        ("长音频测试", 90),       # 分片为2段
        ("超长音频测试", 150),    # 分片为3段
        ("极长音频测试", 300),    # 分片为5段
    ]
    
    for test_name, duration in test_cases:
        test_audio_processing(test_name, duration)
    
    print("\n" + "=" * 50)
    print("🎉 模拟测试完成！")
    print("\n📋 测试总结:")
    print("✅ 短音频被正确拒绝")
    print("✅ 正常音频正常处理")
    print("✅ 长音频自动分片处理")
    print("✅ 分片结果正确合并")
    print("\n💡 下一步:")
    print("1. 部署代码到服务器")
    print("2. 使用真实ASR接口测试")
    print("3. 观察服务器日志")

if __name__ == "__main__":
    main()
