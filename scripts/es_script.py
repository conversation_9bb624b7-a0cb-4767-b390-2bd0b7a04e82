import os
import requests
import concurrent.futures

# 定义请求的URL
url = "http://*************:8080/weiwei/ingredient/es-upsert"

# 读取目录下的所有.md文件，使用生成器逐行返回数据
def read_md_files(directory):
    # 读取已上传的日志
    uploaded_names = set()
    uploaded_data = set()
    if os.path.exists("upload_log.txt"):
        with open("upload_log.txt", "r", encoding="utf-8") as log_file:
            for line in log_file:
                name, data = line.strip().split('@', 1)
                uploaded_names.add(name)
                uploaded_data.add(data)

    order_number = 1
    for filename in os.listdir(directory):
        if filename.endswith(".md"):
            with open(os.path.join(directory, filename), 'r', encoding='utf-8') as file:
                for line in file:
                    line = line.strip()
                    if line:  # 确保行不为空
                        # 确保name唯一
                        while True:
                            name = f"订单{order_number}"
                            if name not in uploaded_names:
                                break
                            order_number += 1
                        data = {
                            "name": name,
                            "data": line,
                            "status": 1
                        }
                        if data['data'] not in uploaded_data:
                            yield data
                            uploaded_names.add(name)  # 添加到已上传的名字集合中
                        else:
                            print(f"已上传: {data['data']}")
                        order_number += 1

# 发送POST请求
def send_post_request(data):
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            # 如果上传成功，写入日志
            with open("upload_log.txt", "a", encoding="utf-8") as log_file:
                log_file.write(f"{data['name']}@{data['data']}\n")
        return response.status_code, response.json()
    except Exception as e:
        return None, str(e)

# 主函数
def main():
    directory = "/Users/<USER>/Desktop/meituan/xiaomei/xiaomei-weiwei/data/vex_data"
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(send_post_request, data) for data in read_md_files(directory)]
        for future in concurrent.futures.as_completed(futures):
            status_code, result = future.result()
            # print(f"Status Code: {status_code}, Result: {result}")

if __name__ == "__main__":
    main()
