import requests
import json
from tqdm import tqdm
from tqdm.auto import trange
import argparse
from datetime import datetime
import sys
import os
import time
from loguru import logger

# 配置日志输出
# 移除默认处理程序
logger.remove()

# 添加新的处理程序，使用简洁的格式
# 创建日志文件，同时保留控制台输出

# 完全移除默认的日志处理程序
logger.configure(handlers=[])

# 定义一个自定义格式化函数，避免使用默认格式化器
def format_record(record):
    # 简化的格式化函数，不使用trace_id
    color_level = {
        "DEBUG": "<blue>",
        "INFO": "<green>",
        "SUCCESS": "<green>",
        "WARNING": "<yellow>",
        "ERROR": "<red>",
        "CRITICAL": "<red>"
    }.get(record["level"].name, "<blue>")
    
    return f"<green>[处理进度]</green> <blue>{record['time'].strftime('%H:%M:%S')}</blue> | {color_level}{record['message']}</level>"

# 控制台输出 - 只显示关键信息
logger.add(
    sys.stderr, 
    format=format_record,
    level="INFO",
    filter=lambda record: record["name"] == "__main__",
    diagnose=False,
    backtrace=False,
    colorize=True
)

# 文件输出 - 记录详细日志
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'order_update.log')
logger.add(
    log_file, 
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}", 
    level="DEBUG",
    rotation="10 MB",
    retention=3,
    diagnose=False,
    backtrace=False
)
sys.path.append("/Users/<USER>/Downloads/project/xiaomei-weiwei/app")
from configs.lion_config import lion_client
from position import reverse_geocode
from shop_data_fixed import analyzer
import asyncio

OrderInfoUrl = "http://*************:8080/web/v1/order/orderList"
OrderDetailsUrl = "http://*************:8080/web/v1/order/getOrderGroupResultByViewIdAndUserIdWithParam"
ProductionUrl = "https://xiaomeiai.meituan.com/web/v1/order/getSkuInfoBySkuIds"
WeiweiUrl = "https://xiaomeiai.meituan.com"
INDEX_MERCHANT_INFO = "merchant_food"
INGREDIENT_INDEX = "weiwei_ingredient_analysis"

access_token = "eAGF0K9LBEEYxnEGReQEkUvGDYa7CzLzzuzsOybXUzH6CwSL7Oy8axC8sILlwt2BwYsaRBH8iQZBsRm0aNRsEIMI6gWTwaBB74LZP-B5-PDtZD0ruztd3vr15d0jQEaACzhEkYYBz6LVTjkhuQYllYtkQgUQ0vFYBVaroW-Wzc-QnYppkcojGkIptMCiHvUNYmj8cBSVAOMjL8Kwd_D1_PkEOQb_HmOLNNg2Vq8cfTzA-NXP9u0N1Fgh0zExWSw5ymZfDo8b53uvqydvu9XG6WHjrNrb7lUuNvO595-N-3tYY51_sC3Wb0XseGKt0glqZ4y2HA1BAtohUMTnRCB1E4pKI1f7rG-ZbBktUaR9Q5ZACUSUsbRxqwEIcoGY9VzkJ05BJHiBVITGNFv5RkKA1qnmtMa652kpjGNK0-nSAi3WWVualn4BnHl6Dg**eAEFwQcRwEAMAzBKWef04WSZP4RKLWcdHippuoMHf3MWS3BxtKYkYwifQdVV0qW6ha776Q9LQBKQ**HYnI83zgtTYXchUZHxdqkdU785CmlYCgDl-Dt1odfpWAsOgNXPrpORQHxtgTXvI4gvDCrL71iDEwuQYMsZgJVA**MjM1ODA2Nzcsc3VuaGFpeXVlLOWtmea1t-WysyxzdW5oYWl5dWVAbWVpdHVhbi5jb20sMSwzNDE4MTk3MSwxNzQyODQzMzU1NDEx"
request_body = {
    "userId": "868",
    "beginTime": "1704344661",
    "endTime": "1733293409",
    "offset": "0",
    "limit": "20",
    "partnerIdList": [
        6
    ]
}

headers = {
                "Authorization": "Bearer 1838824241643597850",
                "Content-Type": "application/json",
                "access-token": access_token
            }

def unixTimestamp2Time(timestamp:str)->str:
    return datetime.fromtimestamp(int(timestamp)).strftime("%Y-%m-%d %H:%M:%S")

def time2UnixTimestamp(time_str:str)->str:
    import time
    return int(time.mktime(time.strptime(time_str, "%Y-%m-%d %H:%M:%S")))

def get_order_summary(user_id, begin_time, end_time, offset, limit)->list[int, list[dict]]:
    request_body = {
        "userId": user_id,
        "beginTime": begin_time,
        "endTime": end_time,
        "offset": offset,
        "limit": limit, # 数目
        "partnerIdList": [6] # 外卖
    }

    response = requests.post(OrderInfoUrl, headers=headers, json=request_body)
    responseInfo = response.json()
    data = responseInfo.get('data', {})
    orderNum = int(data.get("total", 0))
    orderList = data.get("orderList", [])
    if orderNum == len(orderList):
        return orderNum, orderList
    else:
        print(f"Failed to get order summary for user {user_id}")
        return 0, []
        
def get_order_details(userId, viewId)->list[dict]:
    global headers
    reqeust_body = {
        "userId":userId,
        "viewId":viewId
        }
    response = requests.post(OrderDetailsUrl, headers=headers, json=reqeust_body)
    responseInfo = response.json()
    data = responseInfo.get('data', "")
    
    return json.loads(data).get("wmOrderDetailResults", [])
    

def generate_order_sentence(time, merchant_name: str, price: str, food_name_list: list[str], food_price:list[str], position: str, merchant_type: str)->str:
    ret = f"<时间:{time}> <店铺:{merchant_name}> <位置:{position}> <类型:{merchant_type}> <总价:{price}>"
    for food, price in zip(food_name_list, food_price):
        ret += f" <{food} {price}元> "
    return ret


def get_order_num_in_es_database(search_type:str="订单"):
    logger.info(f"查询ES数据库中的{search_type}数量")
    global WeiweiUrl
    query_url = WeiweiUrl + '/weiwei/ingredient/es-search'
    response = requests.post(query_url, 
                            json={
                                    "method":"term",
                                    "ingredient_list":[f"{search_type}数目@@@@"]
                                })
    
    if response.status_code == 200:
        response_data = response.json()
        if len(response_data.get("data")) != 0:
            return int(response_data.get("data")[0])
        else:
            return 0
    else:
        print(f"查询失败，错误码为: {response.status_code}, 错误信息为: {response.text}")
        return -1

def get_merchant_position(merchant_details)->str:
    logger.info(f"获取商家位置信息")
    longitude = merchant_details.get("longitude", "")["value"] / 1000000
    latitude = merchant_details.get("latitude", "")["value"] / 1000000
    result = reverse_geocode(latitude, longitude)
    return result.get("display_name", "None")

async def main():
    logger.info("程序开始执行")
    global WeiweiUrl, headers

    parser = argparse.ArgumentParser()
    parser.add_argument("--begin_time", type=int, default=1740758400)  # 2025年3月1日
    parser.add_argument("--end_time", type=int, default=1743350399)  # 2025年3月30日
    parser.add_argument("--time_type", type=str, default="unix")
    parser.add_argument("--offset", type=int, default=0)
    parser.add_argument("--limit", type=int, default=1)
    parser.add_argument("--resume", action="store_true", help="从上次处理的位置继续")
    args = parser.parse_args()
    if args.time_type == "unix":
        startTime = args.begin_time
        endTime = args.end_time
    elif args.time_type == "date":
        startTime = time2UnixTimestamp(args.begin_time)
        endTime = time2UnixTimestamp(args.end_time)
    else:
        raise ValueError("Invalid time type. Must be 'unix' or 'date'.")
    # 从本地文件获取userID列表
    user_id_file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data/userid/utvs_crowd_data_1516992_1743421601233_84024.txt')
    logger.info(f"➡️ 正在加载用户数据...")
    with open(user_id_file_path, 'r') as f:
        userIdList = [int(line.strip()) for line in f if line.strip()]
    logger.success(f"✅ 成功加载 {len(userIdList)} 个用户ID")
    
    # 断点续传文件路径
    checkpoint_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'order_update_checkpoint.json')
    
    # 初始化已处理的用户索引
    processed_idx = 0
    
    # 如果启用了断点续传，尝试从文件中读取上次处理的位置
    if args.resume and os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r') as f:
                checkpoint_data = json.load(f)
                processed_idx = checkpoint_data.get('last_processed_idx', 0)
                logger.success(f"⚙️ 断点续传模式：从索引 {processed_idx} 继续处理 (进度 {processed_idx}/{len(userIdList)})")
        except Exception as e:
            logger.error(f"读取断点文件失败，将从头开始处理")
            processed_idx = 0
    else:
        if args.resume:
            logger.warning(f"未找到断点续传文件，将从头开始处理")
        else:
            logger.info("⚙️ 全新处理模式：从头开始处理")
    
    # 只处理尚未处理的用户ID
    userIdList = userIdList[processed_idx:]
    logger.info(f"📊 待处理用户: {len(userIdList)} 个")
    
    # print()
    # userIdList = [868]
    total_num = get_order_num_in_es_database()+1
    logger.info(f"📂 当前数据库订单总数: {total_num} 条")
    
    merchant_details_dict = {}
    logger.info(f"🕐 处理时间范围: {unixTimestamp2Time(startTime)} 到 {unixTimestamp2Time(endTime)}")
    
    # 创建一个更详细的进度条
    progress_bar = tqdm(
        enumerate(userIdList),
        total=len(userIdList),
        desc="处理用户订单",
        unit="用户",
        bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}{postfix}]"
    )
    
    # 使用导入的time模块，避免命名冲突
    start_process_time = time.time()
    for idx, userid in progress_bar:
        real_idx = idx + processed_idx  # 计算真实的索引，用于保存断点
        
        # 更新进度条信息
        elapsed = time.time() - start_process_time
        progress_bar.set_postfix({
            '用户ID': userid,
            '进度': f"{real_idx}/{len(userIdList) + processed_idx}",
            '完成率': f"{real_idx/(len(userIdList) + processed_idx):.1%}",
            '已用时间': f"{int(elapsed//3600)}h {int((elapsed%3600)//60)}m {int(elapsed%60)}s"
        })
        
        orderNum, orderList = get_order_summary(userid, startTime, endTime, args.offset, args.limit)
        # 创建订单详情的进度条
        order_progress = tqdm(
            enumerate(orderList),
            total=len(orderList),
            desc=f"用户 {userid} 的订单详情",
            leave=False,
            unit="订单",
            bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]"
        )
        for jdx, order in order_progress:
            orderDetails = get_order_details(userid, order["orderId"])
            merchant_name = order["title"]
            info = json.loads(order["info"])
            order_time = info.get("info1", "")
            price = info.get("info2", "")
            poiId = order["poiId"]
            foodIdList = [food["wm_food_id"] for food in orderDetails]
            spuIdList = [food["wm_food_spu_id"] for food in orderDetails]
            foodNameList = [food["food_name"] for food in orderDetails]
            foodPrice = [food["food_price"] for food in orderDetails]
            try:
                if poiId not in merchant_details_dict:
                    # 处理Hive查询可能返回None的情况
                    poi_data = await analyzer.get_poi_data_from_hive([poiId])
                    if poi_data is None:
                        logger.warning(f"无法获取商家数据 (poiId: {poiId})，跳过此订单")
                        continue
                    
                    merchant_details_dict[poiId] = analyzer.process(poi_data[0])
                
                merchant_details = merchant_details_dict[poiId]
                position = get_merchant_position(merchant_details)
                
                # 每处理完一个用户，更新断点文件
                with open(checkpoint_file, 'w') as f:
                    json.dump({'last_processed_idx': real_idx + 1}, f)
                logger.debug(f"更新断点文件，当前进度: {real_idx + 1}/{len(userIdList) + processed_idx}")
            except Exception as e:
                logger.error(f"处理商家数据时出错 (poiId: {poiId}): {e}，跳过此订单")
                continue
            prime = merchant_details.get("primary_first_tag_name", "")
            second = merchant_details.get("primary_second_tag_name", "")
            third = merchant_details.get("primary_third_tag_name", "")
            merchant_type = f"{prime}/{second}/{third}"

            sentence = generate_order_sentence(order_time, merchant_name, price, foodNameList, foodPrice, position, merchant_type) # 生成订单描述，用于Vex召回

            # 添加到Vex
            vexData = {
                "text": sentence
            }
            logger.debug(f"[{userid}]vexData: {vexData}")
            vex_url = f"{WeiweiUrl}/weiwei/vex/add"
            response = requests.post(vex_url, headers=headers, json=vexData)
            if response.status_code != 200:
                logger.error(f"❌ Vex写入失败: 用户ID={userid}, 订单ID={order['orderId']}")
            else:
                logger.debug(f"✅ Vex写入成功: 用户ID={userid}, 订单ID={order['orderId']}")
            

            # 添加到ES
            # ES用于召回
            ragData = {
                "index_name": INGREDIENT_INDEX,
                "doc_id": f"订单{total_num}",
                "doc": {
                    "doc_as_upsert": True,
                    "doc": {
                        "analysis": sentence,
                        "ingredient": f"订单{total_num}"
                    }
                }
            }
            logger.debug(f"[{userid}]ragData: {ragData}")
            rag_url = f"{WeiweiUrl}/weiwei/es-upsert"
            response = requests.post(rag_url, headers=headers, json=ragData)
            if response.status_code != 200:
                logger.error(f"❌ ES写入失败: 订单ID={total_num}")
                with open("es_upload_log_test.txt", "a", encoding="utf-8") as log_file:
                    log_file.write(f"{ragData['doc_id']}@{ragData['doc']['doc']['analysis']}\n")
                continue
            else:
                logger.debug(f"✅ ES写入成功: 订单ID={total_num}")
            total_num += 1

            # ES用于构造url
            for foodId, spuId, foodName in zip(foodIdList, spuIdList, foodNameList):
                esData = {
                    "index_name": INDEX_MERCHANT_INFO,
                    "doc_id": f"{poiId}_{foodId}_{spuId}",
                    "doc": {
                        "doc_as_upsert": True,
                        "doc": {"poi_id": poiId, "poi_name_text": merchant_name, "poi_name_keyword": merchant_name, "food_id": foodId, "food_name_text": foodName, "food_name_keyword": foodName, "spu_id": spuId}
                    }
                }
                print(f"[{userid}]esData: {esData}")
                es_url = f"{WeiweiUrl}/weiwei/es-upsert"
                response = requests.post(es_url, headers=headers, json=esData)
                if response.status_code != 200:
                    with open("es_upload_log_test.txt", "a", encoding="utf-8") as log_file:
                        log_file.write(f"{esData['doc_id']}@{esData['doc']['doc']['poi_id']}@{esData['doc']['doc']['food_id']}@{esData['doc']['doc']['spu_id']}\n")
                continue

            total_num+=1
    numData = {
        "name": f"订单数目@@@@", "data": f"{total_num-1}", "status": 1
    }
    es_url = f"{WeiweiUrl}/weiwei/ingredient/es-upsert"
    response = requests.post(es_url, headers=headers, json=numData)
    if response.status_code != 200:
        with open("es_upload_log_test.txt", "a", encoding="utf-8") as log_file:
            log_file.write(f"未成功更新订单总数，<订单数目@@@@>{total_num}, 请将这条消息发送给维护的同学。")
        print(f"未成功更新订单总数，<订单数目@@@@>{total_num}, 请将这条消息发送给维护的同学。")
                

            
            

    

if __name__ == '__main__':
    # ret = get_order_details(868, 301357260874309305)
    # with open('order.json', 'w') as f:
    #     json.dump(ret, f, indent=4, ensure_ascii=False)
    asyncio.run(main())
    