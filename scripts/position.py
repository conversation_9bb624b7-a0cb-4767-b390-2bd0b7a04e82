from geopy.geocoders import Nominatim
from geopy.exc import GeocoderTimedOut, GeocoderServiceError

def reverse_geocode(latitude, longitude):
    """通过经纬度反向地理编码获取位置详情"""
    geolocator = Nominatim(user_agent="my_geocoder", timeout=10)
    try:
        location = geolocator.reverse(f"{latitude}, {longitude}", language="zh-CN")
        if location:
            # location.raw 包含详细的地址字典信息
            address = location.raw.get("address", {})
            city = address.get("city") or address.get("town") or address.get("village")
            road = address.get("road")
            display_name = location.raw.get("display_name")
            return {
                "city": city,
                "road": road,
                "display_name": display_name,
                "full_address": address
            }
    except (GeocoderTimedOut, GeocoderServiceError) as e:
        print("地理编码服务出错:", e)
    return None

if __name__ == "__main__":
    # 示例经纬度，可以替换成需要查询的经纬度
    latitude = 40.034100
    longitude = 116.419000
    result = reverse_geocode(latitude, longitude)
    if result:
        print("城市:", result["city"])
        print("街道信息:", result["road"])
        print("完整地址:", result["display_name"])
    else:
        print("未能获取到位置信息")