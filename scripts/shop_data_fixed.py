import os
import json
import asyncio
import time
import re
from datetime import datetime, timedelta
from pathlib import Path

from pytalos.client import AsyncTalosClient, SDKScene

def load_poi_ids(max_count=500000) -> list:
    """
    这里就是获取若干商家id，跟HIVE查询本身关系不大
    """
    # 这里只是示例，返回一些测试用的商家ID
    return [i for i in range(1, min(100, max_count))]

class TalosQuery:
    def __init__(self, user, session_id):
        """初始化Talos查询客户端
        Args:
            user: MIS用户名，如：limingsheng
            session_id: 从文谍系统(https://data.sankuai.com/wendie/session)获取的session id，有效期7天
        """
        self.client = AsyncTalosClient(
            user,
            session_id,
            sdk_scene=SDKScene.MIS
        )
        # 发起认证
        self.client.open_session()

    async def execute(self, sql):
        try:
            # 提交查询
            qid = self.client.submit(statement=sql)
            print(f"[Talos] 查询ID: {qid}")
            
            finish = False
            while True:
                # 获取最新查询信息
                query_info = self.client.get_query_info(qid)
                # 打印查询引擎执行日志
                print(self.client.engine_log(qid))
                
                if query_info["status"] == "FINISHED":
                    finish = True
                    break
                elif query_info["status"] in ["QUERY_TIMEOUT", "FAILED", "KILLED"] or query_info["status"].startswith("ERROR_"):
                    print(f"[Talos] 查询失败，状态: {query_info['status']}")
                    finish = False
                    break
                
                print(f"[Talos] 查询进行中，当前状态: {query_info['status']}")
                await asyncio.sleep(5)
            
            if finish:
                # 查询成功，获取查询结果
                res = self.client.fetch_all(qid)
                return res["data"]
            return None
                
        except Exception as e:
            print(f"[Talos] 查询异常: {str(e)}")
            return None

class PoiInfoAnalyzer:
    def __init__(self):
        """初始化商家信息分析器"""
        # 注意：session_id需要从文谍系统获取：https://data.sankuai.com/wendie/session
        # session_id有效期为7天，过期需要重新获取
        self.talos_query = TalosQuery(
            user="limingsheng",
            session_id="5f0111701dfb48c3920ff756e1c78ff7" 
        )
        self.cache_dir = 'poi_cache'
        self.results_dir = 'poi_analysis_results'
        self.poi_ids = load_poi_ids()  # 加载POI ID
        os.makedirs(self.results_dir, exist_ok=True)
        os.makedirs(self.cache_dir, exist_ok=True)

    def _get_cache_path(self, date: str) -> str:
        """生成缓存文件路径"""
        return os.path.join(self.cache_dir, f"poi_data_{date}.json")

    def _get_cached_data(self, date: str) -> dict:
        """从本地文件缓存获取数据"""
        try:
            cache_path = self._get_cache_path(date)
            if os.path.exists(cache_path):
                # 检查缓存文件是否在7天内
                file_time = os.path.getmtime(cache_path)
                if time.time() - file_time < 7 * 24 * 60 * 60:  # 7天有效期
                    print(f"\n[缓存] 找到{date}的商家数据缓存")
                    with open(cache_path, 'r', encoding='utf-8') as f:
                        return json.load(f)
                else:
                    print(f"\n[缓存] {date}的缓存已过期")
            else:
                print(f"\n[缓存] 未找到{date}的缓存")
        except Exception as e:
            print(f"[缓存] 读取缓存出错: {str(e)}")
        return None

    def _save_to_cache(self, date: str, data: dict):
        """保存数据到本地文件缓存"""
        try:
            print("\n要缓存的数据内容:")
            print(json.dumps(data, ensure_ascii=False, indent=2))
            
            cache_path = self._get_cache_path(date)
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"[缓存] 成功保存{date}的商家数据缓存到: {cache_path}")
        except Exception as e:
            print(f"[缓存] 保存缓存出错: {str(e)}")

    async def get_poi_data_from_hive(self, poi_ids):
        """从Hive获取商家数据
        Args:
            poi_ids: 要查询的商家ID列表
        Returns:
            查询结果列表
        """
        try:
            # 将POI ID列表转换为字符串
            id_list = ",".join([f"'{id}'" for id in poi_ids])
            print(f"需要查询的商家数量: {len(poi_ids)}")

            # 使用新的SQL查询
            query = f"""
            SELECT 
                wm_poi_id,              -- 商家ID
                wm_poi_name,            -- 商家名称
                first_city_name,        -- 一级城市名
                second_city_name,       -- 二级城市名
                third_city_name,        -- 三级城市名
                city_type,              -- 城市类型0:未开站，1:直营，2:代理
                city_level,             -- 城市级别
                first_online_dt,        -- 第一次上线时间
                valid,                  -- 是否有效 0:下线，1:在线，2:上单中，3:审核通过待上线
                longitude,              -- 经度
                latitude,               -- 纬度
                poi_address,            -- 商家地址
                primary_first_tag_name, -- 主一级品类名
                primary_second_tag_name,-- 主二级品类名
                primary_third_tag_name, -- 主三级品类名
                is_open_flag,            -- 是否开店 0:未开店，1:已开店
                is_pic_flag,            -- 是否有头图 0:无图，1:有图
                poi_type,               -- 商家类型 1:大连锁商家(KA)，2:小连锁商家(CKA)，3:城市商家，4:流量连锁商家
                ol_time,                -- 商家当天营业时长（秒）
                brand_type,             -- 品牌类型 0:城市，3:大连锁，4:供应链，5:总部商超连锁，6:总部生鲜连锁，7:CKA小连锁，8:总部鲜花连锁，9:总部药品连锁，10:总部母婴连锁，11:总部美妆连锁，12:总部服饰鞋帽连锁，13:总部日用品连锁，14:流量连锁
                brand_name,              -- 品牌名称
                brand_level,             -- 品牌级别10:1级，20:2级，30:3级，40:4级，50:5级，60:6级，70:7级，80:8级，999:无
                brand_ka,               -- 品牌KA 0：不是KA，1：是KA
                aor_name,               -- 商圈名称
                poi_aor_name,           -- 直营当日蜂窝名称,代理商为null
                aor_type,               -- 商圈类型 0:未知，1:学校，2:白领。代理商家为0。通过商家经纬度计算得到月初蜂窝信息，经纬度不变则月内蜂窝保持不变
                logistics_type,         -- 物流类型 0:其他，1:商家自配，2:美团配送，3:第三方配送，4:企客，5:跑腿
                min_price,              -- 最低起送价
                min_shipping_fee,       -- 最低配送费
                is_super_poi,           -- 是否外卖优质商家，0:否，1:是
                online_time,            -- 商家首次上线且营业的时间
                delivery_time,          -- 门店每天配送时间，包含一周内营业时间窗口
                business_interval,      -- 门店每天营业时间窗口
                is_wcd_poi,              -- 是否全城送商家，0:否，1:是
                rest_type,              -- 置休类型，0:普通置休，所有人都可以恢复营业，1:客服置休or超级置休,仅总部可恢复营业，2:心跳置休，3:高级置休,城市团队和总部可恢复营业
                across_day,              -- 跨天预订天数，数据格式为1:3,表示至少1天，最多3天预订
                bulletin,               -- 商家公告
                story_id,               -- 商家故事ID
                lead_time,              -- 商家备餐时间 存储单位:秒 默认值为900
                total_business_time,    -- 累计营业时长,截止到统计日期前一天的累计时长
                meal_loss_percent,      -- 餐损比例
                diagnose_score,         -- 商家诊断分
                is_pinhf_poi,           -- 是否拼好饭商家，0:否，1:是
                is_cafeteria_poi,       -- 是否食堂商家，0:否，1:是
                aor_point_string        -- 商圈信息
            FROM mart_waimai.aggr_poi_info_dd
            WHERE dt = '20250211' and wm_poi_id IN ({id_list})
            """
            print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}][Hive] 执行商家数据查询...")

            result = await self.talos_query.execute(query)
            
            if result:
                print("[缓存] 数据已缓存，有效期7天")
                # 处理数据
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}][Hive] 处理后的数据条数: {len(result)}")
                
                # 保存到缓存
                self._save_to_cache('default', result)
                
                return result
            else:
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}][Hive] 获取数据失败")
                return None
                
        except Exception as e:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}][Hive] 获取数据失败: {str(e)}")
            print("\n可能的解决方案:")
            print("1. 请确保已连接美团VPN")
            print("2. 检查账号密码是否正确")
            print("3. 检查是否有访问该表的权限")
            print("4. 如果您能访问网页界面，请检查:")
            print(" - 表名和schema是否正确")
            print(" - 您的访问权限是否完整")
            print("5. 注意事项:")
            print(" - 夜间11点~早上7点可以使用Presto查询")
            return None

    def process(self, data):
        if not data:  # 检查是否有数据
            print("没有数据需要保存")
            return

        # 固定的列名列表，与SQL查询的顺序一致
        columns = [
            "wm_poi_id",
            "wm_poi_name",
            "first_city_name",
            "second_city_name",
            "third_city_name",
            "city_type",
            "city_level",
            "first_online_dt",
            "valid",
            "longitude",
            "latitude",
            "poi_address",
            "primary_first_tag_name",
            "primary_second_tag_name",
            "primary_third_tag_name",
            "is_open_flag",
            "is_pic_flag",
            "poi_type",
            "ol_time",
            "brand_type",
            "brand_name",
            "brand_level",
            "brand_ka",
            "aor_name",
            "poi_aor_name",
            "aor_type",
            "logistics_type",
            "min_price",
            "min_shipping_fee",
            "is_super_poi",
            "online_time",
            "delivery_time",
            "business_interval",
            "is_wcd_poi",
            "rest_type",
            "across_day",
            "bulletin",
            "story_id",
            "lead_time",
            "total_business_time",
            "meal_loss_percent",
            "diagnose_score",
            "is_pinhf_poi",
            "is_cafeteria_poi",
            "aor_point_string"
        ]
        
        # 字段说明映射
        field_comments = {
            "wm_poi_id": "商家ID",
            "wm_poi_name": "商家名称",
            "first_city_name": "一级城市名",
            "second_city_name": "二级城市名",
            "third_city_name": "三级城市名",
            "city_type": "城市类型(0:未开站，1:直营，2:代理)",
            "city_level": "城市级别",
            "first_online_dt": "第一次上线时间",
            "valid": "是否有效(0:下线，1:在线，2:上单中，3:审核通过待上线)",
            "longitude": "经度",
            "latitude": "纬度",
            "poi_address": "商家地址",
            "primary_first_tag_name": "主一级品类名",
            "primary_second_tag_name": "主二级品类名",
            "primary_third_tag_name": "主三级品类名",
            "is_open_flag": "是否开店(0:未开店，1:已开店)",
            "is_pic_flag": "是否有头图(0:无图，1:有图)",
            "poi_type": "商家类型(1:大连锁商家(KA)，2:小连锁商家(CKA)，3:城市商家，4:流量连锁商家)",
            "ol_time": "商家当天营业时长（秒）",
            "brand_type": "品牌类型(0:城市，3:大连锁，4:供应链，5-14:各类总部连锁)",
            "brand_name": "品牌名称",
            "brand_level": "品牌级别(10:1级，20:2级，30:3级，40:4级，50:5级，60:6级，70:7级，80:8级，999:无)",
            "brand_ka": "品牌KA(0:不是KA，1:是KA)",
            "aor_name": "商圈名称",
            "poi_aor_name": "直营当日蜂窝名称",
            "aor_type": "商圈类型(0:未知，1:学校，2:白领)",
            "logistics_type": "物流类型(0:其他，1:商家自配，2:美团配送，3:第三方配送，4:企客，5:跑腿)",
            "min_price": "最低起送价",
            "min_shipping_fee": "最低配送费",
            "is_super_poi": "是否外卖优质商家(0:否，1:是)",
            "online_time": "商家首次上线且营业的时间",
            "delivery_time": "门店每天配送时间",
            "business_interval": "门店每天营业时间窗口",
            "is_wcd_poi": "是否全城送商家(0:否，1:是)",
            "rest_type": "置休类型(0:普通置休，1:客服置休，2:心跳置休，3:高级置休)",
            "across_day": "跨天预订天数",
            "bulletin": "商家公告",
            "story_id": "商家故事ID",
            "lead_time": "商家备餐时间(秒)",
            "total_business_time": "累计营业时长",
            "meal_loss_percent": "餐损比例",
            "diagnose_score": "商家诊断分",
            "is_pinhf_poi": "是否拼好饭商家(0:否，1:是)",
            "is_cafeteria_poi": "是否食堂商家(0:否，1:是)",
            "aor_point_string": "商圈信息"
        }

        # 转换数据格式
        result = {}
        
        # 处理商家数据
        poi_data = {}
        for i, field in enumerate(columns):
            value = str(data[i]).strip() if data[i] is not None else None  # 确保值是字符串并去除空格
            
            # 处理NULL值和空字符串
            if value in ("NULL", ""):
                value = None
            elif field in ("longitude", "latitude", "min_price", "min_shipping_fee", "lead_time", 
                            "total_business_time", "meal_loss_percent", "diagnose_score"):
                try:
                    value = float(value) if value != "0" else 0
                except (ValueError, TypeError):
                    value = None
            elif field in ("city_type", "city_level", "valid", "is_open_flag", "is_pic_flag", "poi_type", 
                            "ol_time", "brand_type", "brand_level", "brand_ka", "aor_type", "logistics_type", 
                            "is_super_poi", "is_wcd_poi", "rest_type", "story_id", "is_pinhf_poi", "is_cafeteria_poi"):
                try:
                    value = int(value) if value != "0" else 0
                except (ValueError, TypeError):
                    value = None
            
            poi_data[field] = {
                "value": value,
                "description": field_comments.get(field, f"未知字段: {field}")
            }
        
        return poi_data

    def save_results(self, data, filename):
        """保存结果到JSON文件

        Args:
            data: 要保存的数据，从Talos返回的列表格式
            filename: 保存的文件名
        """
        if not data:  # 检查是否有数据
            print("没有数据需要保存")
            return

        # 统计信息
        total_rows = len(data)
        skipped_rows = 0
        null_id_rows = 0
        success_rows = 0
        
        print(f"\n开始处理数据...")
        print(f"总数据行数: {total_rows}")

        # 固定的列名列表，与SQL查询的顺序一致
        columns = [
            "wm_poi_id",
            "wm_poi_name",
            "first_city_name",
            "second_city_name",
            "third_city_name",
            "city_type",
            "city_level",
            "first_online_dt",
            "valid",
            "longitude",
            "latitude",
            "poi_address",
            "primary_first_tag_name",
            "primary_second_tag_name",
            "primary_third_tag_name",
            "is_open_flag",
            "is_pic_flag",
            "poi_type",
            "ol_time",
            "brand_type",
            "brand_name",
            "brand_level",
            "brand_ka",
            "aor_name",
            "poi_aor_name",
            "aor_type",
            "logistics_type",
            "min_price",
            "min_shipping_fee",
            "is_super_poi",
            "online_time",
            "delivery_time",
            "business_interval",
            "is_wcd_poi",
            "rest_type",
            "across_day",
            "bulletin",
            "story_id",
            "lead_time",
            "total_business_time",
            "meal_loss_percent",
            "diagnose_score",
            "is_pinhf_poi",
            "is_cafeteria_poi",
            "aor_point_string"
        ]
        
        print(f"列名: {columns}")  # 打印列名以便调试
        
        # 字段说明映射
        field_comments = {
            "wm_poi_id": "商家ID",
            "wm_poi_name": "商家名称",
            "first_city_name": "一级城市名",
            "second_city_name": "二级城市名",
            "third_city_name": "三级城市名",
            "city_type": "城市类型(0:未开站，1:直营，2:代理)",
            "city_level": "城市级别",
            "first_online_dt": "第一次上线时间",
            "valid": "是否有效(0:下线，1:在线，2:上单中，3:审核通过待上线)",
            "longitude": "经度",
            "latitude": "纬度",
            "poi_address": "商家地址",
            "primary_first_tag_name": "主一级品类名",
            "primary_second_tag_name": "主二级品类名",
            "primary_third_tag_name": "主三级品类名",
            "is_open_flag": "是否开店(0:未开店，1:已开店)",
            "is_pic_flag": "是否有头图(0:无图，1:有图)",
            "poi_type": "商家类型(1:大连锁商家(KA)，2:小连锁商家(CKA)，3:城市商家，4:流量连锁商家)",
            "ol_time": "商家当天营业时长（秒）",
            "brand_type": "品牌类型(0:城市，3:大连锁，4:供应链，5-14:各类总部连锁)",
            "brand_name": "品牌名称",
            "brand_level": "品牌级别(10:1级，20:2级，30:3级，40:4级，50:5级，60:6级，70:7级，80:8级，999:无)",
            "brand_ka": "品牌KA(0:不是KA，1:是KA)",
            "aor_name": "商圈名称",
            "poi_aor_name": "直营当日蜂窝名称",
            "aor_type": "商圈类型(0:未知，1:学校，2:白领)",
            "logistics_type": "物流类型(0:其他，1:商家自配，2:美团配送，3:第三方配送，4:企客，5:跑腿)",
            "min_price": "最低起送价",
            "min_shipping_fee": "最低配送费",
            "is_super_poi": "是否外卖优质商家(0:否，1:是)",
            "online_time": "商家首次上线且营业的时间",
            "delivery_time": "门店每天配送时间",
            "business_interval": "门店每天营业时间窗口",
            "is_wcd_poi": "是否全城送商家(0:否，1:是)",
            "rest_type": "置休类型(0:普通置休，1:客服置休，2:心跳置休，3:高级置休)",
            "across_day": "跨天预订天数",
            "bulletin": "商家公告",
            "story_id": "商家故事ID",
            "lead_time": "商家备餐时间(秒)",
            "total_business_time": "累计营业时长",
            "meal_loss_percent": "餐损比例",
            "diagnose_score": "商家诊断分",
            "is_pinhf_poi": "是否拼好饭商家(0:否，1:是)",
            "is_cafeteria_poi": "是否食堂商家(0:否，1:是)",
            "aor_point_string": "商圈信息"
        }

        # 转换数据格式
        result = {}
        for row in data:  # 处理每一行数据
            # 确保行数据长度与列名长度相同
            if len(row) != len(columns):
                print(f"警告：数据行长度({len(row)})与列名长度({len(columns)})不匹配，跳过此行")
                skipped_rows += 1
                continue

            # 获取商家ID
            try:
                wm_poi_id = str(row[0]) if row[0] not in ("NULL", "") else None  # wm_poi_id 总是第一列
            except IndexError:
                print("错误：无法获取商家ID")
                skipped_rows += 1
                continue
            
            if not wm_poi_id:
                null_id_rows += 1
                continue
                
            success_rows += 1
            
            # 处理商家数据
            poi_data = {}
            for i, field in enumerate(columns):
                value = str(row[i]).strip() if row[i] is not None else None  # 确保值是字符串并去除空格
                
                # 处理NULL值和空字符串
                if value in ("NULL", ""):
                    value = None
                elif field in ("longitude", "latitude", "min_price", "min_shipping_fee", "lead_time", 
                             "total_business_time", "meal_loss_percent", "diagnose_score"):
                    try:
                        value = float(value) if value != "0" else 0
                    except (ValueError, TypeError):
                        value = None
                elif field in ("city_type", "city_level", "valid", "is_open_flag", "is_pic_flag", "poi_type", 
                             "ol_time", "brand_type", "brand_level", "brand_ka", "aor_type", "logistics_type", 
                             "is_super_poi", "is_wcd_poi", "rest_type", "story_id", "is_pinhf_poi", "is_cafeteria_poi"):
                    try:
                        value = int(value) if value != "0" else 0
                    except (ValueError, TypeError):
                        value = None
                
                poi_data[field] = {
                    "value": value,
                    "description": field_comments.get(field, f"未知字段: {field}")
                }
            
            result[wm_poi_id] = poi_data

        # 确保目录存在
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 保存到文件
        file_path = os.path.join(self.results_dir, filename)
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            print(f"\n数据处理统计:")
            print(f"- 总数据行数: {total_rows}")
            print(f"- 成功处理商家数: {success_rows}")
            print(f"- 跳过的行数（数据格式不正确）: {skipped_rows}")
            print(f"- 空ID的行数: {null_id_rows}")
            print(f"- 丢失的数据行数: {total_rows - success_rows - skipped_rows - null_id_rows}")
            print(f"\n结果已保存到: {file_path}")
            print(f"共处理 {len(data)} 条数据，成功保存 {len(result)} 条商家信息")
        except Exception as e:
            print(f"保存文件时出错: {str(e)}")

analyzer = PoiInfoAnalyzer()
async def main():
    """主函数"""
    analyzer = PoiInfoAnalyzer()
    
    # 获取商家数据
    
    poi_data = await analyzer.get_poi_data_from_hive(analyzer.poi_ids)
    
    
    if poi_data:
        # 保存原始数据
        print("\n保存原始数据...")
        analyzer.save_results(
            poi_data, 
            f'poi_dianping_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        )        
    else:
        print("\n无法获取商家数据，请检查错误信息")

if __name__ == "__main__":
    asyncio.run(main())
