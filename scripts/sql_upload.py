import requests
import argparse
import os
from tqdm import tqdm
import json

SERVER_URL = "https://xiaomeiai.meituan.com/weiwei/" # 具体的服务器url，如果更换服务器，请修改该URL
TEST_SERVER_URL = "http://xiaomeiai.cloud.test.sankuai.com/weiwei/"


def check_ret(ret):
    return type(ret) == int

# 仅能上传文本文件
def upload(file_path:str, output_log_path:str = './output_sql.txt', error_log_path:str = './error_log.txt', waiting_file:str = './waiting_file.txt', upload_target:str = 'ingredient_analysis', vector_name:str = "output.txt", debug:bool = False):
    content_map = {}
    try:
        with open(output_log_path, 'r', encoding='utf-8') as past_f:
            previous_content = past_f.readlines()
            for idx, line in tqdm(enumerate(previous_content), total=len(previous_content), desc="读取历史数据"):
                if line == "":
                    print(f"上传日志数据文件{output_log_path}, 第{idx}行数据为空")
                    continue
                food_name = line[line.find('|')+1:]
                global_id = line.split('|')[0]
                try:
                    global_id = int(global_id)
                except Exception as e:
                    print(f"上传日志数据文件{output_log_path}, 第{idx}行数据global_id转换失败， 错误信息为: {e}, 错误行数据为: {line}, 请排除问题后重新运行。")
                    return
                
                if food_name == '':
                    print(f'上传日志数据文件{output_log_path}, 第{idx}行数据食材名称为空， 请排除问题后重新运行。')
                    return
                content_map[food_name] = global_id

    except Exception as e:
        print(f"读取历史数据文件{output_log_path}失败， 错误信息为: {e}, 请注意，一开始是不包含这个文件的，如果没有，程序会自动创建该文件")
    
    
    print("历史运行数据加载完成")
    count = 0
    with open(error_log_path, 'a', encoding='utf-8') as error_f, open(output_log_path, 'a', encoding='utf-8') as output_f:
        if upload_target == 'ingredient_analysis':
            query_url = (SERVER_URL + 'ingredient/upsert') if debug == True else (TEST_SERVER_URL + 'ingredient/upsert')
            print(f"当前访问的URL为: {query_url}")
            error_f.write(f"错误信息为 ingredient_analysis 中的错误\n")
            file_list = os.listdir(file_path) # 获取文件夹下的所有文件
            print(f"读取到 {len(file_list)} 条数据")
            for idx, file in tqdm(enumerate(file_list), total=len(file_list), desc="上传数据"):
                if file.endswith('.txt') or file.endswith('.md'):
                    try:
                        food_name = file.split('.')[0].split('_')[0] # 获取食材名称
                    except Exception as e:
                        print(f"文件{file}获取食材名称失败, 拒绝该文件上传，请修改文件名后再上传")
                        error_f.write(f"文件{file}获取食材名称失败, 拒绝该文件上传，请修改文件名后再上传\n")
                        continue

                    if food_name in content_map: # 这个食物已经上传过了
                        error_f.write(f"文件{file}食材名称已存在于{upload_target}， 拒绝该文件上传\n")
                        continue
                    try:
                        file_dir = os.path.join(file_path, file)
                        with open(file_dir, 'r', encoding='utf-8') as f:
                            contents = f.readlines()
                            contents = '\n'.join(contents)
                            data = {
                                "name": food_name,
                                "data": contents,
                                "status": 1
                            }
                            sql_id = requests.post(query_url, json=data)
                            if sql_id.status_code == 200:
                                output_f.write(f"{sql_id}|{food_name}\n")
                                count += 1
                            else:
                                error_f.write(f"文件{file}上传失败, 错误信息为: {sql_id.text}\n")
                                continue
                    except Exception as e:
                        print(f"文件{file}读取或上传失败, 拒绝该文件上传， 错误信息为: {e}")
                        error_f.write(f"第{idx}条数据文件{file}读取或上传失败, 拒绝该文件上传， 错误信息为: {e}\n")
                        continue
                else:
                    print(f"文件{file}不是txt或md文件")
                    error_f.write(f"第{idx}条数据文件{file}不是txt或md文件\n")
                    continue
            print(f"上传成功{count}条数据")
        elif upload_target == 'vector_record':
            file_list = os.listdir(file_path) # 获取文件夹下的所有文件
            if vector_name in file_list and False: # 弃用，线上并没有这个接口
                print(f"文件{vector_name}存在, 直接读取该文件并上传")
                vector_name_dir = os.path.join(file_path, vector_name)
                with open(vector_name_dir, 'r', encoding='utf-8') as f:
                    txt_msg = f.readlines()
                    for idx, txt in tqdm(enumerate(txt_msg), total=len(txt_msg), desc="上传数据"):
                        if txt:
                            error_f.write(f"第{idx}行数据文件{vector_name}读取失败, 错误信息为: {txt}\n")
                            continue

                        try:
                            sep_pos = txt.find("|")
                        except Exception as e:
                            error_f.write(f"第{idx}行数据查找分割符'|'失败, 错误信息为: {e}, 错误行数据为: {txt}\n")
                            continue


                        try:
                            global_id = int(txt[:sep_pos])
                        except Exception as e:
                            error_f.write(f"第{idx}行数据转换global_id失败, 错误信息为: {e}, 错误行数据为: {txt}\n")
                            continue


                        try:
                            origin_content = txt[sep_pos+1:]
                        except Exception as e:
                            error_f.write(f"第{idx}行数据获取origin_content失败, 错误信息为: {e}, 错误行数据为: {txt}\n")
                            continue

                        if origin_content in content_map:
                            error_f.write(f"第{idx}行数据食材名称已存在于{upload_target}, 拒绝该文件上传\n")
                            continue

                        try:
                            data = {
                                "text"
                            }
                            global_id = vector_record.insert_vector(global_id, origin_content)
                            if check_ret(global_id):
                                output_f.write(f"{global_id}|{origin_content}\n")
                                count += 1
                        except Exception as e:
                            error_f.write(f"第{idx}行数据插入失败, 错误信息为: {e}, 错误行数据为: {txt}\n")
                            continue
                print(f"上传成功{count}条数据")
            else:
                # 找不到记录文件，说明要上传新文件
                print(f"找不到记录文件{vector_name}, 说明要上传新文件")
                query_url = TEST_SERVER_URL + 'vex/add' if debug else SERVER_URL + 'vex/add'
                print(f"当前访问的URL为: {query_url}")
                file_list = os.listdir(file_path)
                count = 0
                for file_name in file_list:
                    print(f"当前处理的文件为: {file_name}")
                    if file_name.endswith(".md") or file_name.endswith(".txt"):
                        print(f"当前文件 {file_name} 类型为markdown或txt文件，正在以纯文本的处理方式进行处理")
                        with open(os.path.join(file_path, file_name), 'r', encoding='utf-8') as f:
                            text_lines = f.readlines()
                            for lines, line in tqdm(enumerate(text_lines), total=len(text_lines), desc=f"处理文件 {file_name}进度"):
                                if line in content_map:
                                    continue # 如果该行内容已经上传，则跳过
                                # 去除行首尾的空白字符
                                line = line.strip()
                                # 如果行内容不为空，则进行请求
                                if line:
                                    json_data = {
                                        "text": line,
                                    }
                                    try:
                                        response = requests.post(query_url, json=json_data)
                                    except requests.exceptions.RequestException as e:
                                        error_f.write(f"关于文件{file_name} 第{lines}行: '{line}' 的request操作失败，存在未知错误，错误信息码为: {e}\n")
                                        continue
                                    if response.status_code == 200: # 返回正常
                                        response_data = response.json()
                                        if response_data.get("code") == 0:
                                            global_id = response_data.get("globalId")
                                            text = response_data.get("text")
                                            output_f.write(f"{global_id}|{text}\n")  
                                        elif response_data.get("code") == 400:
                                            # 返回400说明数据已经存在了
                                            error_f.write(f"关于 '{line}' 的操作失败，因为该数据已经存在于数据库中，错误信息码为: {response_data.get('code')}\n")
                                            continue
                                        else:
                                            error_f.write(f"关于 '{line}' 的操作失败，存在未知错误，错误信息码为: {response_data.get('code')}\n")
                                            continue
                                    else:
                                        error_f.write(f"操作失败 '{line}' 状态码为: {response.status_code}\n")
                                        continue
                    else:
                        print(f"当前文件 {file_name} 类型不支持，目前支持markdown/txt和json文件")
                        continue
            print(f"文件上传完成，共上传{count}条数据")
        else:
            print(f"upload_target 错误，请检查")
            error_f.write(f"upload_target 错误，请检查\n")  

def Search_Data(content:str, base_url:str, type:str, topk:int, output_file_path:str = './output.json', error_log_path:str = './error_log.txt'):
    assert type == 'search'
    with open(error_log_path, 'w', encoding='utf-8') as error_log:
        query_url = f"{base_url}" + type
        print(f"当前访问的URL为: {query_url}")
        json_data = {
            "text": content,
            "topk": topk
        }
        try:
            response = requests.post(query_url, json=json_data)
        except requests.exceptions.RequestException as e:
            error_log.write(f"查询失败，存在未知错误，错误信息码为: {e}\n")
            return
        if response.status_code == 200:
            response_data = response.json()
            # 将字典转换为列表，并按'score'排序
            sorted_response_data = sorted(response_data.items(), key=lambda item: item[1]['score'], reverse=True)
            
            print(f"查询成功，返回结果为: {sorted_response_data}")
            # 将结果写入output_file_path作为JSON
            with open(output_file_path, 'w', encoding='utf-8') as output_file:
                json.dump(sorted_response_data, output_file, ensure_ascii=False, indent=4)
        else:
            error_log.write(f"查询失败，状态码为: {response.status_code}\n")

def parse_arguments():
    parser = argparse.ArgumentParser(description='Process some integers.')
    parser.add_argument('--type', type=str, help='选择进行操作的类型，目前支持: add_merchant, add_ingredient和search， add_merchant', default='add_merchant')
    parser.add_argument('--file_path', type=str, help='所需要上传文件的文件夹路径，程序会自动扫描该文件夹下所有的文件，然后以行为单位上传，默认为./data', default="./data")
    parser.add_argument('--content', type=str, help='所需要查询的内容，默认为空', default="")
    parser.add_argument('--topk', type=int, help='所需要查询的topk结果，默认为10', default=10)
    parser.add_argument('--output_file_path', type=str, help='输出文件的路径，默认为./output.txt', default="./output.txt")
    parser.add_argument('--error_log_path', type=str, help='错误日志文件的路径，默认为./error_log.txt', default="./error_log.txt")
    parser.add_argument('--waiting_file', type=str, help='等待上传的文件的路径，默认为./waiting_file.txt', default="./waiting_file.txt")
    parser.add_argument('--vector_name', type=str, help='曾经上传过的向量文件日志的名称，默认为output.txt', default="output.txt")
    parser.add_argument('--debug', type=bool, help='是否开启debug模式，默认为False', default=False)
    return parser.parse_args()

def main():
    args = parse_arguments()
    file_path = args.file_path
    # 判断文件路径是否存在，如果不存在则创建
    if not os.path.exists(file_path):
        print(f"文件路径 '{file_path}' 不存在，正在创建...")
        os.makedirs(file_path)
        return
    if args.type == 'add_merchant':
        # 使用文件路径、基础URL和输出文件路径调用函数
        upload(file_path, args.output_file_path, args.error_log_path, args.waiting_file, "vector_record", args.vector_name, args.debug)
    elif args.type == 'add_ingredient':
        upload(file_path, args.output_file_path, args.error_log_path, args.waiting_file, "ingredient_analysis", args.vector_name, args.debug)
    elif args.type == 'search':
        if not args.output_file_path.endswith('.json'):
            change_path = os.path.splitext(args.output_file_path)[0] + '.json'
            print(f"输出文件路径 '{args.output_file_path}' 不是以 '.json' 结尾，正在自动更改...，修改为: {change_path}")
            args.output_file_path = change_path
        # 使用文件路径、基础URL和输出文件路径调用函数
        Search_Data(args.content, TEST_SERVER_URL if args.debug else SERVER_URL, args.type, args.topk, args.output_file_path, args.error_log_path)
    else:
        print(f"当前操作类型 '{args.type}' 不支持，请检查输入")

if __name__ == "__main__":
    main()
    # sql = text("SELECT * FROM ingredient_analysis WHERE '测试测试测试测试测试测试' LIKE CONCAT('%', ingredient, '%') LIMIT 2")
    # conn = sql_client.CLIENT.connect()
    # insight_list = conn.execute(sql).fetchall()
    # print(insight_list)
    # print(type(insight_list))
    # conn.commit()
    # conn.close()