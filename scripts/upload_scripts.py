import os
import requests
import concurrent.futures

# 定义请求的URL
url = "http://127.0.0.1:8080/weiwei/ingredient/es-upsert"

# 读取目录下的所有.md文件，使用生成器逐行返回数据
def read_md_files_with_name(directory):
    uploaded_names = set()
    uploaded_data = set()
    if os.path.exists("analysis_upload_log_test.txt"):
        with open("analysis_upload_log_test.txt", "r", encoding="utf-8") as log_file:
            for line in log_file:
                name, data = line.strip().split('@', 1)
                uploaded_names.add(name)
                uploaded_data.add(data)

    for idx, filename in enumerate(os.listdir(directory)):
        if filename.endswith(".md"):
            # 从文件名中提取食材名称
            ingredient_name = filename.split('_')[0] if '_' in filename else filename.split('.')[0]
            with open(os.path.join(directory, filename), 'r', encoding='utf-8') as file:
                content = file.read().strip()

                if content:  # 确保内容不为空
                    name = ingredient_name
                    data = {
                        "name": name,
                        "data": content,
                        "status": 1
                    }
                    yield data

# 发送POST请求
def send_post_request(data):
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            # 如果上传成功，写入日志
            with open("analysis_upload_log_test.txt", "a", encoding="utf-8") as log_file:
                log_file.write(f"{data['name']}@ttt\n")
        return response.status_code, response.json()
    except Exception as e:
        return None, str(e)

# 主函数
def main():
    # directory = "/Users/<USER>/Desktop/meituan/xiaomei/xiaomei-weiwei/data/knowledge_v3"
    directory = "/Users/<USER>/Desktop/meituan/xiaomei/xiaomei-weiwei/data/knowledge_ingredient_20250210"
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(send_post_request, data) for data in read_md_files_with_name(directory)]
        for future in concurrent.futures.as_completed(futures):
            status_code, result = future.result()
            # print(f"Status Code: {status_code}, Result: {result}")

if __name__ == "__main__":
    main() 