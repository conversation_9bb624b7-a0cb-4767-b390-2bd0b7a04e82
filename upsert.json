{
    "index_name": "maindb_v1",
    "doc_id": "test0",
    "doc": {
        "doc_as_upsert": true,
        "doc":{
            "poi_name.keyword": "test0",
            "poi_name.text": "test0",
            "food_name.keyword": "test0",
            "food_name.text": "test0",
            "poi_id": "11111",
            "spu_id": "22222",
            "food_id": "33333",
            "order_num": -100,
            "validation": false,
            "position.longtitude": 114.31,
            "position.latitude": 30.52,
            "extra":"test information"
        }
    }
}


{
    "index_name": "maindb_v1",
    "query": {
        "bool": {
            "must": [
                {
                    "terms": {
                        "poi_id": "11111"
                    }
                },
                {
                    "terms": {
                        "spu_id": "22222"
                    }
                }
            ]
        }
    }
}

{
    "index_name":"maindb_v1",
    "query":{
        "query":{
            "bool":{
                "must":[
                    {"match":{"poi_id": "11111"}
                    },
                    {
                        "term":{"spu_id": "22222"}
                    }
                ]
            }
        },
        "size":1
    }
}